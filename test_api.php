<?php
/**
 * API Testing Script for Flori Construction Ltd Mobile App
 * This script tests all the mobile API endpoints
 */

// Base URL for API testing
$baseUrl = 'http://localhost/erdevwe/api';

// Test credentials (replace with actual admin credentials)
$testCredentials = [
    'username' => 'admin',
    'password' => 'admin123'
];

// Function to make HTTP requests
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    // Set headers
    $defaultHeaders = ['Content-Type: application/json'];
    $allHeaders = array_merge($defaultHeaders, $headers);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
    
    // Set method and data
    switch (strtoupper($method)) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PUT':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

// Function to print test results
function printTestResult($testName, $result, $expectedCode = 200) {
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "TEST: $testName\n";
    echo str_repeat("=", 50) . "\n";
    
    if ($result['error']) {
        echo "❌ CURL ERROR: " . $result['error'] . "\n";
        return false;
    }
    
    echo "HTTP Code: " . $result['http_code'] . "\n";
    
    if ($result['http_code'] == $expectedCode) {
        echo "✅ HTTP Status: PASS\n";
    } else {
        echo "❌ HTTP Status: FAIL (Expected: $expectedCode, Got: " . $result['http_code'] . ")\n";
    }
    
    echo "Response:\n";
    $decodedResponse = json_decode($result['response'], true);
    if ($decodedResponse) {
        echo json_encode($decodedResponse, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo $result['response'] . "\n";
    }
    
    return $result['http_code'] == $expectedCode;
}

// Start testing
echo "🚀 Starting API Tests for Flori Construction Ltd Mobile App\n";
echo "Base URL: $baseUrl\n";

$authToken = '';

// Test 1: Authentication - Login
echo "\n📱 Testing Authentication Endpoints...\n";

$loginResult = makeRequest(
    "$baseUrl/auth.php",
    'POST',
    $testCredentials
);

$loginSuccess = printTestResult("User Login", $loginResult);

if ($loginSuccess) {
    $loginData = json_decode($loginResult['response'], true);
    if (isset($loginData['token'])) {
        $authToken = $loginData['token'];
        echo "🔑 Auth token obtained: " . substr($authToken, 0, 20) . "...\n";
    }
}

// Test 2: Token Verification
if ($authToken) {
    $verifyResult = makeRequest(
        "$baseUrl/auth.php?action=verify",
        'GET',
        null,
        ["Authorization: Bearer $authToken"]
    );
    
    printTestResult("Token Verification", $verifyResult);
}

// Test 3: Mobile API - Dashboard
echo "\n📊 Testing Mobile API Endpoints...\n";

if ($authToken) {
    $dashboardResult = makeRequest(
        "$baseUrl/mobile.php?action=dashboard",
        'GET',
        null,
        ["Authorization: Bearer $authToken"]
    );
    
    printTestResult("Dashboard Data", $dashboardResult);
}

// Test 4: Projects List
if ($authToken) {
    $projectsResult = makeRequest(
        "$baseUrl/mobile.php?action=projects&page=1&limit=5",
        'GET',
        null,
        ["Authorization: Bearer $authToken"]
    );
    
    printTestResult("Projects List", $projectsResult);
}

// Test 5: Media List
if ($authToken) {
    $mediaResult = makeRequest(
        "$baseUrl/mobile.php?action=media&page=1&limit=5",
        'GET',
        null,
        ["Authorization: Bearer $authToken"]
    );
    
    printTestResult("Media List", $mediaResult);
}

// Test 6: Create Project
if ($authToken) {
    $newProject = [
        'title' => 'Test Mobile Project',
        'description' => 'This is a test project created via mobile API',
        'short_description' => 'Test project for mobile app',
        'location' => 'Test Location',
        'project_type' => 'completed',
        'client_name' => 'Test Client',
        'is_featured' => false
    ];
    
    $createResult = makeRequest(
        "$baseUrl/mobile.php?action=project",
        'POST',
        $newProject,
        ["Authorization: Bearer $authToken"]
    );
    
    printTestResult("Create Project", $createResult, 200);
}

// Test 7: File Upload Test (simulate)
echo "\n📁 Testing File Upload Capability...\n";

if ($authToken) {
    // Note: This is a simplified test. Actual file upload would require multipart/form-data
    echo "📝 File upload test requires actual file data and multipart/form-data.\n";
    echo "   The upload endpoint is available at: $baseUrl/mobile.php?action=upload\n";
    echo "   Required: POST with multipart/form-data containing 'file' field\n";
}

// Test 8: Database Connection Test
echo "\n🗄️ Testing Database Connection...\n";

try {
    require_once 'config/config.php';
    
    if (isset($db)) {
        echo "✅ Database connection: SUCCESS\n";
        
        // Test basic queries
        $projectCount = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1");
        echo "📊 Active projects in database: " . ($projectCount['count'] ?? 0) . "\n";
        
        $mediaCount = $db->fetchOne("SELECT COUNT(*) as count FROM media WHERE is_active = 1");
        echo "📸 Active media files in database: " . ($mediaCount['count'] ?? 0) . "\n";
        
    } else {
        echo "❌ Database connection: FAILED\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}

// Test 9: File System Permissions
echo "\n📂 Testing File System Permissions...\n";

$uploadDir = 'uploads';
if (!is_dir($uploadDir)) {
    if (mkdir($uploadDir, 0755, true)) {
        echo "✅ Upload directory created: $uploadDir\n";
    } else {
        echo "❌ Failed to create upload directory: $uploadDir\n";
    }
} else {
    echo "✅ Upload directory exists: $uploadDir\n";
}

if (is_writable($uploadDir)) {
    echo "✅ Upload directory is writable\n";
} else {
    echo "❌ Upload directory is not writable\n";
}

// Test 10: API Response Format Validation
echo "\n🔍 Testing API Response Format...\n";

if ($authToken) {
    $testResult = makeRequest(
        "$baseUrl/mobile.php?action=dashboard",
        'GET',
        null,
        ["Authorization: Bearer $authToken"]
    );
    
    $responseData = json_decode($testResult['response'], true);
    
    if ($responseData) {
        echo "✅ JSON Response: Valid\n";
        
        // Check required fields
        $requiredFields = ['success'];
        $hasAllFields = true;
        
        foreach ($requiredFields as $field) {
            if (!isset($responseData[$field])) {
                echo "❌ Missing required field: $field\n";
                $hasAllFields = false;
            }
        }
        
        if ($hasAllFields) {
            echo "✅ Response structure: Valid\n";
        }
        
    } else {
        echo "❌ JSON Response: Invalid\n";
    }
}

// Summary
echo "\n" . str_repeat("=", 60) . "\n";
echo "🏁 API TESTING COMPLETE\n";
echo str_repeat("=", 60) . "\n";

echo "📋 Test Summary:\n";
echo "• Authentication: " . ($loginSuccess ? "✅ PASS" : "❌ FAIL") . "\n";
echo "• Database Connection: Available for manual verification\n";
echo "• File Permissions: Available for manual verification\n";
echo "• API Endpoints: Available for manual verification\n";

echo "\n📱 Next Steps:\n";
echo "1. Verify all tests pass\n";
echo "2. Test file upload with actual files\n";
echo "3. Build and test Android app\n";
echo "4. Deploy to production environment\n";

echo "\n🔧 Troubleshooting:\n";
echo "• If authentication fails, check admin credentials\n";
echo "• If database errors occur, verify config/config.php\n";
echo "• If file upload fails, check directory permissions\n";
echo "• If API errors occur, check error logs\n";

?>
