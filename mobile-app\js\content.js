/**
 * Content Manager for Flori Construction Mobile App
 * Handles content management for pages, services, and testimonials
 */

class ContentManager {
    constructor() {
        this.content = {};
        this.services = [];
        this.testimonials = [];
        this.currentSection = 'pages';
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Content section tabs
        const sectionTabs = document.querySelectorAll('.content-tab');
        sectionTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const section = tab.dataset.section;
                this.switchSection(section);
            });
        });
    }
    
    async load() {
        try {
            await this.loadContent();
            this.renderContent();
        } catch (error) {
            console.error('Failed to load content:', error);
            window.floriAdmin.showToast('Failed to load content', 'error');
        }
    }
    
    async loadContent() {
        const [contentResponse, servicesResponse, testimonialsResponse] = await Promise.all([
            window.floriAdmin.apiRequest('content.php'),
            window.floriAdmin.apiRequest('services.php'),
            window.floriAdmin.apiRequest('testimonials.php')
        ]);
        
        if (contentResponse && contentResponse.success) {
            this.content = contentResponse.content || {};
        }
        
        if (servicesResponse && servicesResponse.success) {
            this.services = servicesResponse.services || [];
        }
        
        if (testimonialsResponse && testimonialsResponse.success) {
            this.testimonials = testimonialsResponse.testimonials || [];
        }
    }
    
    renderContent() {
        const container = document.getElementById('content-sections');
        if (!container) return;
        
        const html = `
            <div class="content-tabs">
                <button class="content-tab active" data-section="pages">
                    <i class="fas fa-file-alt"></i> Pages
                </button>
                <button class="content-tab" data-section="services">
                    <i class="fas fa-cogs"></i> Services
                </button>
                <button class="content-tab" data-section="testimonials">
                    <i class="fas fa-quote-left"></i> Testimonials
                </button>
            </div>
            
            <div class="content-body">
                <div id="pages-section" class="content-section active">
                    ${this.renderPagesSection()}
                </div>
                
                <div id="services-section" class="content-section">
                    ${this.renderServicesSection()}
                </div>
                
                <div id="testimonials-section" class="content-section">
                    ${this.renderTestimonialsSection()}
                </div>
            </div>
        `;
        
        container.innerHTML = html;
        this.setupContentEventListeners();
    }
    
    renderPagesSection() {
        const pages = [
            { key: 'home', title: 'Home Page', icon: 'fas fa-home' },
            { key: 'about', title: 'About Page', icon: 'fas fa-info-circle' },
            { key: 'contact', title: 'Contact Page', icon: 'fas fa-envelope' }
        ];
        
        return `
            <div class="section-header">
                <h3>Page Content</h3>
                <p>Manage content for your website pages</p>
            </div>
            
            <div class="content-cards">
                ${pages.map(page => `
                    <div class="content-card" data-page="${page.key}">
                        <div class="card-icon">
                            <i class="${page.icon}"></i>
                        </div>
                        <div class="card-content">
                            <h4>${page.title}</h4>
                            <p>Edit ${page.title.toLowerCase()} content and settings</p>
                        </div>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-primary" onclick="window.ContentManager.editPage('${page.key}')">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }
    
    renderServicesSection() {
        return `
            <div class="section-header">
                <h3>Services</h3>
                <button class="btn btn-primary" onclick="window.ContentManager.addService()">
                    <i class="fas fa-plus"></i> Add Service
                </button>
            </div>
            
            <div class="services-list">
                ${this.services.length === 0 ? 
                    '<div class="empty-state"><p>No services found. Add your first service.</p></div>' :
                    this.services.map(service => `
                        <div class="service-item" data-id="${service.id}">
                            <div class="service-image">
                                ${service.featured_image ? 
                                    `<img src="../${service.featured_image}" alt="${window.floriAdmin.escapeHtml(service.title)}">` :
                                    '<div class="placeholder"><i class="fas fa-cog"></i></div>'
                                }
                            </div>
                            <div class="service-content">
                                <h4>${window.floriAdmin.escapeHtml(service.title)}</h4>
                                <p>${window.floriAdmin.escapeHtml(service.short_description || '').substring(0, 100)}...</p>
                                <div class="service-meta">
                                    <span class="service-status ${service.is_active ? 'active' : 'inactive'}">
                                        ${service.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                            <div class="service-actions">
                                <button class="btn btn-sm btn-ghost" onclick="window.ContentManager.editService(${service.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="window.ContentManager.deleteService(${service.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')
                }
            </div>
        `;
    }
    
    renderTestimonialsSection() {
        return `
            <div class="section-header">
                <h3>Testimonials</h3>
                <button class="btn btn-primary" onclick="window.ContentManager.addTestimonial()">
                    <i class="fas fa-plus"></i> Add Testimonial
                </button>
            </div>
            
            <div class="testimonials-list">
                ${this.testimonials.length === 0 ? 
                    '<div class="empty-state"><p>No testimonials found. Add your first testimonial.</p></div>' :
                    this.testimonials.map(testimonial => `
                        <div class="testimonial-item" data-id="${testimonial.id}">
                            <div class="testimonial-content">
                                <div class="testimonial-text">
                                    <p>"${window.floriAdmin.escapeHtml(testimonial.content.substring(0, 150))}..."</p>
                                </div>
                                <div class="testimonial-author">
                                    <strong>${window.floriAdmin.escapeHtml(testimonial.client_name)}</strong>
                                    ${testimonial.company ? `<span>${window.floriAdmin.escapeHtml(testimonial.company)}</span>` : ''}
                                </div>
                                <div class="testimonial-meta">
                                    <span class="rating">
                                        ${this.renderStars(testimonial.rating || 5)}
                                    </span>
                                    <span class="status ${testimonial.is_approved ? 'approved' : 'pending'}">
                                        ${testimonial.is_approved ? 'Approved' : 'Pending'}
                                    </span>
                                </div>
                            </div>
                            <div class="testimonial-actions">
                                <button class="btn btn-sm btn-ghost" onclick="window.ContentManager.editTestimonial(${testimonial.id})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="window.ContentManager.deleteTestimonial(${testimonial.id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('')
                }
            </div>
        `;
    }
    
    renderStars(rating) {
        const stars = [];
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars.push('<i class="fas fa-star"></i>');
            } else {
                stars.push('<i class="far fa-star"></i>');
            }
        }
        return stars.join('');
    }
    
    setupContentEventListeners() {
        // Content tabs
        const tabs = document.querySelectorAll('.content-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const section = tab.dataset.section;
                this.switchSection(section);
            });
        });
    }
    
    switchSection(section) {
        // Update active tab
        document.querySelectorAll('.content-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');
        
        // Update active section
        document.querySelectorAll('.content-section').forEach(sec => {
            sec.classList.remove('active');
        });
        document.getElementById(`${section}-section`).classList.add('active');
        
        this.currentSection = section;
    }
    
    editPage(pageKey) {
        const pageContent = this.content[pageKey] || {};
        
        const modalContent = `
            <div class="modal-header">
                <h2>Edit ${pageKey.charAt(0).toUpperCase() + pageKey.slice(1)} Page</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="edit-page-form" class="modal-form">
                <input type="hidden" name="page_key" value="${pageKey}">
                
                <div class="form-group">
                    <label for="page-title">Page Title</label>
                    <input type="text" id="page-title" name="title" value="${window.floriAdmin.escapeHtml(pageContent.title || '')}">
                </div>
                
                <div class="form-group">
                    <label for="page-content">Page Content</label>
                    <textarea id="page-content" name="content" rows="10">${window.floriAdmin.escapeHtml(pageContent.content || '')}</textarea>
                </div>
                
                <div class="form-group">
                    <label for="meta-title">Meta Title</label>
                    <input type="text" id="meta-title" name="meta_title" value="${window.floriAdmin.escapeHtml(pageContent.meta_title || '')}">
                </div>
                
                <div class="form-group">
                    <label for="meta-description">Meta Description</label>
                    <textarea id="meta-description" name="meta_description" rows="3">${window.floriAdmin.escapeHtml(pageContent.meta_description || '')}</textarea>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Changes
                    </button>
                </div>
            </form>
        `;
        
        window.floriAdmin.showModal(modalContent);
        
        document.getElementById('edit-page-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePage();
        });
    }
    
    async savePage() {
        const form = document.getElementById('edit-page-form');
        const formData = new FormData(form);
        
        const pageData = {
            page_key: formData.get('page_key'),
            title: formData.get('title'),
            content: formData.get('content'),
            meta_title: formData.get('meta_title'),
            meta_description: formData.get('meta_description')
        };
        
        try {
            const data = await window.floriAdmin.apiRequest('mobile.php?action=content', {
                method: 'PUT',
                body: JSON.stringify(pageData)
            });
            
            if (data && data.success) {
                window.floriAdmin.closeModal();
                window.floriAdmin.showToast('Page updated successfully', 'success');
                this.load();
            } else {
                window.floriAdmin.showToast(data?.error || 'Failed to update page', 'error');
            }
        } catch (error) {
            console.error('Failed to save page:', error);
            window.floriAdmin.showToast('Failed to update page', 'error');
        }
    }
    
    addService() {
        // Implementation for adding new service
        console.log('Add service');
    }
    
    editService(serviceId) {
        // Implementation for editing service
        console.log('Edit service:', serviceId);
    }
    
    deleteService(serviceId) {
        // Implementation for deleting service
        console.log('Delete service:', serviceId);
    }
    
    addTestimonial() {
        // Implementation for adding new testimonial
        console.log('Add testimonial');
    }
    
    editTestimonial(testimonialId) {
        // Implementation for editing testimonial
        console.log('Edit testimonial:', testimonialId);
    }
    
    deleteTestimonial(testimonialId) {
        // Implementation for deleting testimonial
        console.log('Delete testimonial:', testimonialId);
    }
}

// Initialize Content Manager
window.ContentManager = new ContentManager();
