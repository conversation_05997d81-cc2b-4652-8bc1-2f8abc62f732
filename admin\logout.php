<?php
/**
 * Admin Logout Script for Flori Construction Ltd
 */

require_once '../config/config.php';

// Get user info before destroying session
$user_name = $_SESSION['full_name'] ?? $_SESSION['username'] ?? 'User';
$logout_time = date('Y-m-d H:i:s');

// Clear remember me cookie if it exists
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/', '', true, true);
}

// Destroy session
session_destroy();

// Clear session cookie
if (isset($_COOKIE[session_name()])) {
    setcookie(session_name(), '', time() - 3600, '/');
}

// Check if it's an AJAX request for instant logout
if (isset($_GET['instant']) && $_GET['instant'] == '1') {
    header('Location: login.php?logged_out=1');
    exit;
}

// Show logout confirmation page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logging Out - <?= SITE_NAME ?></title>
    <meta name="description" content="Secure logout from <?= SITE_NAME ?> admin panel">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin-login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .logout-container {
            position: relative;
            z-index: 10;
            width: 100%;
            max-width: 450px;
            padding: var(--spacing-lg);
        }

        .logout-card {
            background: var(--white);
            border-radius: var(--border-radius-2xl);
            box-shadow: var(--shadow-xl);
            padding: var(--spacing-3xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .logout-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--success-color), var(--primary-color), var(--info-color));
        }

        .logout-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--success-color), #2ecc71);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: var(--font-size-2xl);
            margin: 0 auto var(--spacing-xl);
            animation: checkmark 0.6s ease-in-out;
        }

        @keyframes checkmark {
            0% {
                transform: scale(0);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }

        .logout-message h1 {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--gray-800);
            margin-bottom: var(--spacing-sm);
        }

        .logout-message p {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin-bottom: var(--spacing-xl);
            line-height: var(--line-height-relaxed);
        }

        .logout-info {
            background: linear-gradient(135deg, var(--gray-50), var(--white));
            border: 1px solid var(--gray-200);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            text-align: left;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            font-size: var(--font-size-sm);
        }

        .info-label {
            color: var(--gray-600);
            font-weight: var(--font-weight-medium);
        }

        .info-value {
            color: var(--gray-800);
            font-weight: var(--font-weight-semibold);
        }

        .logout-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .redirect-notice {
            font-size: var(--font-size-sm);
            color: var(--gray-500);
            margin-top: var(--spacing-lg);
        }

        .countdown {
            font-weight: var(--font-weight-bold);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-icon">
                <i class="fas fa-check"></i>
            </div>

            <div class="logout-message">
                <h1>Successfully Logged Out</h1>
                <p>Thank you for using the admin panel. Your session has been securely terminated.</p>
            </div>

            <div class="logout-info">
                <div class="info-item">
                    <span class="info-label">User:</span>
                    <span class="info-value"><?= htmlspecialchars($user_name) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Logout Time:</span>
                    <span class="info-value"><?= date('M j, Y \a\t g:i A', strtotime($logout_time)) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Session:</span>
                    <span class="info-value">Terminated</span>
                </div>
            </div>

            <div class="logout-actions">
                <a href="login.php" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    <span>Sign In Again</span>
                </a>

                <a href="../index.php" class="back-link">
                    <i class="fas fa-home"></i>
                    <span>Back to Website</span>
                </a>
            </div>

            <div class="redirect-notice">
                <p>Redirecting to login page in <span class="countdown" id="countdown">5</span> seconds...</p>
            </div>
        </div>
    </div>

    <!-- Background Animation -->
    <div class="background-animation">
        <div class="construction-icons">
            <i class="fas fa-hard-hat"></i>
            <i class="fas fa-hammer"></i>
            <i class="fas fa-tools"></i>
            <i class="fas fa-building"></i>
            <i class="fas fa-truck"></i>
            <i class="fas fa-drafting-compass"></i>
            <i class="fas fa-ruler-combined"></i>
            <i class="fas fa-wrench"></i>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Countdown timer
            let countdown = 5;
            const countdownElement = document.getElementById('countdown');

            const timer = setInterval(function() {
                countdown--;
                countdownElement.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(timer);
                    window.location.href = 'login.php?logged_out=1';
                }
            }, 1000);

            // Animate construction icons
            const icons = document.querySelectorAll('.construction-icons i');
            icons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.8}s`;
                icon.style.animationDuration = `${4 + (index * 0.5)}s`;
            });

            // Add click handlers to stop countdown
            const links = document.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', function() {
                    clearInterval(timer);
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Enter or Space to go to login
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    clearInterval(timer);
                    window.location.href = 'login.php?logged_out=1';
                }

                // Escape to go to website
                if (e.key === 'Escape') {
                    e.preventDefault();
                    clearInterval(timer);
                    window.location.href = '../index.php';
                }
            });
        });
    </script>
</body>
</html>
