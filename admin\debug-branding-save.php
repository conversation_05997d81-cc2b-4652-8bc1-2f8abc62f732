<?php
/**
 * Debug Branding Save Functionality
 * This script helps debug why branding settings are not saving
 */

require_once '../config/config.php';

// Check if user is logged in
requireLogin();

echo "<!DOCTYPE html>";
echo "<html><head><title>Debug Branding Save</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
    pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
</style></head><body>";

echo "<h1>🔍 Branding Save Debug Tool</h1>";

try {
    // Test 1: Check database connection
    echo "<div class='test-section'>";
    echo "<h2>Test 1: Database Connection</h2>";
    
    if ($db) {
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test basic query
        $result = $db->fetchOne("SELECT 1 as test");
        if ($result) {
            echo "<div class='success'>✅ Database queries working</div>";
        } else {
            echo "<div class='error'>❌ Database queries not working</div>";
        }
    } else {
        echo "<div class='error'>❌ Database connection failed</div>";
    }
    echo "</div>";
    
    // Test 2: Check site_settings table
    echo "<div class='test-section'>";
    echo "<h2>Test 2: Site Settings Table</h2>";
    
    try {
        $tableExists = $db->fetchOne("SHOW TABLES LIKE 'site_settings'");
        if ($tableExists) {
            echo "<div class='success'>✅ site_settings table exists</div>";
            
            // Check table structure
            $columns = $db->fetchAll("SHOW COLUMNS FROM site_settings");
            echo "<div class='info'>Table structure:</div>";
            echo "<pre>";
            foreach ($columns as $column) {
                echo $column['Field'] . " - " . $column['Type'] . " - " . $column['Null'] . " - " . $column['Key'] . "\n";
            }
            echo "</pre>";
            
            // Check existing data
            $existingData = $db->fetchAll("SELECT setting_key, LEFT(setting_value, 100) as preview FROM site_settings");
            echo "<div class='info'>Existing settings:</div>";
            echo "<pre>";
            foreach ($existingData as $row) {
                echo $row['setting_key'] . ": " . $row['preview'] . "...\n";
            }
            echo "</pre>";
            
        } else {
            echo "<div class='error'>❌ site_settings table does not exist</div>";
            echo "<div class='info'>Run database/update-schema.php to create the table</div>";
        }
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error checking table: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // Test 3: Test INSERT operation
    echo "<div class='test-section'>";
    echo "<h2>Test 3: Test INSERT Operation</h2>";
    
    try {
        $testKey = 'debug_test_' . time();
        $testData = [
            'test_color' => '#ff0000',
            'test_time' => date('Y-m-d H:i:s')
        ];
        
        $insertId = $db->insert('site_settings', [
            'setting_key' => $testKey,
            'setting_value' => json_encode($testData),
            'created_at' => date('Y-m-d H:i:s')
        ]);
        
        echo "<div class='success'>✅ INSERT operation successful (ID: $insertId)</div>";
        
        // Verify the insert
        $inserted = $db->fetchOne("SELECT * FROM site_settings WHERE id = ?", [$insertId]);
        if ($inserted) {
            echo "<div class='success'>✅ Data verified in database</div>";
            echo "<pre>" . print_r($inserted, true) . "</pre>";
            
            // Clean up test data
            $db->query("DELETE FROM site_settings WHERE id = ?", [$insertId]);
            echo "<div class='info'>Test data cleaned up</div>";
        } else {
            echo "<div class='error'>❌ Could not verify inserted data</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ INSERT operation failed: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // Test 4: Test UPDATE operation
    echo "<div class='test-section'>";
    echo "<h2>Test 4: Test UPDATE Operation</h2>";
    
    try {
        // Check if branding_colors exists
        $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'branding_colors'");
        
        if ($existing) {
            echo "<div class='info'>Found existing branding_colors record (ID: " . $existing['id'] . ")</div>";
            
            $testColors = [
                'primary_color' => '#ff0000',
                'secondary_color' => '#00ff00',
                'test_update' => date('Y-m-d H:i:s')
            ];
            
            $result = $db->update('site_settings',
                ['setting_value' => json_encode($testColors), 'updated_at' => date('Y-m-d H:i:s')],
                'setting_key = ?',
                ['branding_colors']
            );
            
            echo "<div class='success'>✅ UPDATE operation successful</div>";
            
            // Verify the update
            $updated = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_colors'");
            if ($updated) {
                echo "<div class='success'>✅ Update verified in database</div>";
                echo "<pre>" . $updated['setting_value'] . "</pre>";
            }
            
        } else {
            echo "<div class='info'>No existing branding_colors record found, testing INSERT instead</div>";
            
            $testColors = [
                'primary_color' => '#e74c3c',
                'secondary_color' => '#2c3e50',
                'accent_color' => '#f39c12',
                'text_color' => '#333333',
                'background_color' => '#ffffff'
            ];
            
            $insertId = $db->insert('site_settings', [
                'setting_key' => 'branding_colors',
                'setting_value' => json_encode($testColors),
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            echo "<div class='success'>✅ INSERT operation successful (ID: $insertId)</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ UPDATE operation failed: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // Test 5: Test CSRF functions
    echo "<div class='test-section'>";
    echo "<h2>Test 5: CSRF Protection</h2>";
    
    if (function_exists('getCSRFField')) {
        echo "<div class='success'>✅ getCSRFField function exists</div>";
        $csrfField = getCSRFField();
        echo "<div class='info'>CSRF field: " . htmlspecialchars($csrfField) . "</div>";
    } else {
        echo "<div class='error'>❌ getCSRFField function not found</div>";
    }
    
    if (function_exists('requireCSRF')) {
        echo "<div class='success'>✅ requireCSRF function exists</div>";
    } else {
        echo "<div class='error'>❌ requireCSRF function not found</div>";
    }
    echo "</div>";
    
    // Test 6: Test sanitize function
    echo "<div class='test-section'>";
    echo "<h2>Test 6: Sanitize Function</h2>";
    
    if (function_exists('sanitize')) {
        echo "<div class='success'>✅ sanitize function exists</div>";
        $testInput = "<script>alert('test')</script>";
        $sanitized = sanitize($testInput);
        echo "<div class='info'>Test input: " . htmlspecialchars($testInput) . "</div>";
        echo "<div class='info'>Sanitized: " . htmlspecialchars($sanitized) . "</div>";
    } else {
        echo "<div class='error'>❌ sanitize function not found</div>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Fatal error: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Stack trace:</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<div style='margin-top: 30px; text-align: center;'>";
echo "<a href='branding.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Branding Settings</a>";
echo "</div>";

echo "</body></html>";
?>
