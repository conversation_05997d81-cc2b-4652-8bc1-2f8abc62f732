<?php
require_once '../config/config.php';
require_once '../config/database.php';

requireLogin();

$db = new Database();

// Get date range from query params
$startDate = $_GET['start_date'] ?? date('Y-m-01'); // First day of current month
$endDate = $_GET['end_date'] ?? date('Y-m-d'); // Today

// Analytics data
$analytics = [
    'projects' => [
        'total' => $db->fetchOne("SELECT COUNT(*) as count FROM projects")['count'],
        'active' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE project_type = 'ongoing'")['count'],
        'completed' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE project_type = 'completed'")['count'],
        'recent' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)")['count']
    ],
    'media' => [
        'total' => $db->fetchOne("SELECT COUNT(*) as count FROM media")['count'],
        'images' => $db->fetchOne("SELECT COUNT(*) as count FROM media WHERE file_type LIKE 'image%'")['count'],
        'videos' => $db->fetchOne("SELECT COUNT(*) as count FROM media WHERE file_type LIKE 'video%'")['count'],
        'size' => $db->fetchOne("SELECT SUM(file_size) as size FROM media")['size'] ?? 0
    ],
    'inquiries' => [
        'total' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries")['count'],
        'pending' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'],
        'responded' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'contacted'")['count'],
        'recent' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)")['count']
    ],
    'users' => [
        'total' => $db->fetchOne("SELECT COUNT(*) as count FROM users")['count'],
        'active' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'],
        'admins' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'admin'")['count'],
        'editors' => $db->fetchOne("SELECT COUNT(*) as count FROM users WHERE role = 'editor'")['count']
    ]
];

// Chart data
$projectsChart = $db->fetchAll(
    "SELECT DATE(created_at) as date, COUNT(*) as count
     FROM projects
     WHERE created_at BETWEEN ? AND ?
     GROUP BY DATE(created_at)
     ORDER BY date",
    [$startDate, $endDate]
);

$inquiriesChart = $db->fetchAll(
    "SELECT DATE(created_at) as date, COUNT(*) as count
     FROM contact_inquiries
     WHERE created_at BETWEEN ? AND ?
     GROUP BY DATE(created_at)
     ORDER BY date",
    [$startDate, $endDate]
);

$mediaChart = $db->fetchAll(
    "SELECT DATE(created_at) as date, COUNT(*) as count
     FROM media
     WHERE created_at BETWEEN ? AND ?
     GROUP BY DATE(created_at)
     ORDER BY date",
    [$startDate, $endDate]
);

// Recent activity
$recentProjects = $db->fetchAll(
    "SELECT id, title, project_type as status, created_at FROM projects ORDER BY created_at DESC LIMIT 5"
);

$recentInquiries = $db->fetchAll(
    "SELECT id, name, email, subject, status, created_at FROM contact_inquiries ORDER BY created_at DESC LIMIT 5"
);

$recentMedia = $db->fetchAll(
    "SELECT id, filename, original_name, file_type, created_at FROM media ORDER BY created_at DESC LIMIT 5"
);

// Top performing content (projects by creation date since no direct media relationship)
$topProjects = $db->fetchAll(
    "SELECT p.id, p.title, p.project_type as status, p.created_at
     FROM projects p
     WHERE p.is_active = 1
     ORDER BY p.created_at DESC
     LIMIT 5"
);

$serviceStats = $db->fetchAll(
    "SELECT s.id, s.title, s.is_featured, s.sort_order
     FROM services s
     WHERE s.is_active = 1
     ORDER BY s.sort_order ASC
     LIMIT 5"
);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics Dashboard - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Enhanced Analytics Page Styles */
        .analytics-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .analytics-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .analytics-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .analytics-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .analytics-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .analytics-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .analytics-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .analytics-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .analytics-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        .enhanced-date-filter {
            background: rgba(255, 255, 255, 0.15);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .enhanced-date-filter form {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .enhanced-date-filter label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            margin: 0;
        }

        .enhanced-date-filter input {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-xs) var(--spacing-sm);
            color: var(--gray-800);
            font-size: var(--font-size-sm);
        }

        .enhanced-overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-2xl);
        }

        .enhanced-overview-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            overflow: hidden;
            position: relative;
        }

        .enhanced-overview-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .enhanced-overview-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .enhanced-overview-card:hover::before {
            opacity: 1;
        }

        .enhanced-card-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .enhanced-card-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            color: var(--white);
        }

        .enhanced-card-icon.projects { background: linear-gradient(135deg, #667eea, #764ba2); }
        .enhanced-card-icon.media { background: linear-gradient(135deg, #f093fb, #f5576c); }
        .enhanced-card-icon.inquiries { background: linear-gradient(135deg, #4facfe, #00f2fe); }
        .enhanced-card-icon.users { background: linear-gradient(135deg, #43e97b, #38f9d7); }

        .enhanced-card-title h3 {
            margin: 0;
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
        }

        .enhanced-card-title p {
            margin: 0;
            font-size: var(--font-size-sm);
            color: var(--gray-600);
        }

        .enhanced-card-content {
            padding: var(--spacing-xl);
        }

        .enhanced-main-stat {
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        .enhanced-main-stat .stat-number {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--gray-800);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .enhanced-main-stat .stat-label {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            font-weight: var(--font-weight-medium);
        }

        .enhanced-sub-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
        }

        .enhanced-sub-stat {
            text-align: center;
            padding: var(--spacing-md);
            background: var(--gray-50);
            border-radius: var(--border-radius-md);
            border: 1px solid var(--gray-200);
        }

        .enhanced-sub-stat .sub-number {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .enhanced-sub-stat .sub-label {
            font-size: var(--font-size-xs);
            color: var(--gray-600);
            font-weight: var(--font-weight-medium);
        }

        .enhanced-charts-section {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-2xl);
            overflow: hidden;
        }

        .enhanced-section-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--gray-200);
        }

        .enhanced-section-header h2 {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .enhanced-section-header p {
            margin: 0;
            font-size: var(--font-size-base);
            color: var(--gray-600);
        }

        .enhanced-charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-xl);
            padding: var(--spacing-xl);
        }

        .enhanced-chart-card {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .enhanced-chart-header {
            background: var(--gray-50);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .enhanced-chart-header h3 {
            margin: 0;
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .enhanced-chart-content {
            padding: var(--spacing-lg);
        }

        .enhanced-chart-container {
            position: relative;
            height: 300px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .analytics-page-header .header-content {
                flex-direction: column;
                text-align: center;
            }

            .analytics-stats {
                flex-direction: column;
                align-items: center;
            }

            .enhanced-date-filter form {
                flex-direction: column;
                align-items: stretch;
            }

            .enhanced-overview-grid {
                grid-template-columns: 1fr;
            }

            .enhanced-charts-grid {
                grid-template-columns: 1fr;
            }

            .enhanced-sub-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Enhanced Analytics Header -->
                <div class="analytics-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-chart-line"></i>
                                Analytics Dashboard
                            </h1>
                            <p class="page-description">Monitor your website performance and track key business metrics</p>
                        </div>
                        <div class="enhanced-date-filter">
                            <form method="GET">
                                <label>From:</label>
                                <input type="date" name="start_date" value="<?= $startDate ?>" required>
                                <label>To:</label>
                                <input type="date" name="end_date" value="<?= $endDate ?>" required>
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-filter"></i>
                                    Apply
                                </button>
                            </form>
                        </div>
                    </div>

                    <div class="analytics-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= number_format($analytics['projects']['total']) ?></span>
                            <span class="stat-label">Total Projects</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= number_format($analytics['inquiries']['total']) ?></span>
                            <span class="stat-label">Total Inquiries</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= number_format($analytics['media']['total']) ?></span>
                            <span class="stat-label">Media Files</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= number_format($analytics['users']['total']) ?></span>
                            <span class="stat-label">System Users</span>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Overview Stats -->
                <div class="enhanced-overview-grid">
                    <div class="enhanced-overview-card">
                        <div class="enhanced-card-header">
                            <div class="enhanced-card-icon projects">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="enhanced-card-title">
                                <h3>Projects</h3>
                                <p>Construction Portfolio</p>
                            </div>
                        </div>
                        <div class="enhanced-card-content">
                            <div class="enhanced-main-stat">
                                <span class="stat-number"><?= number_format($analytics['projects']['total']) ?></span>
                                <span class="stat-label">Total Projects</span>
                            </div>
                            <div class="enhanced-sub-stats">
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['projects']['active'] ?></span>
                                    <span class="sub-label">Active</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['projects']['completed'] ?></span>
                                    <span class="sub-label">Completed</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['projects']['recent'] ?></span>
                                    <span class="sub-label">This Month</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-overview-card">
                        <div class="enhanced-card-header">
                            <div class="enhanced-card-icon media">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="enhanced-card-title">
                                <h3>Media Library</h3>
                                <p>Digital Assets</p>
                            </div>
                        </div>
                        <div class="enhanced-card-content">
                            <div class="enhanced-main-stat">
                                <span class="stat-number"><?= number_format($analytics['media']['total']) ?></span>
                                <span class="stat-label">Total Files</span>
                            </div>
                            <div class="enhanced-sub-stats">
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['media']['images'] ?></span>
                                    <span class="sub-label">Images</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['media']['videos'] ?></span>
                                    <span class="sub-label">Videos</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= formatFileSize($analytics['media']['size']) ?></span>
                                    <span class="sub-label">Total Size</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-overview-card">
                        <div class="enhanced-card-header">
                            <div class="enhanced-card-icon inquiries">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="enhanced-card-title">
                                <h3>Inquiries</h3>
                                <p>Customer Contact</p>
                            </div>
                        </div>
                        <div class="enhanced-card-content">
                            <div class="enhanced-main-stat">
                                <span class="stat-number"><?= number_format($analytics['inquiries']['total']) ?></span>
                                <span class="stat-label">Total Inquiries</span>
                            </div>
                            <div class="enhanced-sub-stats">
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['inquiries']['pending'] ?></span>
                                    <span class="sub-label">Pending</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['inquiries']['responded'] ?></span>
                                    <span class="sub-label">Responded</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['inquiries']['recent'] ?></span>
                                    <span class="sub-label">This Week</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-overview-card">
                        <div class="enhanced-card-header">
                            <div class="enhanced-card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="enhanced-card-title">
                                <h3>Users</h3>
                                <p>System Access</p>
                            </div>
                        </div>
                        <div class="enhanced-card-content">
                            <div class="enhanced-main-stat">
                                <span class="stat-number"><?= number_format($analytics['users']['total']) ?></span>
                                <span class="stat-label">Total Users</span>
                            </div>
                            <div class="enhanced-sub-stats">
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['users']['active'] ?></span>
                                    <span class="sub-label">Active</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['users']['admins'] ?></span>
                                    <span class="sub-label">Admins</span>
                                </div>
                                <div class="enhanced-sub-stat">
                                    <span class="sub-number"><?= $analytics['users']['editors'] ?></span>
                                    <span class="sub-label">Editors</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Charts Section -->
                <div class="enhanced-charts-section">
                    <div class="enhanced-section-header">
                        <h2>
                            <i class="fas fa-chart-area"></i>
                            Performance Charts
                        </h2>
                        <p>Visual representation of your data trends over time</p>
                    </div>
                    <div class="enhanced-charts-grid">
                        <div class="enhanced-chart-card">
                            <div class="enhanced-chart-header">
                                <h3>
                                    <i class="fas fa-building"></i>
                                    Projects Over Time
                                </h3>
                                <button class="btn btn-outline btn-sm" title="Toggle Chart Type">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                            </div>
                            <div class="enhanced-chart-content">
                                <div class="enhanced-chart-container">
                                    <canvas id="projectsChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="enhanced-chart-card">
                            <div class="enhanced-chart-header">
                                <h3>
                                    <i class="fas fa-envelope"></i>
                                    Inquiries Over Time
                                </h3>
                                <button class="btn btn-outline btn-sm" title="Toggle Chart Type">
                                    <i class="fas fa-chart-bar"></i>
                                </button>
                            </div>
                            <div class="enhanced-chart-content">
                                <div class="enhanced-chart-container">
                                    <canvas id="inquiriesChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Detailed Analytics -->
                <div class="detailed-analytics">
                    <div class="section-header">
                        <h2 class="section-title">
                            <i class="fas fa-chart-pie"></i>
                            Detailed Analytics
                        </h2>
                        <p class="section-description">In-depth breakdown of your data and performance metrics</p>
                    </div>
                    <div class="analytics-grid">
                        <!-- Project Status Breakdown -->
                        <div class="analytics-card admin-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-chart-pie"></i>
                                    Project Status Distribution
                                </h3>
                            </div>
                            <div class="card-content">
                                <div class="progress-analytics">
                                    <div class="progress-item">
                                        <div class="progress-label">
                                            <span class="label-text">Active Projects</span>
                                            <span class="label-value"><?= $analytics['projects']['active'] ?> / <?= $analytics['projects']['total'] ?></span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill active" style="width: <?= $analytics['projects']['total'] > 0 ? ($analytics['projects']['active'] / $analytics['projects']['total']) * 100 : 0 ?>%"></div>
                                        </div>
                                        <div class="progress-percentage"><?= $analytics['projects']['total'] > 0 ? round(($analytics['projects']['active'] / $analytics['projects']['total']) * 100, 1) : 0 ?>%</div>
                                    </div>
                                    <div class="progress-item">
                                        <div class="progress-label">
                                            <span class="label-text">Completed Projects</span>
                                            <span class="label-value"><?= $analytics['projects']['completed'] ?> / <?= $analytics['projects']['total'] ?></span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill completed" style="width: <?= $analytics['projects']['total'] > 0 ? ($analytics['projects']['completed'] / $analytics['projects']['total']) * 100 : 0 ?>%"></div>
                                        </div>
                                        <div class="progress-percentage"><?= $analytics['projects']['total'] > 0 ? round(($analytics['projects']['completed'] / $analytics['projects']['total']) * 100, 1) : 0 ?>%</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Media Breakdown -->
                        <div class="analytics-card admin-card">
                            <div class="card-header">
                                <h3>
                                    <i class="fas fa-chart-donut"></i>
                                    Media Library Breakdown
                                </h3>
                            </div>
                            <div class="card-content">
                                <div class="media-breakdown">
                                    <div class="media-type-item">
                                        <div class="media-icon images">
                                            <i class="fas fa-image"></i>
                                        </div>
                                        <div class="media-details">
                                            <h4><?= number_format($analytics['media']['images']) ?></h4>
                                            <p>Images</p>
                                            <small><?= $analytics['media']['total'] > 0 ? round(($analytics['media']['images'] / $analytics['media']['total']) * 100, 1) : 0 ?>% of total</small>
                                        </div>
                                    </div>
                                    <div class="media-type-item">
                                        <div class="media-icon videos">
                                            <i class="fas fa-video"></i>
                                        </div>
                                        <div class="media-details">
                                            <h4><?= number_format($analytics['media']['videos']) ?></h4>
                                            <p>Videos</p>
                                            <small><?= $analytics['media']['total'] > 0 ? round(($analytics['media']['videos'] / $analytics['media']['total']) * 100, 1) : 0 ?>% of total</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="storage-info">
                                    <div class="storage-stat">
                                        <i class="fas fa-hdd"></i>
                                        <span>Total Storage: <?= formatFileSize($analytics['media']['size']) ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                    <!-- Top Performing Content -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-trophy"></i> Top Projects</h3>
                        </div>
                        <div class="card-body">
                            <div class="top-content">
                                <?php foreach ($topProjects as $project): ?>
                                    <div class="content-item">
                                        <div class="content-info">
                                            <h5><?= htmlspecialchars($project['title']) ?></h5>
                                            <p>Created: <?= formatDate($project['created_at'], 'M j, Y') ?></p>
                                        </div>
                                        <span class="status-badge <?= $project['status'] ?>"><?= ucfirst($project['status']) ?></span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-clock"></i> Recent Activity</h3>
                        </div>
                        <div class="card-body">
                            <div class="activity-feed">
                                <?php foreach ($recentProjects as $project): ?>
                                    <div class="activity-item">
                                        <i class="fas fa-building"></i>
                                        <div>
                                            <p><strong><?= htmlspecialchars($project['title']) ?></strong> project created</p>
                                            <small><?= formatDate($project['created_at'], 'M j, Y g:i A') ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>

                                <?php foreach (array_slice($recentInquiries, 0, 3) as $inquiry): ?>
                                    <div class="activity-item">
                                        <i class="fas fa-envelope"></i>
                                        <div>
                                            <p>New inquiry from <strong><?= htmlspecialchars($inquiry['name']) ?></strong></p>
                                            <small><?= formatDate($inquiry['created_at'], 'M j, Y g:i A') ?></small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-download"></i> Export Data</h3>
                    </div>
                    <div class="card-body">
                        <div class="export-options">
                            <button class="btn btn-secondary" onclick="exportData('projects')">
                                <i class="fas fa-file-csv"></i> Export Projects
                            </button>
                            <button class="btn btn-secondary" onclick="exportData('inquiries')">
                                <i class="fas fa-file-csv"></i> Export Inquiries
                            </button>
                            <button class="btn btn-secondary" onclick="exportData('media')">
                                <i class="fas fa-file-csv"></i> Export Media
                            </button>
                            <button class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-file-pdf"></i> Generate Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Chart.js configurations
        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        };

        // Projects Chart
        const projectsData = <?= json_encode($projectsChart) ?>;
        const projectsCtx = document.getElementById('projectsChart').getContext('2d');
        new Chart(projectsCtx, {
            type: 'line',
            data: {
                labels: projectsData.map(item => item.date),
                datasets: [{
                    label: 'Projects',
                    data: projectsData.map(item => item.count),
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: chartOptions
        });

        // Inquiries Chart
        const inquiriesData = <?= json_encode($inquiriesChart) ?>;
        const inquiriesCtx = document.getElementById('inquiriesChart').getContext('2d');
        new Chart(inquiriesCtx, {
            type: 'bar',
            data: {
                labels: inquiriesData.map(item => item.date),
                datasets: [{
                    label: 'Inquiries',
                    data: inquiriesData.map(item => item.count),
                    backgroundColor: '#e74c3c',
                    borderColor: '#c0392b',
                    borderWidth: 1
                }]
            },
            options: chartOptions
        });

        // Export functions
        function exportData(type) {
            const startDate = '<?= $startDate ?>';
            const endDate = '<?= $endDate ?>';
            window.open(`export.php?type=${type}&start_date=${startDate}&end_date=${endDate}`, '_blank');
        }

        function generateReport() {
            const startDate = '<?= $startDate ?>';
            const endDate = '<?= $endDate ?>';
            window.open(`report.php?start_date=${startDate}&end_date=${endDate}`, '_blank');
        }

        // Auto-refresh every 5 minutes
        setInterval(() => {
            location.reload();
        }, 300000);
    </script>
</body>
</html>
