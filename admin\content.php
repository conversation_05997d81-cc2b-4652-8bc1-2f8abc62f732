<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    $action = $_POST['action'] ?? '';

    if ($action === 'update_content') {
        $contentKey = sanitize($_POST['content_key']);
        $contentValue = $_POST['content_value']; // Don't sanitize HTML content

        try {
            // Check if content exists
            $existing = $db->fetchOne("SELECT id FROM site_content WHERE content_key = ?", [$contentKey]);

            if ($existing) {
                $db->update('site_content',
                    ['content_value' => $contentValue, 'updated_at' => date('Y-m-d H:i:s')],
                    'content_key = ?',
                    [$contentKey]
                );
            } else {
                $db->insert('site_content', [
                    'content_key' => $contentKey,
                    'content_value' => $contentValue,
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $message = 'Content updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating content: ' . $e->getMessage();
            $messageType = 'error';
        }
    }

    if ($action === 'update_seo') {
        $pageKey = sanitize($_POST['page_key']);
        $metaTitle = sanitize($_POST['meta_title']);
        $metaDescription = sanitize($_POST['meta_description']);
        $metaKeywords = sanitize($_POST['meta_keywords']);

        try {
            $seoData = [
                'meta_title' => $metaTitle,
                'meta_description' => $metaDescription,
                'meta_keywords' => $metaKeywords
            ];

            $existing = $db->fetchOne("SELECT id FROM page_seo WHERE page_key = ?", [$pageKey]);

            if ($existing) {
                $db->update('page_seo',
                    array_merge($seoData, ['updated_at' => date('Y-m-d H:i:s')]),
                    'page_key = ?',
                    [$pageKey]
                );
            } else {
                $db->insert('page_seo', array_merge($seoData, [
                    'page_key' => $pageKey,
                    'created_at' => date('Y-m-d H:i:s')
                ]));
            }

            $message = 'SEO settings updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating SEO settings: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get current content
$contentItems = [
    'hero_title' => 'Building Excellence Since 2010',
    'hero_subtitle' => 'Professional construction services with unmatched quality and reliability',
    'about_title' => 'About Flori Construction Ltd',
    'about_text' => 'Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.',
    'services_title' => 'Our Services',
    'services_subtitle' => 'Comprehensive construction solutions for all your needs',
    'projects_title' => 'Our Projects',
    'projects_subtitle' => 'Showcasing our commitment to excellence and quality craftsmanship',
    'contact_title' => 'Get In Touch',
    'contact_subtitle' => 'Ready to start your construction project? Contact us for a free consultation and quote.',
    'footer_text' => 'Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.'
];

// Get content from database
foreach ($contentItems as $key => $defaultValue) {
    $content = $db->fetchOne("SELECT content_value FROM site_content WHERE content_key = ?", [$key]);
    if ($content) {
        $contentItems[$key] = $content['content_value'];
    }
}

// Get SEO data
$pages = ['home', 'about', 'services', 'projects', 'contact'];
$seoData = [];

foreach ($pages as $page) {
    $seo = $db->fetchOne("SELECT * FROM page_seo WHERE page_key = ?", [$page]);
    $seoData[$page] = $seo ?: [
        'meta_title' => '',
        'meta_description' => '',
        'meta_keywords' => ''
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Management - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- TinyMCE -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js" integrity="sha512-6JR4bbn8rCKvrkdoTJd/VFyXAN4CE9XMtgykPWgKiHjou56YDJxWsi90hAeMTYxNwUnKSQu9JPc3SQUg+aGCHw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

    <style>
        /* Enhanced Content Management Styles */
        .content-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .page-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px 0;
            margin: -20px -20px 40px -20px;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
            text-align: center;
        }

        .page-header p {
            text-align: center;
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .enhanced-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            border: 1px solid #e8ecf4;
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .enhanced-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(0,0,0,0.12);
        }

        .enhanced-card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e8ecf4;
        }

        .enhanced-card-header h3 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .enhanced-card-header i {
            font-size: 1.2rem;
            color: #28a745;
        }

        .enhanced-card-content {
            padding: 0;
        }

        .enhanced-tabs {
            background: white;
            border-radius: 15px;
            overflow: hidden;
        }

        .tab-buttons {
            display: flex;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #e8ecf4;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding: 0 20px;
        }

        .tab-buttons::-webkit-scrollbar {
            display: none;
        }

        .tab-button {
            background: transparent;
            border: none;
            padding: 18px 24px;
            color: #6c757d;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            position: relative;
            border-bottom: 3px solid transparent;
            font-size: 0.95rem;
            margin: 0 2px;
            border-radius: 8px 8px 0 0;
        }

        .tab-button:hover {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            transform: translateY(-1px);
        }

        .tab-button.active {
            color: #28a745;
            background: rgba(40, 167, 69, 0.15);
            border-bottom-color: #28a745;
            font-weight: 600;
        }

        .tab-content {
            display: none;
            padding: 35px;
            animation: fadeIn 0.3s ease;
            min-height: 400px;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .content-section {
            margin-bottom: 45px;
            padding-bottom: 35px;
            border-bottom: 1px solid #e9ecef;
        }

        .content-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .content-section h4 {
            color: #2c3e50;
            font-size: 1.4rem;
            margin-bottom: 30px;
            padding-bottom: 12px;
            border-bottom: 3px solid #28a745;
            display: inline-block;
            font-weight: 600;
        }

        .content-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 28px;
            margin-bottom: 25px;
            transition: all 0.3s ease;
            position: relative;
        }

        .content-item:hover {
            border-color: #28a745;
            background: linear-gradient(135deg, #f1f8f4 0%, #ffffff 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.1);
        }

        .content-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .content-label i {
            color: #28a745;
            font-size: 1rem;
        }

        .form-control-enhanced {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background: #f8f9fa;
            font-size: 1rem;
        }

        .form-control-enhanced:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
            background: white;
            outline: none;
        }

        .char-count {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 8px;
            text-align: right;
            font-weight: 500;
        }

        .char-count .count {
            font-weight: 600;
        }

        .btn-enhanced {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 12px;
            padding: 14px 28px;
            color: white;
            font-weight: 600;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.25);
            margin-top: 20px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 160px;
            justify-content: center;
        }

        .btn-enhanced:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.35);
            color: white;
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        }

        .btn-enhanced:active {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);
        }

        .btn-enhanced:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }

        .btn-outline-enhanced {
            background: transparent;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 12px 24px;
            color: #28a745;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            min-width: 140px;
            justify-content: center;
        }

        .btn-outline-enhanced:hover {
            background: #28a745;
            color: white;
            transform: translateY(-2px);
            text-decoration: none;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        .btn-outline-enhanced:active {
            transform: translateY(0);
        }

        .seo-preview {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .seo-preview-header {
            color: #6c757d;
            font-size: 0.9rem;
            margin-bottom: 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .seo-title {
            color: #1a0dab;
            font-size: 1.2rem;
            text-decoration: underline;
            margin-bottom: 8px;
            font-weight: 500;
            cursor: pointer;
        }

        .seo-title:hover {
            color: #0d47a1;
        }

        .seo-url {
            color: #006621;
            font-size: 0.9rem;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .seo-description {
            color: #545454;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .alert-enhanced {
            border: none;
            border-radius: 12px;
            padding: 20px 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .content-form {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #f0f0f0;
        }

        .content-form:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        /* Enhanced spacing for better visual hierarchy */
        .content-item + .content-item {
            margin-top: 25px;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-top: 25px;
        }

        .quick-action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            transition: all 0.3s ease;
        }

        .quick-action-item:hover {
            transform: translateY(-2px);
        }

        .quick-action-item .btn-outline-enhanced {
            margin-bottom: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            min-height: 80px;
            justify-content: center;
            font-weight: 500;
            border-width: 2px;
            transition: all 0.3s ease;
        }

        .quick-action-item .btn-outline-enhanced i {
            font-size: 1.2rem;
            margin-bottom: 4px;
        }

        .quick-action-item .btn-outline-enhanced span {
            font-size: 0.9rem;
            line-height: 1.2;
        }

        .quick-action-item small {
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .quick-action-item:hover small {
            opacity: 1;
            color: #28a745 !important;
        }

        /* Better spacing for SEO sections */
        .seo-preview {
            margin-top: 25px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .enhanced-card-content {
                padding: 0;
            }

            .page-header {
                padding: 30px 20px;
            }

            .page-header h1 {
                font-size: 2rem;
            }

            .tab-content {
                padding: 25px 20px;
            }

            .content-item {
                padding: 22px 18px;
                margin-bottom: 20px;
            }

            .quick-actions-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .quick-action-item .btn-outline-enhanced {
                min-height: 70px;
                padding: 14px 18px !important;
            }

            .quick-action-item .btn-outline-enhanced i {
                font-size: 1.1rem;
            }

            .quick-action-item .btn-outline-enhanced span {
                font-size: 0.85rem;
            }

            .btn-enhanced {
                padding: 12px 20px;
                min-width: 140px;
                font-size: 0.9rem;
            }

            .btn-outline-enhanced {
                padding: 10px 18px;
                min-width: 120px;
                font-size: 0.9rem;
            }

            .tab-buttons {
                padding: 0 15px;
            }

            .tab-button {
                padding: 16px 20px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <div class="content-container">
                    <!-- Page Header -->
                    <div class="page-header">
                        <h1><i class="fas fa-edit"></i> Content Management</h1>
                        <p>Manage your website content, SEO settings, and page information</p>
                    </div>

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                    <div class="alert-enhanced alert-<?= $messageType ?>">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                        <?= $message ?>
                    </div>
                    <?php endif; ?>

                    <!-- Content Editing Tabs -->
                    <div class="enhanced-card">
                        <div class="enhanced-card-header">
                            <h3><i class="fas fa-edit"></i> Website Content</h3>
                        </div>
                        <div class="enhanced-card-content">
                            <div class="enhanced-tabs">
                            <div class="tab-buttons">
                                <button class="tab-button active" data-tab="homepage">Homepage</button>
                                <button class="tab-button" data-tab="about">About Page</button>
                                <button class="tab-button" data-tab="services">Services</button>
                                <button class="tab-button" data-tab="projects">Projects</button>
                                <button class="tab-button" data-tab="contact">Contact</button>
                                <button class="tab-button" data-tab="seo">SEO Settings</button>
                            </div>

                            <!-- Homepage Content -->
                            <div class="tab-content active" id="homepage">
                                <div class="content-section">
                                    <h4><i class="fas fa-home"></i> Homepage Hero Section</h4>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-star"></i>
                                                Hero Section Title
                                            </div>
                                            <input type="hidden" name="content_key" value="hero_title">
                                            <input type="text" name="content_value" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($contentItems['hero_title']) ?>" maxlength="100"
                                                   placeholder="Enter your main headline">
                                            <div class="char-count">Characters: <span class="count">0</span>/100</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Hero Title
                                        </button>
                                    </form>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-quote-left"></i>
                                                Hero Section Subtitle
                                            </div>
                                            <input type="hidden" name="content_key" value="hero_subtitle">
                                            <textarea name="content_value" class="form-control-enhanced" rows="3" maxlength="200"
                                                      placeholder="Enter a compelling subtitle that describes your business"><?= htmlspecialchars($contentItems['hero_subtitle']) ?></textarea>
                                            <div class="char-count">Characters: <span class="count">0</span>/200</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Hero Subtitle
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- About Content -->
                            <div class="tab-content" id="about">
                                <div class="content-section">
                                    <h4><i class="fas fa-info-circle"></i> About Page Content</h4>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-heading"></i>
                                                About Section Title
                                            </div>
                                            <input type="hidden" name="content_key" value="about_title">
                                            <input type="text" name="content_value" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($contentItems['about_title']) ?>" maxlength="100"
                                                   placeholder="Enter the about section title">
                                            <div class="char-count">Characters: <span class="count">0</span>/100</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update About Title
                                        </button>
                                    </form>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-align-left"></i>
                                                About Section Text
                                            </div>
                                            <input type="hidden" name="content_key" value="about_text">
                                            <textarea name="content_value" class="form-control-enhanced rich-editor" rows="6"
                                                      placeholder="Enter detailed information about your company"><?= htmlspecialchars($contentItems['about_text']) ?></textarea>
                                            <small style="color: #6c757d; margin-top: 10px; display: block;">
                                                <i class="fas fa-magic"></i> Rich text editor with formatting options
                                            </small>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update About Text
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Services Content -->
                            <div class="tab-content" id="services">
                                <div class="content-section">
                                    <h4><i class="fas fa-tools"></i> Services Page Content</h4>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-heading"></i>
                                                Services Section Title
                                            </div>
                                            <input type="hidden" name="content_key" value="services_title">
                                            <input type="text" name="content_value" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($contentItems['services_title']) ?>" maxlength="100"
                                                   placeholder="Enter the services section title">
                                            <div class="char-count">Characters: <span class="count">0</span>/100</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Services Title
                                        </button>
                                    </form>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-list-ul"></i>
                                                Services Section Subtitle
                                            </div>
                                            <input type="hidden" name="content_key" value="services_subtitle">
                                            <textarea name="content_value" class="form-control-enhanced" rows="3" maxlength="200"
                                                      placeholder="Enter a brief description of your services"><?= htmlspecialchars($contentItems['services_subtitle']) ?></textarea>
                                            <div class="char-count">Characters: <span class="count">0</span>/200</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Services Subtitle
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Projects Content -->
                            <div class="tab-content" id="projects">
                                <div class="content-section">
                                    <h4><i class="fas fa-building"></i> Projects Page Content</h4>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-heading"></i>
                                                Projects Section Title
                                            </div>
                                            <input type="hidden" name="content_key" value="projects_title">
                                            <input type="text" name="content_value" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($contentItems['projects_title']) ?>" maxlength="100"
                                                   placeholder="Enter the projects section title">
                                            <div class="char-count">Characters: <span class="count">0</span>/100</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Projects Title
                                        </button>
                                    </form>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-images"></i>
                                                Projects Section Subtitle
                                            </div>
                                            <input type="hidden" name="content_key" value="projects_subtitle">
                                            <textarea name="content_value" class="form-control-enhanced" rows="3" maxlength="200"
                                                      placeholder="Enter a description of your project portfolio"><?= htmlspecialchars($contentItems['projects_subtitle']) ?></textarea>
                                            <div class="char-count">Characters: <span class="count">0</span>/200</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Projects Subtitle
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Contact Content -->
                            <div class="tab-content" id="contact">
                                <div class="content-section">
                                    <h4><i class="fas fa-phone"></i> Contact Page Content</h4>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-heading"></i>
                                                Contact Section Title
                                            </div>
                                            <input type="hidden" name="content_key" value="contact_title">
                                            <input type="text" name="content_value" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($contentItems['contact_title']) ?>" maxlength="100"
                                                   placeholder="Enter the contact section title">
                                            <div class="char-count">Characters: <span class="count">0</span>/100</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Contact Title
                                        </button>
                                    </form>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_content">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-envelope"></i>
                                                Contact Section Subtitle
                                            </div>
                                            <input type="hidden" name="content_key" value="contact_subtitle">
                                            <textarea name="content_value" class="form-control-enhanced" rows="3" maxlength="200"
                                                      placeholder="Enter a call-to-action for potential clients"><?= htmlspecialchars($contentItems['contact_subtitle']) ?></textarea>
                                            <div class="char-count">Characters: <span class="count">0</span>/200</div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update Contact Subtitle
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- SEO Settings -->
                            <div class="tab-content" id="seo">
                                <?php foreach ($pages as $page): ?>
                                <div class="content-section">
                                    <h4><i class="fas fa-search"></i> <?= ucfirst($page) ?> Page SEO</h4>

                                    <form method="POST" class="content-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_seo">
                                        <input type="hidden" name="page_key" value="<?= $page ?>">

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-heading"></i>
                                                Meta Title
                                            </div>
                                            <input type="text" name="meta_title" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($seoData[$page]['meta_title']) ?>"
                                                   maxlength="60" placeholder="Page title for search engines (recommended: 50-60 characters)">
                                            <div class="char-count">Characters: <span class="count">0</span>/60</div>
                                        </div>

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-align-left"></i>
                                                Meta Description
                                            </div>
                                            <textarea name="meta_description" class="form-control-enhanced" rows="3"
                                                      maxlength="160" placeholder="Brief description for search results (recommended: 150-160 characters)"><?= htmlspecialchars($seoData[$page]['meta_description']) ?></textarea>
                                            <div class="char-count">Characters: <span class="count">0</span>/160</div>
                                        </div>

                                        <div class="content-item">
                                            <div class="content-label">
                                                <i class="fas fa-tags"></i>
                                                Meta Keywords
                                            </div>
                                            <input type="text" name="meta_keywords" class="form-control-enhanced"
                                                   value="<?= htmlspecialchars($seoData[$page]['meta_keywords']) ?>"
                                                   placeholder="Comma-separated keywords (e.g., construction, building, renovation)">
                                            <small style="color: #6c757d; margin-top: 8px; display: block;">
                                                <i class="fas fa-info-circle"></i> Separate keywords with commas. Focus on relevant terms for this page.
                                            </small>
                                        </div>

                                        <div class="seo-preview">
                                            <div class="seo-preview-header">
                                                <i class="fas fa-eye"></i> Search Engine Preview
                                            </div>
                                            <div class="seo-title"><?= $seoData[$page]['meta_title'] ?: $page . ' - ' . SITE_NAME ?></div>
                                            <div class="seo-url"><?= SITE_URL ?>/<?= $page === 'home' ? '' : $page . '.php' ?></div>
                                            <div class="seo-description"><?= $seoData[$page]['meta_description'] ?: 'No description available - add a meta description to improve search engine visibility.' ?></div>
                                        </div>

                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Update <?= ucfirst($page) ?> SEO
                                        </button>
                                    </form>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- Quick Actions -->
                    <div class="enhanced-card">
                        <div class="enhanced-card-header">
                            <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                            <p style="margin: 8px 0 0 0; color: #6c757d; font-size: 0.9rem; font-weight: 400;">
                                Frequently used tools and shortcuts for managing your website
                            </p>
                        </div>
                        <div class="enhanced-card-content" style="padding: 35px;">
                            <div class="quick-actions-grid" style="gap: 25px; margin-top: 0;">
                                <div class="quick-action-item">
                                    <a href="../index.php" target="_blank" class="btn-outline-enhanced" style="width: 100%; padding: 16px 20px;">
                                        <i class="fas fa-external-link-alt"></i>
                                        <span>Preview Website</span>
                                    </a>
                                    <small style="display: block; margin-top: 8px; color: #6c757d; text-align: center; font-size: 0.8rem;">
                                        View your live website
                                    </small>
                                </div>
                                <div class="quick-action-item">
                                    <a href="media.php" class="btn-outline-enhanced" style="width: 100%; padding: 16px 20px;">
                                        <i class="fas fa-images"></i>
                                        <span>Manage Media</span>
                                    </a>
                                    <small style="display: block; margin-top: 8px; color: #6c757d; text-align: center; font-size: 0.8rem;">
                                        Upload and organize files
                                    </small>
                                </div>
                                <div class="quick-action-item">
                                    <a href="branding.php" class="btn-outline-enhanced" style="width: 100%; padding: 16px 20px;">
                                        <i class="fas fa-palette"></i>
                                        <span>Branding Settings</span>
                                    </a>
                                    <small style="display: block; margin-top: 8px; color: #6c757d; text-align: center; font-size: 0.8rem;">
                                        Customize colors and fonts
                                    </small>
                                </div>
                                <div class="quick-action-item">
                                    <a href="email-test.php" class="btn-outline-enhanced" style="width: 100%; padding: 16px 20px;">
                                        <i class="fas fa-envelope"></i>
                                        <span>Test Email</span>
                                    </a>
                                    <small style="display: block; margin-top: 8px; color: #6c757d; text-align: center; font-size: 0.8rem;">
                                        Test email functionality
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>

    <script>
        // Tab functionality
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.dataset.tab;

                // Remove active class from all tabs and buttons
                document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

                // Add active class to clicked button and corresponding content
                this.classList.add('active');
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Enhanced character counting with better styling
        document.querySelectorAll('input[maxlength], textarea[maxlength]').forEach(input => {
            const updateCount = () => {
                const maxLength = input.getAttribute('maxlength');
                const currentLength = input.value.length;
                const countSpan = input.parentNode.querySelector('.char-count .count');
                if (countSpan) {
                    countSpan.textContent = currentLength;
                    const percentage = currentLength / maxLength;

                    if (percentage > 0.9) {
                        countSpan.style.color = '#dc3545';
                        countSpan.style.fontWeight = '600';
                    } else if (percentage > 0.7) {
                        countSpan.style.color = '#fd7e14';
                        countSpan.style.fontWeight = '600';
                    } else {
                        countSpan.style.color = '#28a745';
                        countSpan.style.fontWeight = '500';
                    }
                }
            };

            updateCount();
            input.addEventListener('input', updateCount);
        });

        // Enhanced form submission handling
        document.querySelectorAll('form[method="POST"]').forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn && !submitBtn.disabled) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                    submitBtn.disabled = true;

                    // Re-enable after timeout in case of error
                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 10000);
                }
            });
        });

        // Initialize TinyMCE for rich text editors with enhanced styling
        if (typeof tinymce !== 'undefined') {
            tinymce.init({
                selector: '.rich-editor',
                height: 350,
                menubar: false,
                plugins: 'advlist autolink lists link image charmap preview anchor searchreplace visualblocks code fullscreen insertdatetime media table paste help wordcount',
                toolbar: 'undo redo | formatselect | bold italic backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | removeformat | code | help',
                content_style: `
                    body {
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                        font-size: 14px;
                        line-height: 1.6;
                        color: #333;
                        padding: 15px;
                    }
                    h1, h2, h3, h4, h5, h6 { color: #2c3e50; margin-top: 1em; margin-bottom: 0.5em; }
                    p { margin-bottom: 1em; }
                    a { color: #28a745; }
                `,
                branding: false,
                elementpath: false,
                resize: true,
                statusbar: true,
                paste_as_text: true,
                setup: function(editor) {
                    editor.on('change', function() {
                        editor.save();
                    });
                }
            });
        } else {
            console.warn('TinyMCE failed to load. Rich text editors will not be available.');
            // Fallback: enhance textarea styling
            document.querySelectorAll('.rich-editor').forEach(textarea => {
                textarea.style.minHeight = '200px';
                textarea.style.fontFamily = 'monospace';
            });
        }
    </script>
</body>
</html>
