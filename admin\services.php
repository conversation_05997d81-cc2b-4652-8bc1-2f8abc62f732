<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle actions
$action = $_GET['action'] ?? $_POST['action'] ?? 'list';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    if ($action === 'delete') {
        $serviceId = $_POST['id'] ?? null;

    if (!$serviceId) {
        $message = 'Service ID is required for deletion';
        $messageType = 'error';
    } else {
        try {
            // Check if service exists
            $existingService = $db->fetchOne("SELECT id, title FROM services WHERE id = ? AND is_active = 1", [$serviceId]);

            if (!$existingService) {
                $message = 'Service not found or already deleted';
                $messageType = 'error';
            } else {
                // Soft delete the service
                $result = $db->update('services', [
                    'is_active' => 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$serviceId]);

                if ($result) {
                    $message = 'Service "' . htmlspecialchars($existingService['title']) . '" deleted successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to delete service';
                    $messageType = 'error';
                }
            }
            $action = 'list';
        } catch (Exception $e) {
            $message = 'Error deleting service: ' . $e->getMessage();
            $messageType = 'error';
            error_log("Service deletion error: " . $e->getMessage());
        }
        }
    } elseif ($action === 'add' || $action === 'edit') {
        $serviceId = $_POST['id'] ?? null;
        $title = sanitize($_POST['title']);
        $slug = generateSlug($_POST['slug'] ?: $title);
        $description = sanitize($_POST['description']);
        $shortDescription = sanitize($_POST['short_description']);
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
        $sortOrder = (int)($_POST['sort_order'] ?: 0);
        $metaTitle = sanitize($_POST['meta_title']);
        $metaDescription = sanitize($_POST['meta_description']);

        // Validate required fields
        if (empty($title) || empty($shortDescription) || empty($description)) {
            $message = 'Title, short description, and full description are required';
            $messageType = 'error';
        } else {
            // Handle file upload
        $featuredImage = '';
        if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
            try {
                $uploadResult = uploadFile($_FILES['featured_image'], 'services');
                $featuredImage = $uploadResult['file_path'];
            } catch (Exception $e) {
                $message = 'Error uploading image: ' . $e->getMessage();
                $messageType = 'error';
            }
        }

        if (!$message) {
            try {
                $serviceData = [
                    'title' => $title,
                    'slug' => $slug,
                    'description' => $description,
                    'short_description' => $shortDescription,
                    'is_featured' => $isFeatured,
                    'sort_order' => $sortOrder,
                    'meta_title' => $metaTitle,
                    'meta_description' => $metaDescription,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($featuredImage) {
                    $serviceData['featured_image'] = $featuredImage;
                }

                if ($action === 'add') {
                    $serviceData['created_at'] = date('Y-m-d H:i:s');
                    $db->insert('services', $serviceData);
                    $message = 'Service added successfully!';
                } else {
                    $db->update('services', $serviceData, 'id = ?', [$serviceId]);
                    $message = 'Service updated successfully!';
                }

                $messageType = 'success';
                $action = 'list';

            } catch (Exception $e) {
                $message = 'Error saving service: ' . $e->getMessage();
                $messageType = 'error';
            }
            }
        }
    }
}

// Get service for editing
$service = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $service = $db->fetchOne("SELECT * FROM services WHERE id = ? AND is_active = 1", [$_GET['id']]);
    if (!$service) {
        $action = 'list';
        $message = 'Service not found';
        $messageType = 'error';
    }
}

// Get services list
if ($action === 'list') {
    $page = (int)($_GET['page'] ?? 1);
    $limit = ADMIN_ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';

    $where = ['is_active = 1'];
    $params = [];

    if ($search) {
        $where[] = '(title LIKE ? OR description LIKE ? OR short_description LIKE ?)';
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    $whereClause = implode(' AND ', $where);

    $totalQuery = "SELECT COUNT(*) as total FROM services WHERE $whereClause";
    $total = $db->fetchOne($totalQuery, $params)['total'];

    $servicesQuery = "SELECT * FROM services WHERE $whereClause ORDER BY sort_order ASC, title ASC LIMIT $limit OFFSET $offset";
    $services = $db->fetchAll($servicesQuery, $params);

    $totalPages = ceil($total / $limit);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services Management - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                <!-- Enhanced Services Header -->
                <div class="services-header">
                    <div class="services-header-content">
                        <div class="services-title-section">
                            <h2 class="services-main-title">
                                <i class="fas fa-tools services-icon"></i>
                                Services Management
                            </h2>
                            <p class="services-subtitle">Manage your construction services and offerings</p>
                            <div class="services-stats">
                                <div class="stat-item">
                                    <span class="stat-number"><?= $total ?></span>
                                    <span class="stat-label">Total Services</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?= count(array_filter($services, fn($s) => $s['is_featured'])) ?></span>
                                    <span class="stat-label">Featured</span>
                                </div>
                            </div>
                        </div>
                        <div class="services-actions">
                            <a href="?action=add" class="btn btn-success btn-large">
                                <i class="fas fa-plus"></i>
                                <span>Add New Service</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Search & Filters -->
                <div class="search-filters-container">
                    <div class="search-section">
                        <form method="GET" class="modern-search-form">
                            <input type="hidden" name="action" value="list">
                            <div class="search-input-group">
                                <div class="search-icon">
                                    <i class="fas fa-search"></i>
                                </div>
                                <input type="text" name="search" placeholder="Search services by title, description..."
                                       value="<?= htmlspecialchars($search) ?>" class="search-input">
                                <div class="search-actions">
                                    <button type="submit" class="search-btn">
                                        <i class="fas fa-search"></i>
                                        <span>Search</span>
                                    </button>
                                    <?php if ($search): ?>
                                    <a href="?action=list" class="clear-btn">
                                        <i class="fas fa-times"></i>
                                        <span>Clear</span>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Services Content -->
                <?php if (empty($services)): ?>
                <div class="empty-state-container">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-tools"></i>
                        </div>
                        <div class="empty-state-content">
                            <h3>No Services Found</h3>
                            <p>You haven't created any services yet. Start building your service portfolio by adding your first service.</p>
                            <div class="empty-state-actions">
                                <a href="?action=add" class="btn btn-success btn-large">
                                    <i class="fas fa-plus"></i>
                                    <span>Create Your First Service</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="services-table-container">
                    <div class="table-header">
                        <div class="table-title">
                            <h3>Services List</h3>
                            <span class="table-count"><?= count($services) ?> of <?= $total ?> services</span>
                        </div>
                        <div class="table-actions">
                            <button class="view-toggle active" data-view="table" title="Table View">
                                <i class="fas fa-table"></i>
                            </button>
                            <button class="view-toggle" data-view="grid" title="Grid View">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>

                    <div class="modern-table-wrapper">
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th class="col-image">
                                        <span class="th-content">
                                            <i class="fas fa-image"></i>
                                            Image
                                        </span>
                                    </th>
                                    <th class="col-title">
                                        <span class="th-content">
                                            <i class="fas fa-heading"></i>
                                            Service Details
                                        </span>
                                    </th>
                                    <th class="col-status">
                                        <span class="th-content">
                                            <i class="fas fa-star"></i>
                                            Status
                                        </span>
                                    </th>
                                    <th class="col-meta">
                                        <span class="th-content">
                                            <i class="fas fa-info-circle"></i>
                                            Meta
                                        </span>
                                    </th>
                                    <th class="col-actions">
                                        <span class="th-content">
                                            <i class="fas fa-cogs"></i>
                                            Actions
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                        <?php foreach ($services as $serv): ?>
                        <tr class="service-row" data-service-id="<?= $serv['id'] ?>">
                            <td class="col-image">
                                <div class="service-image-container">
                                    <?php if ($serv['featured_image']): ?>
                                    <img src="<?= UPLOAD_URL . '/' . $serv['featured_image'] ?>"
                                         alt="<?= htmlspecialchars($serv['title']) ?>"
                                         class="service-thumbnail"
                                         loading="lazy">
                                    <?php else: ?>
                                    <div class="service-thumbnail-placeholder">
                                        <i class="fas fa-image"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="col-title">
                                <div class="service-details">
                                    <h4 class="service-title"><?= htmlspecialchars($serv['title']) ?></h4>
                                    <p class="service-description"><?= htmlspecialchars(substr($serv['short_description'], 0, 120)) ?><?= strlen($serv['short_description']) > 120 ? '...' : '' ?></p>
                                    <div class="service-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-link"></i>
                                            <code>/<?= htmlspecialchars($serv['slug']) ?></code>
                                        </span>
                                    </div>
                                </div>
                            </td>
                            <td class="col-status">
                                <div class="status-indicators">
                                    <?php if ($serv['is_featured']): ?>
                                    <span class="status-badge featured">
                                        <i class="fas fa-star"></i>
                                        Featured
                                    </span>
                                    <?php else: ?>
                                    <span class="status-badge regular">
                                        <i class="fas fa-circle"></i>
                                        Regular
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="col-meta">
                                <div class="meta-info">
                                    <div class="meta-row">
                                        <span class="meta-label">Order:</span>
                                        <span class="meta-value"><?= $serv['sort_order'] ?></span>
                                    </div>
                                    <div class="meta-row">
                                        <span class="meta-label">Created:</span>
                                        <span class="meta-value"><?= formatDate($serv['created_at']) ?></span>
                                    </div>
                                </div>
                            </td>
                            <td class="col-actions">
                                <div class="action-buttons">
                                    <a href="?action=edit&id=<?= $serv['id'] ?>"
                                       class="action-btn edit-btn"
                                       title="Edit Service">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="../service.php?slug=<?= $serv['slug'] ?>"
                                       target="_blank"
                                       class="action-btn view-btn"
                                       title="View Service">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <form method="POST" class="delete-form"
                                          onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?= $serv['id'] ?>">
                                        <button type="submit"
                                                class="action-btn delete-btn"
                                                title="Delete Service">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                        </table>
                    </div>

                    <!-- Enhanced Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <div class="table-footer">
                        <div class="pagination-info">
                            <span>Showing <?= (($page - 1) * $limit) + 1 ?> to <?= min($page * $limit, $total) ?> of <?= $total ?> services</span>
                        </div>
                        <nav class="modern-pagination" aria-label="Services pagination">
                            <div class="pagination-controls">
                                <?php if ($page > 1): ?>
                                <a href="?action=list&page=1<?= $search ? '&search=' . urlencode($search) : '' ?>"
                                   class="pagination-btn first-btn" title="First page">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                                <a href="?action=list&page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>"
                                   class="pagination-btn prev-btn" title="Previous page">
                                    <i class="fas fa-angle-left"></i>
                                    <span>Previous</span>
                                </a>
                                <?php endif; ?>

                                <div class="pagination-numbers">
                                    <?php
                                    $start = max(1, $page - 2);
                                    $end = min($totalPages, $page + 2);

                                    if ($start > 1): ?>
                                        <a href="?action=list&page=1<?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-number">1</a>
                                        <?php if ($start > 2): ?>
                                            <span class="pagination-ellipsis">...</span>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $start; $i <= $end; $i++): ?>
                                        <?php if ($i == $page): ?>
                                        <span class="pagination-number active"><?= $i ?></span>
                                        <?php else: ?>
                                        <a href="?action=list&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-number"><?= $i ?></a>
                                        <?php endif; ?>
                                    <?php endfor; ?>

                                    <?php if ($end < $totalPages): ?>
                                        <?php if ($end < $totalPages - 1): ?>
                                            <span class="pagination-ellipsis">...</span>
                                        <?php endif; ?>
                                        <a href="?action=list&page=<?= $totalPages ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-number"><?= $totalPages ?></a>
                                    <?php endif; ?>
                                </div>

                                <?php if ($page < $totalPages): ?>
                                <a href="?action=list&page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>"
                                   class="pagination-btn next-btn" title="Next page">
                                    <span>Next</span>
                                    <i class="fas fa-angle-right"></i>
                                </a>
                                <a href="?action=list&page=<?= $totalPages ?><?= $search ? '&search=' . urlencode($search) : '' ?>"
                                   class="pagination-btn last-btn" title="Last page">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </nav>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Enhanced Form Header -->
                <div class="form-header">
                    <div class="form-header-content">
                        <div class="form-title-section">
                            <h2 class="form-main-title">
                                <i class="fas fa-<?= $action === 'add' ? 'plus' : 'edit' ?> form-icon"></i>
                                <?= $action === 'add' ? 'Create New Service' : 'Edit Service' ?>
                            </h2>
                            <p class="form-subtitle">
                                <?= $action === 'add'
                                    ? 'Add a new service to your construction portfolio'
                                    : 'Update service information and settings' ?>
                            </p>
                        </div>
                        <div class="form-actions-header">
                            <a href="?action=list" class="btn btn-outline btn-large">
                                <i class="fas fa-arrow-left"></i>
                                <span>Back to Services</span>
                            </a>
                        </div>
                    </div>
                </div>

                <form method="POST" enctype="multipart/form-data" class="modern-service-form">
                    <?= getCSRFField() ?>
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="id" value="<?= $service['id'] ?>">
                    <?php endif; ?>

                    <!-- Service Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                Service Information
                            </h3>
                            <p class="section-description">Basic information about your service</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label for="title" class="field-label">
                                        <i class="fas fa-heading"></i>
                                        Service Title *
                                    </label>
                                    <input type="text"
                                           id="title"
                                           name="title"
                                           class="form-input"
                                           value="<?= htmlspecialchars($service['title'] ?? '') ?>"
                                           placeholder="Enter service title..."
                                           required>
                                    <div class="field-help">This will be the main title displayed for your service</div>
                                </div>

                                <div class="form-field">
                                    <label for="slug" class="field-label">
                                        <i class="fas fa-link"></i>
                                        URL Slug
                                    </label>
                                    <input type="text"
                                           id="slug"
                                           name="slug"
                                           class="form-input"
                                           value="<?= htmlspecialchars($service['slug'] ?? '') ?>"
                                           placeholder="auto-generated-from-title">
                                    <div class="field-help">Used in the service URL. Leave empty to auto-generate from title.</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="short_description" class="field-label">
                                        <i class="fas fa-align-left"></i>
                                        Short Description *
                                    </label>
                                    <textarea id="short_description"
                                              name="short_description"
                                              class="form-textarea"
                                              rows="3"
                                              placeholder="Brief description for service listings..."
                                              maxlength="200"
                                              required><?= htmlspecialchars($service['short_description'] ?? '') ?></textarea>
                                    <div class="char-count" data-target="short_description" data-max="200">
                                        <span class="count-text">0 / 200 characters</span>
                                        <div class="count-bar">
                                            <div class="count-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="field-help">Brief description shown in service listings (recommended: 150-200 characters)</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="description" class="field-label">
                                        <i class="fas fa-file-alt"></i>
                                        Full Description *
                                    </label>
                                    <textarea id="description"
                                              name="description"
                                              class="form-textarea"
                                              rows="8"
                                              placeholder="Detailed description of your service..."
                                              required><?= htmlspecialchars($service['description'] ?? '') ?></textarea>
                                    <div class="field-help">Detailed description shown on the service page. You can use HTML formatting.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-image"></i>
                                Featured Image
                            </h3>
                            <p class="section-description">Upload an image to represent your service</p>
                        </div>
                        <div class="section-content">
                            <?php if ($action === 'edit' && $service['featured_image']): ?>
                            <div class="current-image-preview">
                                <label class="preview-label">Current Image:</label>
                                <div class="image-preview-container">
                                    <img src="<?= UPLOAD_URL . '/' . $service['featured_image'] ?>"
                                         alt="Current featured image"
                                         class="current-image">
                                    <div class="image-overlay">
                                        <span class="image-info">Click to replace</span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="form-field">
                                <label for="featured_image" class="field-label">
                                    <i class="fas fa-upload"></i>
                                    <?= ($action === 'edit' && $service['featured_image']) ? 'Replace Image' : 'Upload Image' ?>
                                </label>
                                <div class="modern-file-upload">
                                    <input type="file"
                                           id="featured_image"
                                           name="featured_image"
                                           accept="image/*"
                                           class="file-input">
                                    <label for="featured_image" class="file-upload-area">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="upload-text">
                                            <strong>Choose a file</strong> or drag it here
                                        </div>
                                        <div class="upload-formats">
                                            Supported formats: JPG, PNG, GIF, WebP (Max: 10MB)
                                        </div>
                                    </label>
                                    <div class="file-preview" style="display: none;">
                                        <div class="preview-image"></div>
                                        <div class="preview-info">
                                            <span class="file-name"></span>
                                            <span class="file-size"></span>
                                        </div>
                                        <button type="button" class="remove-file">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="field-help">Upload a high-quality image that represents your service. Recommended size: 800x600px or larger.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings & SEO Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-cogs"></i>
                                Settings & SEO
                            </h3>
                            <p class="section-description">Configure service settings and search engine optimization</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label class="field-label">
                                        <i class="fas fa-star"></i>
                                        Service Options
                                    </label>
                                    <div class="checkbox-group">
                                        <label class="modern-checkbox">
                                            <input type="checkbox"
                                                   name="is_featured"
                                                   value="1"
                                                   <?= ($service['is_featured'] ?? 0) ? 'checked' : '' ?>>
                                            <span class="checkbox-mark"></span>
                                            <span class="checkbox-text">
                                                <strong>Featured Service</strong>
                                                <small>Display prominently on homepage and service listings</small>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-field">
                                    <label for="sort_order" class="field-label">
                                        <i class="fas fa-sort-numeric-down"></i>
                                        Sort Order
                                    </label>
                                    <input type="number"
                                           id="sort_order"
                                           name="sort_order"
                                           class="form-input"
                                           value="<?= $service['sort_order'] ?? 0 ?>"
                                           min="0"
                                           placeholder="0">
                                    <div class="field-help">Lower numbers appear first in listings</div>
                                </div>

                                <div class="form-field">
                                    <label for="meta_title" class="field-label">
                                        <i class="fas fa-tag"></i>
                                        Meta Title (SEO)
                                    </label>
                                    <input type="text"
                                           id="meta_title"
                                           name="meta_title"
                                           class="form-input"
                                           value="<?= htmlspecialchars($service['meta_title'] ?? '') ?>"
                                           maxlength="60"
                                           placeholder="SEO-optimized title for search engines">
                                    <div class="char-count" data-target="meta_title" data-max="60">
                                        <span class="count-text">0 / 60 characters</span>
                                        <div class="count-bar">
                                            <div class="count-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="field-help">Recommended: 50-60 characters for optimal search engine display</div>
                                </div>

                                <div class="form-field">
                                    <label for="meta_description" class="field-label">
                                        <i class="fas fa-file-text"></i>
                                        Meta Description (SEO)
                                    </label>
                                    <textarea id="meta_description"
                                              name="meta_description"
                                              class="form-textarea"
                                              rows="3"
                                              maxlength="160"
                                              placeholder="Brief description for search engine results..."><?= htmlspecialchars($service['meta_description'] ?? '') ?></textarea>
                                    <div class="char-count" data-target="meta_description" data-max="160">
                                        <span class="count-text">0 / 160 characters</span>
                                        <div class="count-bar">
                                            <div class="count-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="field-help">Recommended: 150-160 characters for optimal search engine display</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <div class="actions-container">
                            <div class="primary-actions">
                                <button type="submit" class="btn btn-success btn-large">
                                    <i class="fas fa-save"></i>
                                    <span><?= $action === 'add' ? 'Create Service' : 'Update Service' ?></span>
                                </button>
                            </div>
                            <div class="secondary-actions">
                                <a href="?action=list" class="btn btn-outline btn-large">
                                    <i class="fas fa-times"></i>
                                    <span>Cancel</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-generate slug from title
            const titleField = document.getElementById('title');
            const slugField = document.getElementById('slug');

            if (titleField && slugField) {
                titleField.addEventListener('input', function() {
                    if (!slugField.value || slugField.dataset.autoGenerated) {
                        const slug = this.value.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/[\s-]+/g, '-')
                            .replace(/^-+|-+$/g, '');
                        slugField.value = slug;
                        slugField.dataset.autoGenerated = 'true';
                    }
                });

                slugField.addEventListener('input', function() {
                    this.dataset.autoGenerated = 'false';
                });
            }

            // Character counters
            function initCharCounter(target, max) {
                const field = document.getElementById(target);
                const counter = document.querySelector(`[data-target="${target}"]`);

                if (field && counter) {
                    const countText = counter.querySelector('.count-text');
                    const countProgress = counter.querySelector('.count-progress');

                    function updateCounter() {
                        const length = field.value.length;
                        const percentage = (length / max) * 100;

                        countText.textContent = `${length} / ${max} characters`;
                        countProgress.style.width = `${Math.min(percentage, 100)}%`;

                        // Update counter styling based on length
                        counter.className = 'char-count';
                        if (length > max) {
                            counter.classList.add('over-limit');
                        } else if (length > max * 0.8) {
                            counter.classList.add('near-limit');
                        } else {
                            counter.classList.add('normal');
                        }
                    }

                    field.addEventListener('input', updateCounter);
                    updateCounter(); // Initial count
                }
            }

            // Initialize character counters
            initCharCounter('short_description', 200);
            initCharCounter('meta_title', 60);
            initCharCounter('meta_description', 160);

            // Modern file upload
            const fileInput = document.getElementById('featured_image');
            const fileUploadArea = document.querySelector('.file-upload-area');
            const filePreview = document.querySelector('.file-preview');

            if (fileInput && fileUploadArea) {
                // Drag and drop functionality
                fileUploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('drag-over');
                });

                fileUploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('drag-over');
                });

                fileUploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('drag-over');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        fileInput.files = files;
                        handleFileSelect(files[0]);
                    }
                });

                // File input change
                fileInput.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        handleFileSelect(e.target.files[0]);
                    }
                });

                // Handle file selection
                function handleFileSelect(file) {
                    if (file && file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewImage = filePreview.querySelector('.preview-image');
                            const fileName = filePreview.querySelector('.file-name');
                            const fileSize = filePreview.querySelector('.file-size');

                            previewImage.style.backgroundImage = `url(${e.target.result})`;
                            fileName.textContent = file.name;
                            fileSize.textContent = formatFileSize(file.size);

                            fileUploadArea.style.display = 'none';
                            filePreview.style.display = 'flex';
                        };
                        reader.readAsDataURL(file);
                    }
                }

                // Remove file
                const removeFileBtn = filePreview.querySelector('.remove-file');
                if (removeFileBtn) {
                    removeFileBtn.addEventListener('click', function() {
                        fileInput.value = '';
                        fileUploadArea.style.display = 'flex';
                        filePreview.style.display = 'none';
                    });
                }

                // Format file size
                function formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            }

            // Enhanced form validation
            const form = document.querySelector('.modern-service-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.classList.add('error');
                            isValid = false;
                        } else {
                            field.classList.remove('error');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        // Scroll to first error
                        const firstError = form.querySelector('.error');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            firstError.focus();
                        }
                    }
                });
            }

            // View toggle functionality (for future grid view)
            const viewToggles = document.querySelectorAll('.view-toggle');
            viewToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    viewToggles.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    const view = this.dataset.view;
                    // Future implementation for grid/table view switching
                    console.log('Switching to view:', view);
                });
            });
        });
    </script>
</body>
</html>
