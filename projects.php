<?php
require_once 'config/config.php';

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get filter parameters
$type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$where = ['is_active = 1'];
$params = [];

if ($type && in_array($type, ['completed', 'ongoing'])) {
    $where[] = 'project_type = ?';
    $params[] = $type;
}

if ($search) {
    $where[] = '(title LIKE ? OR description LIKE ? OR location LIKE ?)';
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

$whereClause = implode(' AND ', $where);

// Get total count
$totalQuery = "SELECT COUNT(*) as total FROM projects WHERE $whereClause";
$totalResult = $db->fetchOne($totalQuery, $params);
$total = $totalResult['total'];

// Get projects
$projectsQuery = "SELECT * FROM projects WHERE $whereClause ORDER BY sort_order ASC, created_at DESC LIMIT $limit OFFSET $offset";
$projects = $db->fetchAll($projectsQuery, $params);

// Calculate pagination
$totalPages = ceil($total / $limit);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Projects<?= $type ? ' - ' . ucfirst($type) : '' ?> - <?= SITE_NAME ?></title>
    <meta name="description" content="Explore our portfolio of construction projects including completed and ongoing developments across London.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?= SITE_PHONE ?></span>
                    <span><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></span>
                </div>
                <div class="social-links">
                    <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                    <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>

        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="about.php">About Us</a></li>
                    <li><a href="services.php">Our Services</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle active">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed" <?= $type === 'completed' ? 'class="active"' : '' ?>>Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing" <?= $type === 'ongoing' ? 'class="active"' : '' ?>>Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="contact.php">Contact Us</a></li>
                </ul>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Our Projects<?= $type ? ' - ' . ucfirst($type) : '' ?></h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Our Projects</span>
                <?php if ($type): ?>
                <span>/</span>
                <span><?= ucfirst($type) ?></span>
                <?php endif; ?>
            </nav>
        </div>
    </section>

    <!-- Projects Intro -->
    <section class="projects-intro">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Our Portfolio</h2>
                <p class="section-subtitle">Every project is unique and custom made to meet our clients' specific requirements and vision.</p>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="filter-section">
        <div class="container">
            <div class="filter-controls">
                <div class="filter-tabs">
                    <a href="projects.php" class="filter-tab <?= !$type ? 'active' : '' ?>">All Projects</a>
                    <a href="projects.php?type=completed" class="filter-tab <?= $type === 'completed' ? 'active' : '' ?>">Completed</a>
                    <a href="projects.php?type=ongoing" class="filter-tab <?= $type === 'ongoing' ? 'active' : '' ?>">Ongoing</a>
                </div>

                <form method="GET" class="search-form">
                    <?php if ($type): ?>
                    <input type="hidden" name="type" value="<?= htmlspecialchars($type) ?>">
                    <?php endif; ?>
                    <div class="search-input-group">
                        <input type="text" name="search" placeholder="Search projects..." value="<?= htmlspecialchars($search) ?>" class="search-input">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <?php if ($search): ?>
            <div class="search-results-info">
                <p>Showing <?= $total ?> result(s) for "<?= htmlspecialchars($search) ?>"</p>
                <a href="projects.php<?= $type ? '?type=' . $type : '' ?>" class="clear-search">Clear search</a>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Projects Grid -->
    <section class="projects-grid-section">
        <div class="container">
            <?php if (empty($projects)): ?>
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No projects found</h3>
                <p>Try adjusting your search criteria or browse all our projects.</p>
                <a href="projects.php" class="btn btn-primary">View All Projects</a>
            </div>
            <?php else: ?>
            <div class="projects-grid">
                <?php foreach ($projects as $project): ?>
                <div class="project-card">
                    <div class="project-image">
                        <img src="<?= $project['featured_image'] ? UPLOAD_URL . '/' . $project['featured_image'] : ASSETS_URL . '/images/project-default.jpg' ?>"
                             alt="<?= htmlspecialchars($project['title']) ?>">
                        <div class="project-overlay">
                            <div class="project-info">
                                <span class="project-type"><?= ucfirst($project['project_type']) ?></span>
                                <h3><?= htmlspecialchars($project['title']) ?></h3>
                                <?php if ($project['location']): ?>
                                <p class="project-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?= htmlspecialchars($project['location']) ?>
                                </p>
                                <?php endif; ?>
                                <a href="project.php?slug=<?= $project['slug'] ?>" class="btn btn-primary">View Details</a>
                            </div>
                        </div>
                    </div>
                    <div class="project-content">
                        <div class="project-meta">
                            <span class="project-status status-<?= $project['project_type'] ?>">
                                <?= ucfirst($project['project_type']) ?>
                            </span>
                            <?php if ($project['completion_date']): ?>
                            <span class="project-date">
                                <i class="fas fa-calendar"></i>
                                <?= formatDate($project['completion_date']) ?>
                            </span>
                            <?php endif; ?>
                        </div>
                        <h3><a href="project.php?slug=<?= $project['slug'] ?>"><?= htmlspecialchars($project['title']) ?></a></h3>
                        <p><?= htmlspecialchars(substr($project['description'], 0, 120)) ?>...</p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?= $page - 1 ?><?= $type ? '&type=' . $type : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
                <?php endif; ?>

                <div class="pagination-numbers">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $page): ?>
                        <span class="pagination-number active"><?= $i ?></span>
                        <?php else: ?>
                        <a href="?page=<?= $i ?><?= $type ? '&type=' . $type : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-number"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>

                <?php if ($page < $totalPages): ?>
                <a href="?page=<?= $page + 1 ?><?= $type ? '&type=' . $type : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-btn">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Have a Project in Mind?</h2>
                <p>Let's discuss how we can bring your construction vision to life with our expertise and dedication.</p>
                <div class="cta-buttons">
                    <a href="contact.php" class="btn btn-primary">Start Your Project</a>
                    <a href="services.php" class="btn btn-outline">Our Services</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <img src="<?= ASSETS_URL ?>/images/logo-white.png" alt="<?= SITE_NAME ?>" class="footer-logo">
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="social-links">
                        <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="projects.php">Our Projects</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="service.php?slug=civil-engineering">Civil Engineering</a></li>
                        <li><a href="service.php?slug=groundworks">Groundworks</a></li>
                        <li><a href="service.php?slug=rc-frames">RC Frames</a></li>
                        <li><a href="service.php?slug=basements">Basements</a></li>
                        <li><a href="service.php?slug=hard-landscaping">Hard Landscaping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> <?= SITE_ADDRESS ?></p>
                        <p><i class="fas fa-phone"></i> <?= SITE_PHONE ?></p>
                        <p><i class="fas fa-mobile-alt"></i> <?= SITE_MOBILE ?></p>
                        <p><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
</body>
</html>
