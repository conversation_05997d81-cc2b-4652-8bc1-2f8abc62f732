<?php
/**
 * Mobile Navigation Test Page
 * Tests mobile navigation functionality and touch interactions
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Navigation Test - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-button {
            display: inline-block;
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
        }
        
        .test-button:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }
        
        .test-button.success {
            background: #27ae60;
        }
        
        .test-button.warning {
            background: #f39c12;
        }
        
        .test-button.danger {
            background: #e74c3c;
        }
        
        .touch-test-area {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            margin: 1rem 0;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            transition: all 0.3s ease;
        }
        
        .touch-test-area.touched {
            background: #e8f5e8;
            border-color: #27ae60;
        }
        
        .navigation-status {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #3498db;
        }
        
        .test-results {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <div class="test-section">
                    <h2>
                        <i class="fas fa-mobile-alt"></i>
                        Mobile Navigation Test
                    </h2>
                    
                    <div class="navigation-status">
                        <h3>Navigation Status:</h3>
                        <p id="navStatus">Testing navigation functionality...</p>
                    </div>
                </div>

                <!-- Sidebar Toggle Test -->
                <div class="test-section">
                    <h3>Sidebar Toggle Test</h3>
                    <p>Test the sidebar toggle functionality on mobile devices:</p>
                    
                    <button type="button" class="test-button" onclick="testSidebarToggle()">
                        <i class="fas fa-bars"></i> Toggle Sidebar
                    </button>
                    
                    <button type="button" class="test-button success" onclick="testSidebarOpen()">
                        <i class="fas fa-arrow-right"></i> Open Sidebar
                    </button>
                    
                    <button type="button" class="test-button warning" onclick="testSidebarClose()">
                        <i class="fas fa-arrow-left"></i> Close Sidebar
                    </button>
                    
                    <div class="test-results" id="sidebarTestResults">
                        Sidebar test results will appear here...
                    </div>
                </div>

                <!-- Touch Interaction Test -->
                <div class="test-section">
                    <h3>Touch Interaction Test</h3>
                    <p>Test touch events and gestures:</p>
                    
                    <div class="touch-test-area" id="touchTestArea">
                        <i class="fas fa-hand-pointer" style="font-size: 2rem; color: #6c757d; margin-bottom: 1rem;"></i>
                        <p>Touch or click this area to test touch events</p>
                        <small>Touch count: <span id="touchCount">0</span></small>
                    </div>
                    
                    <div class="test-results" id="touchTestResults">
                        Touch test results will appear here...
                    </div>
                </div>

                <!-- Dropdown Menu Test -->
                <div class="test-section">
                    <h3>Dropdown Menu Test</h3>
                    <p>Test dropdown functionality on mobile:</p>
                    
                    <div class="dropdown" style="display: inline-block;">
                        <button class="test-button dropdown-toggle" aria-label="Test dropdown" aria-expanded="false">
                            <i class="fas fa-chevron-down"></i> Test Dropdown
                        </button>
                        <div class="dropdown-menu" role="menu">
                            <a href="#" role="menuitem" onclick="logTest('Dropdown item 1 clicked')">
                                <i class="fas fa-home"></i> Menu Item 1
                            </a>
                            <a href="#" role="menuitem" onclick="logTest('Dropdown item 2 clicked')">
                                <i class="fas fa-user"></i> Menu Item 2
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" role="menuitem" onclick="logTest('Dropdown item 3 clicked')">
                                <i class="fas fa-cog"></i> Menu Item 3
                            </a>
                        </div>
                    </div>
                    
                    <div class="test-results" id="dropdownTestResults">
                        Dropdown test results will appear here...
                    </div>
                </div>

                <!-- Swipe Gesture Test -->
                <div class="test-section">
                    <h3>Swipe Gesture Test</h3>
                    <p>Test swipe gestures for navigation:</p>
                    
                    <div class="touch-test-area" id="swipeTestArea">
                        <i class="fas fa-arrows-alt-h" style="font-size: 2rem; color: #6c757d; margin-bottom: 1rem;"></i>
                        <p>Swipe left or right in this area</p>
                        <small>Last swipe: <span id="lastSwipe">None</span></small>
                    </div>
                    
                    <div class="test-results" id="swipeTestResults">
                        Swipe test results will appear here...
                    </div>
                </div>

                <!-- Keyboard Navigation Test -->
                <div class="test-section">
                    <h3>Keyboard Navigation Test</h3>
                    <p>Test keyboard accessibility:</p>
                    
                    <div style="margin: 1rem 0;">
                        <button class="test-button" onclick="testKeyboardNav()">
                            Test Tab Navigation
                        </button>
                        <button class="test-button success" onclick="testEscapeKey()">
                            Test Escape Key
                        </button>
                        <button class="test-button warning" onclick="testEnterKey()">
                            Test Enter Key
                        </button>
                    </div>
                    
                    <div class="test-results" id="keyboardTestResults">
                        Keyboard test results will appear here...
                    </div>
                </div>

                <!-- Performance Test -->
                <div class="test-section">
                    <h3>Performance Test</h3>
                    <p>Test animation performance and responsiveness:</p>
                    
                    <button class="test-button" onclick="testAnimationPerformance()">
                        <i class="fas fa-tachometer-alt"></i> Test Animation Performance
                    </button>
                    
                    <button class="test-button success" onclick="testScrollPerformance()">
                        <i class="fas fa-scroll"></i> Test Scroll Performance
                    </button>
                    
                    <div class="test-results" id="performanceTestResults">
                        Performance test results will appear here...
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        let touchCount = 0;
        let testResults = [];
        
        // Utility function to log test results
        function logTest(message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push(`[${timestamp}] ${message}`);
            updateTestResults();
        }
        
        function updateTestResults() {
            const elements = ['sidebarTestResults', 'touchTestResults', 'dropdownTestResults', 
                            'swipeTestResults', 'keyboardTestResults', 'performanceTestResults'];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.innerHTML = testResults.slice(-10).join('\n');
                }
            });
        }
        
        // Sidebar tests
        function testSidebarToggle() {
            const sidebar = document.querySelector('.admin-sidebar');
            const toggle = document.querySelector('.sidebar-toggle');
            
            if (toggle) {
                toggle.click();
                logTest('Sidebar toggle clicked');
                
                setTimeout(() => {
                    const isOpen = sidebar.classList.contains('show');
                    logTest(`Sidebar is now: ${isOpen ? 'OPEN' : 'CLOSED'}`);
                }, 300);
            } else {
                logTest('ERROR: Sidebar toggle not found');
            }
        }
        
        function testSidebarOpen() {
            const sidebar = document.querySelector('.admin-sidebar');
            if (sidebar && !sidebar.classList.contains('show')) {
                testSidebarToggle();
            } else {
                logTest('Sidebar is already open');
            }
        }
        
        function testSidebarClose() {
            const sidebar = document.querySelector('.admin-sidebar');
            if (sidebar && sidebar.classList.contains('show')) {
                testSidebarToggle();
            } else {
                logTest('Sidebar is already closed');
            }
        }
        
        // Touch tests
        function initTouchTests() {
            const touchArea = document.getElementById('touchTestArea');
            const swipeArea = document.getElementById('swipeTestArea');
            
            // Touch events
            touchArea.addEventListener('touchstart', handleTouchStart);
            touchArea.addEventListener('touchend', handleTouchEnd);
            touchArea.addEventListener('click', handleClick);
            
            // Swipe events
            let startX = 0;
            let startY = 0;
            
            swipeArea.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
                logTest('Swipe started');
            });
            
            swipeArea.addEventListener('touchend', (e) => {
                const endX = e.changedTouches[0].clientX;
                const endY = e.changedTouches[0].clientY;
                const deltaX = endX - startX;
                const deltaY = endY - startY;
                
                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                    const direction = deltaX > 0 ? 'RIGHT' : 'LEFT';
                    document.getElementById('lastSwipe').textContent = direction;
                    logTest(`Swipe detected: ${direction} (${Math.abs(deltaX)}px)`);
                }
            });
        }
        
        function handleTouchStart(e) {
            e.target.classList.add('touched');
            logTest('Touch started');
        }
        
        function handleTouchEnd(e) {
            setTimeout(() => {
                e.target.classList.remove('touched');
            }, 200);
            logTest('Touch ended');
        }
        
        function handleClick(e) {
            touchCount++;
            document.getElementById('touchCount').textContent = touchCount;
            logTest(`Touch/Click event #${touchCount}`);
        }
        
        // Keyboard tests
        function testKeyboardNav() {
            logTest('Testing keyboard navigation...');
            const focusableElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]');
            logTest(`Found ${focusableElements.length} focusable elements`);
            
            if (focusableElements.length > 0) {
                focusableElements[0].focus();
                logTest('Focused first element');
            }
        }
        
        function testEscapeKey() {
            logTest('Testing Escape key...');
            const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
            document.dispatchEvent(escapeEvent);
            logTest('Escape key event dispatched');
        }
        
        function testEnterKey() {
            logTest('Testing Enter key...');
            const enterEvent = new KeyboardEvent('keydown', { key: 'Enter' });
            document.dispatchEvent(enterEvent);
            logTest('Enter key event dispatched');
        }
        
        // Performance tests
        function testAnimationPerformance() {
            logTest('Testing animation performance...');
            const startTime = performance.now();
            
            // Trigger multiple animations
            const cards = document.querySelectorAll('.test-section');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.transform = 'translateY(-5px)';
                    setTimeout(() => {
                        card.style.transform = 'translateY(0)';
                    }, 200);
                }, index * 50);
            });
            
            setTimeout(() => {
                const endTime = performance.now();
                logTest(`Animation test completed in ${(endTime - startTime).toFixed(2)}ms`);
            }, 1000);
        }
        
        function testScrollPerformance() {
            logTest('Testing scroll performance...');
            const startTime = performance.now();
            
            // Smooth scroll to bottom and back
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
            
            setTimeout(() => {
                window.scrollTo({ top: 0, behavior: 'smooth' });
                const endTime = performance.now();
                logTest(`Scroll test completed in ${(endTime - startTime).toFixed(2)}ms`);
            }, 1000);
        }
        
        // Navigation status
        function updateNavigationStatus() {
            const width = window.innerWidth;
            const isMobile = width <= 768;
            const sidebarVisible = !document.querySelector('.admin-sidebar').classList.contains('show');
            
            document.getElementById('navStatus').innerHTML = `
                Screen width: ${width}px<br>
                Mobile mode: ${isMobile ? 'YES' : 'NO'}<br>
                Sidebar visible: ${sidebarVisible ? 'YES' : 'NO'}
            `;
        }
        
        // Initialize tests
        document.addEventListener('DOMContentLoaded', function() {
            initTouchTests();
            updateNavigationStatus();
            logTest('Mobile navigation test initialized');
            
            // Update status on resize
            window.addEventListener('resize', updateNavigationStatus);
        });
    </script>
</body>
</html>
