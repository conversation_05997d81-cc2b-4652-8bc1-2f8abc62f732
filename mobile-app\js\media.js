/**
 * Media Manager for Flori Construction Mobile App
 * Handles media upload, management, and gallery functionality
 */

class MediaManager {
    constructor() {
        this.media = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.filters = {
            type: '',
            search: ''
        };
        this.selectedFiles = [];

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkCameraSupport();
    }

    checkCameraSupport() {
        if ('mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices) {
            this.cameraSupported = true;
        } else {
            this.cameraSupported = false;
            console.log('Camera not supported on this device');
        }
    }

    setupEventListeners() {
        // Upload media button
        const uploadBtn = document.getElementById('upload-media-btn');
        uploadBtn?.addEventListener('click', () => {
            this.showUploadModal();
        });

        // Media type filter
        const typeFilter = document.getElementById('media-type-filter');
        typeFilter?.addEventListener('change', (e) => {
            this.filters.type = e.target.value;
            this.currentPage = 1;
            this.load();
        });

        // File input change
        const fileInput = document.getElementById('file-input');
        fileInput?.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // Drag and drop support
        this.setupDragAndDrop();
    }

    setupDragAndDrop() {
        const mediaGrid = document.getElementById('media-grid');
        if (!mediaGrid) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            mediaGrid.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            mediaGrid.addEventListener(eventName, () => {
                mediaGrid.classList.add('drag-over');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            mediaGrid.addEventListener(eventName, () => {
                mediaGrid.classList.remove('drag-over');
            }, false);
        });

        mediaGrid.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleFileSelection(files);
        }, false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    async load() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 20,
                ...this.filters
            });

            const data = await window.floriAdmin.apiRequest(`mobile.php?action=media&${params}`);

            if (data && data.success) {
                this.media = data.data.media;
                this.updatePagination(data.data.pagination);
                this.renderMedia();
            } else {
                window.floriAdmin.showToast('Failed to load media', 'error');
            }
        } catch (error) {
            console.error('Failed to load media:', error);
            window.floriAdmin.showToast('Failed to load media', 'error');
        }
    }

    renderMedia() {
        const container = document.getElementById('media-grid');
        if (!container) return;

        if (this.media.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-images"></i>
                    <h3>No media files found</h3>
                    <p>Upload your first media file to get started</p>
                    <button class="btn btn-primary" onclick="window.MediaManager.showUploadModal()">
                        <i class="fas fa-upload"></i> Upload Media
                    </button>
                </div>
            `;
            return;
        }

        const html = this.media.map(item => `
            <div class="media-item" data-id="${item.id}">
                <div class="media-preview">
                    ${this.getMediaPreview(item)}
                    <div class="media-overlay">
                        <div class="media-actions">
                            <button class="btn btn-sm btn-ghost" onclick="window.MediaManager.viewMedia(${item.id})" title="View">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-ghost" onclick="window.MediaManager.editMedia(${item.id})" title="Edit">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-danger" onclick="window.MediaManager.deleteMedia(${item.id})" title="Delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="media-info">
                    <h4>${window.floriAdmin.escapeHtml(item.original_name)}</h4>
                    <p class="media-meta">
                        <span class="file-size">${window.floriAdmin.formatFileSize(item.file_size)}</span>
                        <span class="upload-date">${window.floriAdmin.formatDate(item.created_at)}</span>
                    </p>
                    ${item.alt_text ? `<p class="alt-text">${window.floriAdmin.escapeHtml(item.alt_text)}</p>` : ''}
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    getMediaPreview(item) {
        if (item.file_type.startsWith('image/')) {
            return `<img src="../${item.file_path}" alt="${window.floriAdmin.escapeHtml(item.alt_text || item.original_name)}" loading="lazy">`;
        } else if (item.file_type.startsWith('video/')) {
            return `
                <video preload="metadata">
                    <source src="../${item.file_path}" type="${item.file_type}">
                </video>
                <div class="video-overlay">
                    <i class="fas fa-play"></i>
                </div>
            `;
        } else {
            return `
                <div class="file-preview">
                    <i class="fas fa-file"></i>
                    <span>${this.getFileExtension(item.original_name)}</span>
                </div>
            `;
        }
    }

    getFileExtension(filename) {
        return filename.split('.').pop().toUpperCase();
    }

    updatePagination(pagination) {
        this.totalPages = pagination.pages;
        const container = document.getElementById('media-pagination');
        if (!container) return;

        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }

        let html = '<div class="pagination-controls">';

        // Previous button
        if (pagination.page > 1) {
            html += `<button class="btn btn-sm" onclick="window.MediaManager.goToPage(${pagination.page - 1})">
                <i class="fas fa-chevron-left"></i> Previous
            </button>`;
        }

        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const active = i === pagination.page ? 'active' : '';
            html += `<button class="btn btn-sm ${active}" onclick="window.MediaManager.goToPage(${i})">${i}</button>`;
        }

        // Next button
        if (pagination.page < pagination.pages) {
            html += `<button class="btn btn-sm" onclick="window.MediaManager.goToPage(${pagination.page + 1})">
                Next <i class="fas fa-chevron-right"></i>
            </button>`;
        }

        html += '</div>';
        container.innerHTML = html;
    }

    goToPage(page) {
        this.currentPage = page;
        this.load();
    }

    showUploadModal() {
        const modalContent = `
            <div class="modal-header">
                <h2>Upload Media</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="upload-options">
                <button type="button" class="btn btn-primary upload-option" onclick="document.getElementById('file-input').click()">
                    <i class="fas fa-folder-open"></i>
                    <span>Browse Files</span>
                </button>

                ${this.cameraSupported ? `
                    <button type="button" class="btn btn-primary upload-option" onclick="window.MediaManager.openCamera()">
                        <i class="fas fa-camera"></i>
                        <span>Take Photo</span>
                    </button>
                ` : ''}
            </div>

            <div class="upload-area" id="upload-area">
                <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <h3>Drop files here or click to browse</h3>
                    <p>Supported formats: JPG, PNG, GIF, WebP, MP4</p>
                </div>
            </div>

            <div id="upload-queue" class="upload-queue" style="display: none;">
                <h3>Upload Queue</h3>
                <div id="upload-items"></div>

                <div class="upload-actions">
                    <button type="button" class="btn btn-ghost" onclick="window.MediaManager.clearQueue()">Clear Queue</button>
                    <button type="button" class="btn btn-primary" onclick="window.MediaManager.startUpload()">
                        <i class="fas fa-upload"></i> Start Upload
                    </button>
                </div>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);

        // Setup drag and drop for upload area
        const uploadArea = document.getElementById('upload-area');
        if (uploadArea) {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, this.preventDefaults, false);
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => {
                    uploadArea.classList.add('drag-over');
                }, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                uploadArea.addEventListener(eventName, () => {
                    uploadArea.classList.remove('drag-over');
                }, false);
            });

            uploadArea.addEventListener('drop', (e) => {
                const files = e.dataTransfer.files;
                this.handleFileSelection(files);
            }, false);

            uploadArea.addEventListener('click', () => {
                document.getElementById('file-input').click();
            });
        }
    }

    handleFileSelection(files) {
        if (!files || files.length === 0) return;

        const validFiles = Array.from(files).filter(file => {
            const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'video/mp4'];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (!validTypes.includes(file.type)) {
                window.floriAdmin.showToast(`Invalid file type: ${file.name}`, 'error');
                return false;
            }

            if (file.size > maxSize) {
                window.floriAdmin.showToast(`File too large: ${file.name}`, 'error');
                return false;
            }

            return true;
        });

        if (validFiles.length === 0) return;

        this.selectedFiles = [...this.selectedFiles, ...validFiles];
        this.renderUploadQueue();
    }

    renderUploadQueue() {
        const queueContainer = document.getElementById('upload-queue');
        const itemsContainer = document.getElementById('upload-items');

        if (!queueContainer || !itemsContainer) return;

        if (this.selectedFiles.length === 0) {
            queueContainer.style.display = 'none';
            return;
        }

        queueContainer.style.display = 'block';

        const html = this.selectedFiles.map((file, index) => `
            <div class="upload-item" data-index="${index}">
                <div class="file-preview">
                    ${file.type.startsWith('image/') ?
                `<img src="${URL.createObjectURL(file)}" alt="${file.name}">` :
                `<i class="fas fa-file"></i>`
            }
                </div>
                <div class="file-info">
                    <h4>${window.floriAdmin.escapeHtml(file.name)}</h4>
                    <p>${window.floriAdmin.formatFileSize(file.size)}</p>
                    <div class="upload-progress">
                        <div class="progress-bar" style="width: 0%"></div>
                    </div>
                </div>
                <div class="file-actions">
                    <button type="button" class="btn btn-sm btn-danger" onclick="window.MediaManager.removeFromQueue(${index})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `).join('');

        itemsContainer.innerHTML = html;
    }

    removeFromQueue(index) {
        this.selectedFiles.splice(index, 1);
        this.renderUploadQueue();
    }

    clearQueue() {
        this.selectedFiles = [];
        this.renderUploadQueue();
    }

    async startUpload() {
        if (this.selectedFiles.length === 0) return;

        const uploadPromises = this.selectedFiles.map((file, index) => {
            return this.uploadFile(file, index);
        });

        try {
            await Promise.all(uploadPromises);
            window.floriAdmin.showToast('All files uploaded successfully', 'success');
            window.floriAdmin.closeModal();
            this.selectedFiles = [];
            this.load(); // Reload media list
        } catch (error) {
            console.error('Upload failed:', error);
            window.floriAdmin.showToast('Some uploads failed', 'error');
        }
    }

    async uploadFile(file, index) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('alt_text', '');
        formData.append('caption', '');

        const progressBar = document.querySelector(`[data-index="${index}"] .progress-bar`);

        try {
            const response = await fetch(`${window.floriAdmin.apiBase}/mobile.php?action=upload`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${window.floriAdmin.token}`
                },
                body: formData
            });

            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.style.backgroundColor = '#4CAF50';
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Upload failed');
            }

            return data;
        } catch (error) {
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.style.backgroundColor = '#f44336';
            }
            throw error;
        }
    }

    async openCamera() {
        if (!this.cameraSupported) {
            window.floriAdmin.showToast('Camera not supported on this device', 'error');
            return;
        }

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: 'environment',
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            });

            this.showCameraModal(stream);

        } catch (error) {
            console.error('Camera access denied:', error);
            window.floriAdmin.showToast('Camera access denied', 'error');
        }
    }

    showCameraModal(stream) {
        const modalContent = `
            <div class="camera-modal">
                <div class="modal-header">
                    <h2>Take Photo</h2>
                    <button class="modal-close" onclick="window.MediaManager.closeCamera()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="camera-container">
                    <video id="camera-video" autoplay playsinline></video>
                    <canvas id="camera-canvas" style="display: none;"></canvas>
                </div>

                <div class="camera-controls">
                    <button class="btn btn-ghost" onclick="window.MediaManager.closeCamera()">
                        <i class="fas fa-times"></i> Cancel
                    </button>
                    <button class="btn btn-primary camera-capture" onclick="window.MediaManager.capturePhoto()">
                        <i class="fas fa-camera"></i> Capture
                    </button>
                    <button class="btn btn-ghost camera-switch" onclick="window.MediaManager.switchCamera()" style="display: none;">
                        <i class="fas fa-sync-alt"></i> Switch
                    </button>
                </div>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);

        const video = document.getElementById('camera-video');
        if (video) {
            video.srcObject = stream;
            this.currentStream = stream;
        }
    }

    capturePhoto() {
        const video = document.getElementById('camera-video');
        const canvas = document.getElementById('camera-canvas');

        if (!video || !canvas) return;

        const context = canvas.getContext('2d');
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        context.drawImage(video, 0, 0);

        canvas.toBlob((blob) => {
            const file = new File([blob], `photo_${Date.now()}.jpg`, { type: 'image/jpeg' });
            this.closeCamera();
            this.handleFileSelection([file]);
            this.showUploadModal(); // Show upload modal with captured photo
        }, 'image/jpeg', 0.9);
    }

    closeCamera() {
        if (this.currentStream) {
            this.currentStream.getTracks().forEach(track => track.stop());
            this.currentStream = null;
        }
        window.floriAdmin.closeModal();
    }

    async viewMedia(mediaId) {
        const media = this.media.find(item => item.id == mediaId);
        if (!media) return;

        const isImage = media.file_type.startsWith('image/');
        const fileUrl = `../${media.file_path}`;

        const modalContent = `
            <div class="media-viewer">
                <div class="modal-header">
                    <h2>${window.floriAdmin.escapeHtml(media.original_name)}</h2>
                    <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="media-preview">
                    ${isImage ?
                `<img src="${fileUrl}" alt="${window.floriAdmin.escapeHtml(media.alt_text || media.original_name)}">` :
                `<video src="${fileUrl}" controls></video>`
            }
                </div>

                <div class="media-details">
                    <p><strong>File Size:</strong> ${window.floriAdmin.formatFileSize(media.file_size)}</p>
                    <p><strong>Type:</strong> ${media.file_type}</p>
                    <p><strong>Uploaded:</strong> ${window.floriAdmin.formatDate(media.created_at)}</p>
                    ${media.alt_text ? `<p><strong>Alt Text:</strong> ${window.floriAdmin.escapeHtml(media.alt_text)}</p>` : ''}
                    ${media.caption ? `<p><strong>Caption:</strong> ${window.floriAdmin.escapeHtml(media.caption)}</p>` : ''}
                </div>

                <div class="modal-actions">
                    <button class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">Close</button>
                    <button class="btn btn-primary" onclick="window.MediaManager.editMedia(${mediaId})">Edit</button>
                </div>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);
    }

    async editMedia(mediaId) {
        const media = this.media.find(item => item.id == mediaId);
        if (!media) return;

        const modalContent = `
            <div class="media-editor">
                <div class="modal-header">
                    <h2>Edit Media</h2>
                    <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="edit-media-form">
                    <input type="hidden" name="id" value="${media.id}">

                    <div class="form-group">
                        <label for="edit-alt-text">Alt Text</label>
                        <input type="text" id="edit-alt-text" name="alt_text" value="${window.floriAdmin.escapeHtml(media.alt_text || '')}" placeholder="Describe this image...">
                    </div>

                    <div class="form-group">
                        <label for="edit-caption">Caption</label>
                        <textarea id="edit-caption" name="caption" placeholder="Optional caption...">${window.floriAdmin.escapeHtml(media.caption || '')}</textarea>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        `;

        window.floriAdmin.showModal(modalContent);

        // Setup form submission
        const form = document.getElementById('edit-media-form');
        form?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveMediaChanges(new FormData(form));
        });
    }

    async saveMediaChanges(formData) {
        try {
            const data = {
                id: formData.get('id'),
                alt_text: formData.get('alt_text'),
                caption: formData.get('caption')
            };

            const response = await window.floriAdmin.apiRequest('mobile.php?action=media', {
                method: 'PUT',
                body: JSON.stringify(data)
            });

            if (response && response.success) {
                window.floriAdmin.showToast('Media updated successfully', 'success');
                window.floriAdmin.closeModal();
                this.load(); // Refresh the grid
            } else {
                throw new Error(response?.error || 'Failed to update media');
            }

        } catch (error) {
            console.error('Failed to update media:', error);
            window.floriAdmin.showToast('Failed to update media', 'error');
        }
    }

    async deleteMedia(mediaId) {
        if (!confirm('Are you sure you want to delete this media file?')) {
            return;
        }

        try {
            const data = await window.floriAdmin.apiRequest(`mobile.php?action=media&id=${mediaId}`, {
                method: 'DELETE'
            });

            if (data && data.success) {
                window.floriAdmin.showToast('Media deleted successfully', 'success');
                this.load();
            } else {
                window.floriAdmin.showToast('Failed to delete media', 'error');
            }
        } catch (error) {
            console.error('Failed to delete media:', error);
            window.floriAdmin.showToast('Failed to delete media', 'error');
        }
    }
}

// Initialize Media Manager
window.MediaManager = new MediaManager();
