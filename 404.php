<?php
require_once 'config/config.php';

// Set 404 header
http_response_code(404);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - <?= SITE_NAME ?></title>
    <meta name="description" content="The page you are looking for could not be found. Return to our homepage or browse our services and projects.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .error-page {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .error-content {
            max-width: 600px;
            width: 100%;
        }
        
        .error-code {
            font-size: 120px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .error-title {
            font-size: 36px;
            font-weight: 600;
            margin-bottom: 20px;
            color: #fff;
        }
        
        .error-description {
            font-size: 18px;
            margin-bottom: 40px;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .error-btn {
            display: inline-block;
            padding: 15px 30px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
        }
        
        .error-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
            color: white;
        }
        
        .error-btn.primary {
            background: #3498db;
            border-color: #3498db;
        }
        
        .error-btn.primary:hover {
            background: #2980b9;
            border-color: #2980b9;
        }
        
        .error-icon {
            font-size: 80px;
            margin-bottom: 30px;
            opacity: 0.7;
        }
        
        .search-form {
            max-width: 400px;
            margin: 30px auto;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            background: rgba(255,255,255,0.9);
            color: #333;
        }
        
        .search-input::placeholder {
            color: #666;
        }
        
        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #3498db;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 50px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .search-btn:hover {
            background: #2980b9;
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 80px;
            }
            
            .error-title {
                font-size: 28px;
            }
            
            .error-description {
                font-size: 16px;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .error-btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="error-page">
        <div class="error-content">
            <div class="error-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            
            <div class="error-code">404</div>
            
            <h1 class="error-title">Page Not Found</h1>
            
            <p class="error-description">
                Sorry, the page you are looking for could not be found. It might have been moved, deleted, or you entered the wrong URL.
            </p>
            
            <!-- Search Form -->
            <form class="search-form" action="search.php" method="GET">
                <input type="text" name="q" class="search-input" placeholder="Search our website..." required>
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </form>
            
            <div class="error-actions">
                <a href="index.php" class="error-btn primary">
                    <i class="fas fa-home"></i>
                    Go Home
                </a>
                <a href="services.php" class="error-btn">
                    <i class="fas fa-tools"></i>
                    Our Services
                </a>
                <a href="projects.php" class="error-btn">
                    <i class="fas fa-building"></i>
                    Our Projects
                </a>
                <a href="contact.php" class="error-btn">
                    <i class="fas fa-envelope"></i>
                    Contact Us
                </a>
            </div>
            
            <div style="margin-top: 40px; opacity: 0.7;">
                <p>Need help? Contact us at <a href="mailto:<?= SITE_EMAIL ?>" style="color: white; text-decoration: underline;"><?= SITE_EMAIL ?></a></p>
                <p>Or call us at <a href="tel:<?= SITE_PHONE ?>" style="color: white; text-decoration: underline;"><?= SITE_PHONE ?></a></p>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate the error code
            const errorCode = document.querySelector('.error-code');
            errorCode.style.opacity = '0';
            errorCode.style.transform = 'scale(0.5)';
            
            setTimeout(() => {
                errorCode.style.transition = 'all 0.6s ease';
                errorCode.style.opacity = '1';
                errorCode.style.transform = 'scale(1)';
            }, 200);
            
            // Animate other elements
            const elements = document.querySelectorAll('.error-title, .error-description, .search-form, .error-actions');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, 400 + (index * 100));
            });
            
            // Focus search input after animation
            setTimeout(() => {
                document.querySelector('.search-input').focus();
            }, 1000);
        });
        
        // Add floating animation to the icon
        const icon = document.querySelector('.error-icon');
        setInterval(() => {
            icon.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                icon.style.transform = 'translateY(0)';
            }, 1000);
        }, 2000);
        
        // Add particle effect (optional)
        function createParticle() {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: rgba(255,255,255,0.6);
                border-radius: 50%;
                pointer-events: none;
                z-index: -1;
                left: ${Math.random() * 100}vw;
                top: 100vh;
                animation: float-up 6s linear forwards;
            `;
            
            document.body.appendChild(particle);
            
            setTimeout(() => {
                particle.remove();
            }, 6000);
        }
        
        // Add CSS for particle animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes float-up {
                to {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        // Create particles periodically
        setInterval(createParticle, 300);
    </script>
</body>
</html>
