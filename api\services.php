<?php
/**
 * Services Management API
 * Handles services CRUD operations for the mobile app
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            handleGetServices();
            break;
            
        case 'POST':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleCreateService($input, $user);
            break;
            
        case 'PUT':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleUpdateService($input, $user);
            break;
            
        case 'DELETE':
            $user = authenticateRequest();
            if (!$user) {
                jsonResponse(['error' => 'Authentication required'], 401);
            }
            handleDeleteService($user);
            break;
            
        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetServices() {
    global $db;
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $search = $_GET['search'] ?? '';
    $featured = $_GET['featured'] ?? '';
    
    $offset = ($page - 1) * $limit;
    
    try {
        $where = ['is_active = 1'];
        $params = [];
        
        if ($search) {
            $where[] = '(title LIKE ? OR description LIKE ? OR short_description LIKE ?)';
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
        }
        
        if ($featured === 'true') {
            $where[] = 'is_featured = 1';
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Get total count
        $totalQuery = "SELECT COUNT(*) as total FROM services WHERE $whereClause";
        $total = $db->fetchOne($totalQuery, $params)['total'];
        
        // Get services
        $servicesQuery = "SELECT * FROM services WHERE $whereClause ORDER BY sort_order ASC, created_at DESC LIMIT $limit OFFSET $offset";
        $services = $db->fetchAll($servicesQuery, $params);
        
        // Add image URLs
        foreach ($services as &$service) {
            if ($service['featured_image']) {
                $service['featured_image_url'] = UPLOAD_URL . '/' . $service['featured_image'];
            }
            
            if ($service['gallery']) {
                $gallery = json_decode($service['gallery'], true);
                if ($gallery) {
                    $service['gallery_urls'] = array_map(function($image) {
                        return UPLOAD_URL . '/' . $image;
                    }, $gallery);
                }
            }
        }
        
        jsonResponse([
            'success' => true,
            'services' => $services,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to fetch services: ' . $e->getMessage()], 500);
    }
}

function handleCreateService($input, $user) {
    global $db;
    
    // Validate required fields
    $requiredFields = ['title', 'description'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            jsonResponse(['error' => "Field '$field' is required"], 400);
        }
    }
    
    try {
        $title = sanitize($input['title']);
        $slug = generateSlug($input['slug'] ?? $title);
        
        // Check if slug already exists
        $existingSlug = $db->fetchOne("SELECT id FROM services WHERE slug = ?", [$slug]);
        if ($existingSlug) {
            $slug = $slug . '-' . time();
        }
        
        $serviceData = [
            'title' => $title,
            'slug' => $slug,
            'description' => $input['description'],
            'short_description' => sanitize($input['short_description'] ?? ''),
            'featured_image' => sanitize($input['featured_image'] ?? ''),
            'gallery' => isset($input['gallery']) ? json_encode($input['gallery']) : null,
            'is_featured' => (bool)($input['is_featured'] ?? false),
            'sort_order' => (int)($input['sort_order'] ?? 0),
            'meta_title' => sanitize($input['meta_title'] ?? ''),
            'meta_description' => sanitize($input['meta_description'] ?? ''),
            'is_active' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $serviceId = $db->insert('services', $serviceData);
        
        // Get the created service
        $service = $db->fetchOne("SELECT * FROM services WHERE id = ?", [$serviceId]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Service created successfully',
            'service' => $service
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to create service: ' . $e->getMessage()], 500);
    }
}

function handleUpdateService($input, $user) {
    global $db;
    
    if (!isset($input['id'])) {
        jsonResponse(['error' => 'Service ID is required'], 400);
    }
    
    $serviceId = (int)$input['id'];
    
    try {
        // Check if service exists
        $service = $db->fetchOne("SELECT * FROM services WHERE id = ? AND is_active = 1", [$serviceId]);
        if (!$service) {
            jsonResponse(['error' => 'Service not found'], 404);
        }
        
        $updateData = ['updated_at' => date('Y-m-d H:i:s')];
        
        // Update fields if provided
        if (isset($input['title'])) {
            $updateData['title'] = sanitize($input['title']);
        }
        
        if (isset($input['slug'])) {
            $slug = generateSlug($input['slug']);
            // Check if slug already exists (excluding current service)
            $existingSlug = $db->fetchOne("SELECT id FROM services WHERE slug = ? AND id != ?", [$slug, $serviceId]);
            if ($existingSlug) {
                $slug = $slug . '-' . time();
            }
            $updateData['slug'] = $slug;
        }
        
        if (isset($input['description'])) {
            $updateData['description'] = $input['description'];
        }
        
        if (isset($input['short_description'])) {
            $updateData['short_description'] = sanitize($input['short_description']);
        }
        
        if (isset($input['featured_image'])) {
            $updateData['featured_image'] = sanitize($input['featured_image']);
        }
        
        if (isset($input['gallery'])) {
            $updateData['gallery'] = json_encode($input['gallery']);
        }
        
        if (isset($input['is_featured'])) {
            $updateData['is_featured'] = (bool)$input['is_featured'];
        }
        
        if (isset($input['sort_order'])) {
            $updateData['sort_order'] = (int)$input['sort_order'];
        }
        
        if (isset($input['meta_title'])) {
            $updateData['meta_title'] = sanitize($input['meta_title']);
        }
        
        if (isset($input['meta_description'])) {
            $updateData['meta_description'] = sanitize($input['meta_description']);
        }
        
        $db->update('services', $updateData, 'id = ?', [$serviceId]);
        
        // Get updated service
        $updatedService = $db->fetchOne("SELECT * FROM services WHERE id = ?", [$serviceId]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Service updated successfully',
            'service' => $updatedService
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to update service: ' . $e->getMessage()], 500);
    }
}

function handleDeleteService($user) {
    global $db;
    
    if (!isset($_GET['id'])) {
        jsonResponse(['error' => 'Service ID is required'], 400);
    }
    
    $serviceId = (int)$_GET['id'];
    
    try {
        // Check if service exists
        $service = $db->fetchOne("SELECT * FROM services WHERE id = ? AND is_active = 1", [$serviceId]);
        if (!$service) {
            jsonResponse(['error' => 'Service not found'], 404);
        }
        
        // Soft delete
        $db->update('services', [
            'is_active' => 0,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'id = ?', [$serviceId]);
        
        jsonResponse([
            'success' => true,
            'message' => 'Service deleted successfully'
        ]);
        
    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to delete service: ' . $e->getMessage()], 500);
    }
}

// Include authentication function from auth.php
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}
?>
