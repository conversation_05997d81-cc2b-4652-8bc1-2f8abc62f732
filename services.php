<?php
require_once 'config/config.php';

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get search parameter
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';

// Build query
$where = ['is_active = 1'];
$params = [];

if ($search) {
    $where[] = '(title LIKE ? OR description LIKE ? OR short_description LIKE ?)';
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
}

$whereClause = implode(' AND ', $where);

// Get total count
$totalQuery = "SELECT COUNT(*) as total FROM services WHERE $whereClause";
$totalResult = $db->fetchOne($totalQuery, $params);
$total = $totalResult['total'];

// Get services
$servicesQuery = "SELECT * FROM services WHERE $whereClause ORDER BY sort_order ASC, title ASC LIMIT $limit OFFSET $offset";
$services = $db->fetchAll($servicesQuery, $params);

// Calculate pagination
$totalPages = ceil($total / $limit);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Services - <?= SITE_NAME ?></title>
    <meta name="description" content="Explore our comprehensive construction services including civil engineering, groundworks, RC frames, basements, and hard landscaping in London.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?= SITE_PHONE ?></span>
                    <span><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></span>
                </div>
                <div class="social-links">
                    <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                    <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>

        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="about.php">About Us</a></li>
                    <li><a href="services.php" class="active">Our Services</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed">Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="contact.php">Contact Us</a></li>
                </ul>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Our Services</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Our Services</span>
            </nav>
        </div>
    </section>

    <!-- Services Intro -->
    <section class="services-intro">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Professional Construction Services</h2>
                <p class="section-subtitle">We are committed to delivering high-quality services across all areas of construction, from initial planning to final completion.</p>
            </div>
        </div>
    </section>

    <!-- Search Section -->
    <section class="search-section">
        <div class="container">
            <form method="GET" class="search-form">
                <div class="search-input-group">
                    <input type="text" name="search" placeholder="Search services..." value="<?= htmlspecialchars($search) ?>" class="search-input">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <?php if ($search): ?>
                <div class="search-results-info">
                    <p>Showing <?= $total ?> result(s) for "<?= htmlspecialchars($search) ?>"</p>
                    <a href="services.php" class="clear-search">Clear search</a>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </section>

    <!-- Services Grid -->
    <section class="services-grid-section">
        <div class="container">
            <?php if (empty($services)): ?>
            <div class="no-results">
                <i class="fas fa-search"></i>
                <h3>No services found</h3>
                <p>Try adjusting your search criteria or browse all our services.</p>
                <a href="services.php" class="btn btn-primary">View All Services</a>
            </div>
            <?php else: ?>
            <div class="services-grid">
                <?php foreach ($services as $service): ?>
                <div class="service-card">
                    <div class="service-image">
                        <img src="<?= $service['featured_image'] ? UPLOAD_URL . '/' . $service['featured_image'] : ASSETS_URL . '/images/service-default.jpg' ?>"
                             alt="<?= htmlspecialchars($service['title']) ?>">
                        <div class="service-overlay">
                            <a href="service.php?slug=<?= $service['slug'] ?>" class="service-link">
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </div>
                    <div class="service-content">
                        <h3><?= htmlspecialchars($service['title']) ?></h3>
                        <p><?= htmlspecialchars($service['short_description']) ?></p>
                        <a href="service.php?slug=<?= $service['slug'] ?>" class="btn btn-outline">Learn More</a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
                <?php endif; ?>

                <div class="pagination-numbers">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $page): ?>
                        <span class="pagination-number active"><?= $i ?></span>
                        <?php else: ?>
                        <a href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-number"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>

                <?php if ($page < $totalPages): ?>
                <a href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="pagination-btn">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-choose-us">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Why Choose Our Services?</h2>
                <p class="section-subtitle">We bring expertise, reliability, and excellence to every project</p>
            </div>

            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-award"></i>
                    </div>
                    <h4>Expert Team</h4>
                    <p>Our skilled professionals bring years of experience and industry expertise to every project.</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h4>Modern Equipment</h4>
                    <p>We use the latest tools and technology to ensure efficient and high-quality construction work.</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>Timely Delivery</h4>
                    <p>We understand the importance of deadlines and work diligently to complete projects on time.</p>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h4>Quality Assurance</h4>
                    <p>Every project undergoes rigorous quality checks to ensure it meets our high standards.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Need a Custom Solution?</h2>
                <p>Contact us to discuss your specific construction needs and get a personalized quote.</p>
                <div class="cta-buttons">
                    <a href="contact.php" class="btn btn-primary">Get Quote</a>
                    <a href="projects.php" class="btn btn-outline">View Projects</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <img src="<?= ASSETS_URL ?>/images/logo-white.png" alt="<?= SITE_NAME ?>" class="footer-logo">
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="social-links">
                        <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="projects.php">Our Projects</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="service.php?slug=civil-engineering">Civil Engineering</a></li>
                        <li><a href="service.php?slug=groundworks">Groundworks</a></li>
                        <li><a href="service.php?slug=rc-frames">RC Frames</a></li>
                        <li><a href="service.php?slug=basements">Basements</a></li>
                        <li><a href="service.php?slug=hard-landscaping">Hard Landscaping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> <?= SITE_ADDRESS ?></p>
                        <p><i class="fas fa-phone"></i> <?= SITE_PHONE ?></p>
                        <p><i class="fas fa-mobile-alt"></i> <?= SITE_MOBILE ?></p>
                        <p><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
</body>
</html>
