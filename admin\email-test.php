<?php
require_once '../config/config.php';
require_once '../config/email.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'test_email') {
        $testEmail = sanitize($_POST['test_email']);

        if (!filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address';
            $messageType = 'error';
        } else {
            try {
                $result = testEmail($testEmail);

                if ($result) {
                    $message = 'Test email sent successfully to ' . htmlspecialchars($testEmail) . '! Please check your inbox.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to send test email. Please check your email configuration.';
                    $messageType = 'error';
                }

            } catch (Exception $e) {
                $message = 'Error sending test email: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }

    if ($action === 'test_contact_form') {
        // Simulate a contact form submission
        $testData = [
            'name' => 'Test User',
            'email' => sanitize($_POST['test_email']),
            'phone' => '0123 456 7890',
            'subject' => 'Test Contact Form Submission',
            'message' => 'This is a test message to verify that the contact form email notifications are working correctly.',
            'service_interest' => 'Civil Engineering',
            'project_reference' => '',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Test Browser',
            'created_at' => date('Y-m-d H:i:s')
        ];

        try {
            // Send notification email
            $notificationSent = sendContactNotification($testData);

            // Send auto-reply
            $autoReplySent = sendContactAutoReply($testData);

            if ($notificationSent && $autoReplySent) {
                $message = 'Contact form test completed successfully! Both notification and auto-reply emails were sent.';
                $messageType = 'success';
            } elseif ($notificationSent) {
                $message = 'Notification email sent successfully, but auto-reply failed.';
                $messageType = 'warning';
            } elseif ($autoReplySent) {
                $message = 'Auto-reply sent successfully, but notification email failed.';
                $messageType = 'warning';
            } else {
                $message = 'Both notification and auto-reply emails failed to send.';
                $messageType = 'error';
            }

        } catch (Exception $e) {
            $message = 'Error testing contact form emails: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get email configuration status
$emailSettings = getEmailSettings();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Testing - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <!-- Enhanced Email Testing Header -->
                <div class="email-test-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-envelope-circle-check"></i>
                                Email System Testing
                            </h1>
                            <p class="page-description">Test and verify your email configuration and delivery system</p>
                        </div>
                        <div class="header-status">
                            <div class="system-status <?= function_exists('mail') ? 'online' : 'offline' ?>">
                                <i class="fas fa-<?= function_exists('mail') ? 'check-circle' : 'times-circle' ?>"></i>
                                <span><?= function_exists('mail') ? 'System Online' : 'System Offline' ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Email Configuration Status -->
                <div class="email-config-grid">
                    <div class="config-card">
                        <div class="config-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="config-content">
                            <h4>PHP Mail Function</h4>
                            <div class="config-status <?= function_exists('mail') ? 'success' : 'error' ?>">
                                <i class="fas fa-<?= function_exists('mail') ? 'check-circle' : 'times-circle' ?>"></i>
                                <span><?= function_exists('mail') ? 'Available' : 'Not Available' ?></span>
                            </div>
                            <p class="config-description">Core PHP mail functionality status</p>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-icon">
                            <i class="fas fa-at"></i>
                        </div>
                        <div class="config-content">
                            <h4>From Email</h4>
                            <div class="config-value">
                                <?= htmlspecialchars($emailSettings['from_email']) ?>
                            </div>
                            <p class="config-description">Sender email address</p>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="config-content">
                            <h4>From Name</h4>
                            <div class="config-value">
                                <?= htmlspecialchars($emailSettings['from_name']) ?>
                            </div>
                            <p class="config-description">Sender display name</p>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="config-content">
                            <h4>Notification Email</h4>
                            <div class="config-value">
                                <?= htmlspecialchars($emailSettings['notification_email']) ?>
                            </div>
                            <p class="config-description">Admin notification recipient</p>
                        </div>
                    </div>

                    <div class="config-card">
                        <div class="config-icon">
                            <i class="fas fa-reply"></i>
                        </div>
                        <div class="config-content">
                            <h4>Auto-Reply</h4>
                            <div class="config-status <?= $emailSettings['auto_reply_enabled'] ? 'success' : 'error' ?>">
                                <i class="fas fa-<?= $emailSettings['auto_reply_enabled'] ? 'check-circle' : 'times-circle' ?>"></i>
                                <span><?= $emailSettings['auto_reply_enabled'] ? 'Enabled' : 'Disabled' ?></span>
                            </div>
                            <p class="config-description">Automatic customer responses</p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Email Testing Forms -->
                <div class="email-test-grid">
                    <!-- Basic Email Test -->
                    <div class="test-card">
                        <div class="test-header">
                            <div class="test-icon basic">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div class="test-info">
                                <h3>Basic Email Test</h3>
                                <p>Send a simple test email to verify your email configuration is working</p>
                            </div>
                        </div>
                        <div class="test-content">
                            <form method="POST" class="modern-test-form">
                                <input type="hidden" name="action" value="test_email">

                                <div class="form-group">
                                    <label for="test_email" class="form-label">
                                        <i class="fas fa-envelope"></i>
                                        Test Email Address
                                    </label>
                                    <input type="email"
                                           id="test_email"
                                           name="test_email"
                                           class="form-input"
                                           placeholder="Enter email address to test"
                                           required>
                                    <div class="form-help">We'll send a test message to this address</div>
                                </div>

                                <button type="submit" class="btn btn-primary btn-large">
                                    <i class="fas fa-paper-plane"></i>
                                    <span>Send Test Email</span>
                                </button>
                            </form>

                            <div class="test-features">
                                <h5>What this test does:</h5>
                                <ul>
                                    <li><i class="fas fa-check"></i> Verifies PHP mail function</li>
                                    <li><i class="fas fa-check"></i> Tests basic email delivery</li>
                                    <li><i class="fas fa-check"></i> Confirms SMTP configuration</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Form Test -->
                    <div class="test-card">
                        <div class="test-header">
                            <div class="test-icon advanced">
                                <i class="fas fa-envelope-open-text"></i>
                            </div>
                            <div class="test-info">
                                <h3>Contact Form Test</h3>
                                <p>Test the complete contact form email workflow including notifications and auto-replies</p>
                            </div>
                        </div>
                        <div class="test-content">
                            <form method="POST" class="modern-test-form">
                                <input type="hidden" name="action" value="test_contact_form">

                                <div class="form-group">
                                    <label for="test_email_contact" class="form-label">
                                        <i class="fas fa-user"></i>
                                        Your Email Address
                                    </label>
                                    <input type="email"
                                           id="test_email_contact"
                                           name="test_email"
                                           class="form-input"
                                           placeholder="Enter your email to receive test messages"
                                           required>
                                    <div class="form-help">You'll receive both notification and auto-reply emails</div>
                                </div>

                                <button type="submit" class="btn btn-success btn-large">
                                    <i class="fas fa-envelope-open-text"></i>
                                    <span>Test Contact Form Emails</span>
                                </button>
                            </form>

                            <div class="test-features">
                                <h5>What this test does:</h5>
                                <ul>
                                    <li><i class="fas fa-check"></i> Sends admin notification email</li>
                                    <li><i class="fas fa-check"></i> Sends customer auto-reply</li>
                                    <li><i class="fas fa-check"></i> Simulates real form submission</li>
                                    <li><i class="fas fa-check"></i> Tests complete email workflow</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Templates Preview -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-eye"></i> Email Templates Preview</h3>
                    </div>
                    <div class="card-content">
                        <div class="row">
                            <div class="col-md-4">
                                <h4>Test Email Template</h4>
                                <div style="border: 1px solid #ddd; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                                    <div style="background: #28a745; color: white; padding: 15px; text-align: center; margin: -15px -15px 15px -15px;">
                                        <h3 style="margin: 0;">✅ Email Test Successful</h3>
                                        <p style="margin: 5px 0 0 0;">Your email configuration is working correctly</p>
                                    </div>
                                    <p>Congratulations! This test email confirms that your email system is properly configured...</p>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <h4>Contact Notification Template</h4>
                                <div style="border: 1px solid #ddd; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                                    <div style="background: #2c3e50; color: white; padding: 15px; text-align: center; margin: -15px -15px 15px -15px;">
                                        <h3 style="margin: 0;">New Contact Form Submission</h3>
                                        <p style="margin: 5px 0 0 0;">You have received a new inquiry</p>
                                    </div>
                                    <p><strong>Name:</strong> John Doe<br>
                                    <strong>Email:</strong> <EMAIL><br>
                                    <strong>Message:</strong> I'm interested in your services...</p>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <h4>Auto-Reply Template</h4>
                                <div style="border: 1px solid #ddd; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                                    <div style="background: #e74c3c; color: white; padding: 15px; text-align: center; margin: -15px -15px 15px -15px;">
                                        <h3 style="margin: 0;">Thank You for Your Inquiry</h3>
                                        <p style="margin: 5px 0 0 0;">We will respond within 24 hours</p>
                                    </div>
                                    <p>Dear Customer,<br><br>
                                    Thank you for contacting <?= SITE_NAME ?>. We have received your inquiry...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Troubleshooting -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-question-circle"></i> Troubleshooting</h3>
                    </div>
                    <div class="card-content">
                        <h4>Common Issues and Solutions:</h4>

                        <div class="test-section">
                            <h5>📧 Emails not being sent</h5>
                            <ul>
                                <li>Check if PHP mail() function is enabled on your server</li>
                                <li>Verify your server's mail configuration</li>
                                <li>Check spam/junk folders</li>
                                <li>Contact your hosting provider about email sending limits</li>
                            </ul>
                        </div>

                        <div class="test-section">
                            <h5>📬 Emails going to spam</h5>
                            <ul>
                                <li>Set up SPF, DKIM, and DMARC records for your domain</li>
                                <li>Use a professional email address (not Gmail/Yahoo)</li>
                                <li>Avoid spam trigger words in subject lines</li>
                                <li>Consider using a transactional email service (SendGrid, Mailgun)</li>
                            </ul>
                        </div>

                        <div class="test-section">
                            <h5>⚙️ Server Configuration</h5>
                            <ul>
                                <li>Ensure sendmail or postfix is installed and configured</li>
                                <li>Check firewall settings for outbound email ports (25, 587, 465)</li>
                                <li>Verify DNS MX records are properly configured</li>
                                <li>Test with command line: <code>echo "Test" | mail -s "Test" <EMAIL></code></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
</body>
</html>
