/**
 * Offline Manager for Flori Construction Mobile App
 * Handles offline functionality and data caching
 */

class OfflineManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.offlineData = {
            projects: [],
            media: [],
            content: {}
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadOfflineData();
        this.updateOfflineIndicator();
    }
    
    setupEventListeners() {
        // Online/offline events
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateOfflineIndicator();
            this.handleOnlineMode();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateOfflineIndicator();
            this.handleOfflineMode();
        });
        
        // Intercept API requests when offline
        this.interceptAPIRequests();
    }
    
    updateOfflineIndicator() {
        // Add connection status indicator to header
        let indicator = document.getElementById('connection-status');
        
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'connection-status';
            indicator.className = 'connection-status';
            
            const header = document.querySelector('.app-header .header-right');
            if (header) {
                header.insertBefore(indicator, header.firstChild);
            }
        }
        
        indicator.className = `connection-status ${this.isOnline ? 'online' : 'offline'}`;
        indicator.innerHTML = `
            <i class="fas fa-${this.isOnline ? 'wifi' : 'wifi-slash'}"></i>
            <span class="status-text">${this.isOnline ? 'Online' : 'Offline'}</span>
        `;
        
        // Update app theme for offline mode
        document.body.classList.toggle('offline-mode', !this.isOnline);
    }
    
    handleOnlineMode() {
        console.log('App is now online');
        
        // Show online notification
        this.showOfflineNotification('Connection restored. Syncing data...', 'success');
        
        // Trigger sync
        if (window.SyncManager) {
            window.SyncManager.performSync();
        }
        
        // Re-enable online-only features
        this.enableOnlineFeatures();
    }
    
    handleOfflineMode() {
        console.log('App is now offline');
        
        // Show offline notification
        this.showOfflineNotification('You are now offline. Changes will be synced when connection is restored.', 'warning');
        
        // Disable online-only features
        this.disableOnlineFeatures();
        
        // Load offline data
        this.loadOfflineData();
    }
    
    enableOnlineFeatures() {
        // Enable upload buttons
        const uploadButtons = document.querySelectorAll('[data-requires-online]');
        uploadButtons.forEach(btn => {
            btn.disabled = false;
            btn.title = '';
        });
        
        // Enable sync-dependent features
        const syncButtons = document.querySelectorAll('[data-requires-sync]');
        syncButtons.forEach(btn => {
            btn.disabled = false;
            btn.title = '';
        });
    }
    
    disableOnlineFeatures() {
        // Disable upload buttons
        const uploadButtons = document.querySelectorAll('[data-requires-online]');
        uploadButtons.forEach(btn => {
            btn.disabled = true;
            btn.title = 'This feature requires an internet connection';
        });
        
        // Disable sync-dependent features
        const syncButtons = document.querySelectorAll('[data-requires-sync]');
        syncButtons.forEach(btn => {
            btn.disabled = true;
            btn.title = 'This feature requires an internet connection';
        });
    }
    
    showOfflineNotification(message, type = 'info') {
        // Create offline notification banner
        let banner = document.getElementById('offline-banner');
        
        if (!banner) {
            banner = document.createElement('div');
            banner.id = 'offline-banner';
            banner.className = 'offline-banner';
            document.body.appendChild(banner);
        }
        
        banner.className = `offline-banner ${type}`;
        banner.innerHTML = `
            <div class="banner-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button class="banner-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(() => {
                if (banner && banner.parentElement) {
                    banner.remove();
                }
            }, 5000);
        }
    }
    
    async loadOfflineData() {
        if (!window.SyncManager) {
            return;
        }
        
        try {
            // Load cached data from IndexedDB
            const [projects, media, content] = await Promise.all([
                window.SyncManager.getOfflineData('projects'),
                window.SyncManager.getOfflineData('media'),
                window.SyncManager.getOfflineData('content')
            ]);
            
            if (projects) this.offlineData.projects = projects;
            if (media) this.offlineData.media = media;
            if (content) this.offlineData.content = content;
            
            console.log('Offline data loaded:', this.offlineData);
            
        } catch (error) {
            console.error('Failed to load offline data:', error);
        }
    }
    
    interceptAPIRequests() {
        // Store original fetch function
        const originalFetch = window.fetch;
        
        // Override fetch for offline handling
        window.fetch = async (url, options = {}) => {
            // If online, use normal fetch
            if (this.isOnline) {
                return originalFetch(url, options);
            }
            
            // If offline, try to serve from cache
            return this.handleOfflineRequest(url, options);
        };
    }
    
    async handleOfflineRequest(url, options) {
        console.log('Handling offline request:', url);
        
        // Parse the request
        const urlObj = new URL(url, window.location.origin);
        const action = urlObj.searchParams.get('action');
        const method = options.method || 'GET';
        
        // Handle different API endpoints
        if (url.includes('mobile.php')) {
            return this.handleMobileAPIOffline(action, method, options);
        }
        
        // For other requests, return offline error
        throw new Error('Network request failed - offline mode');
    }
    
    async handleMobileAPIOffline(action, method, options) {
        switch (action) {
            case 'dashboard':
                return this.createOfflineResponse(this.getDashboardDataOffline());
                
            case 'projects':
                if (method === 'GET') {
                    return this.createOfflineResponse(this.getProjectsOffline());
                } else if (method === 'POST') {
                    return this.handleOfflineProjectCreate(options);
                }
                break;
                
            case 'media':
                if (method === 'GET') {
                    return this.createOfflineResponse(this.getMediaOffline());
                }
                break;
                
            case 'content':
                if (method === 'GET') {
                    return this.createOfflineResponse(this.getContentOffline());
                }
                break;
                
            default:
                throw new Error(`Offline mode: ${action} not available`);
        }
        
        throw new Error('Offline mode: Operation not supported');
    }
    
    createOfflineResponse(data) {
        return Promise.resolve({
            ok: true,
            status: 200,
            json: () => Promise.resolve({
                success: true,
                data: data,
                offline: true
            })
        });
    }
    
    getDashboardDataOffline() {
        const projects = this.offlineData.projects;
        const media = this.offlineData.media;
        
        return {
            stats: {
                total_projects: projects.length,
                completed_projects: projects.filter(p => p.project_type === 'completed').length,
                ongoing_projects: projects.filter(p => p.project_type === 'ongoing').length,
                featured_projects: projects.filter(p => p.is_featured).length,
                total_media: media.length
            },
            recent_projects: projects.slice(0, 5),
            recent_media: media.slice(0, 10),
            last_sync: window.SyncManager?.lastSyncTime || 'Never'
        };
    }
    
    getProjectsOffline() {
        const projects = this.offlineData.projects;
        
        return {
            projects: projects,
            pagination: {
                page: 1,
                limit: projects.length,
                total: projects.length,
                pages: 1
            }
        };
    }
    
    getMediaOffline() {
        const media = this.offlineData.media;
        
        return {
            media: media,
            pagination: {
                page: 1,
                limit: media.length,
                total: media.length,
                pages: 1
            }
        };
    }
    
    getContentOffline() {
        return {
            content: this.offlineData.content
        };
    }
    
    async handleOfflineProjectCreate(options) {
        try {
            const projectData = JSON.parse(options.body);
            
            // Add to offline data
            const newProject = {
                ...projectData,
                id: Date.now(), // Temporary ID
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                offline_created: true
            };
            
            this.offlineData.projects.unshift(newProject);
            
            // Add to sync queue
            if (window.SyncManager) {
                window.SyncManager.addToSyncQueue('project', 'create', projectData);
            }
            
            return this.createOfflineResponse(newProject);
            
        } catch (error) {
            throw new Error('Failed to create project offline');
        }
    }
    
    getOfflineStatus() {
        return {
            isOnline: this.isOnline,
            hasOfflineData: Object.keys(this.offlineData).length > 0,
            offlineDataCount: {
                projects: this.offlineData.projects.length,
                media: this.offlineData.media.length,
                content: Object.keys(this.offlineData.content).length
            }
        };
    }
    
    clearOfflineData() {
        this.offlineData = {
            projects: [],
            media: [],
            content: {}
        };
        
        // Clear IndexedDB
        if (window.SyncManager) {
            window.SyncManager.openDatabase().then(db => {
                const transaction = db.transaction(['projects', 'media', 'content'], 'readwrite');
                transaction.objectStore('projects').clear();
                transaction.objectStore('media').clear();
                transaction.objectStore('content').clear();
            });
        }
    }
}

// Initialize Offline Manager
window.OfflineManager = new OfflineManager();
