<?php
require_once 'config/config.php';

// Get pagination parameters
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Get filter parameters
$category = isset($_GET['category']) ? sanitize($_GET['category']) : '';
$type = isset($_GET['type']) ? sanitize($_GET['type']) : '';

// Build query
$where = ['is_active = 1'];
$params = [];

if ($category && in_array($category, ['projects', 'services', 'gallery', 'general'])) {
    $where[] = 'category = ?';
    $params[] = $category;
}

if ($type && in_array($type, ['image', 'video'])) {
    $where[] = 'file_type = ?';
    $params[] = $type;
}

$whereClause = implode(' AND ', $where);

// Get total count
$totalQuery = "SELECT COUNT(*) as total FROM media WHERE $whereClause";
$totalResult = $db->fetchOne($totalQuery, $params);
$total = $totalResult['total'];

// Get media items
$mediaQuery = "SELECT * FROM media WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
$mediaItems = $db->fetchAll($mediaQuery, $params);

// Calculate pagination
$totalPages = ceil($total / $limit);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Gallery - <?= SITE_NAME ?></title>
    <meta name="description" content="Browse our media gallery showcasing construction projects, services, and behind-the-scenes content from Flori Construction Ltd.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?= SITE_PHONE ?></span>
                    <span><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></span>
                </div>
                <div class="social-links">
                    <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                    <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>

        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="about.php">About Us</a></li>
                    <li><a href="services.php">Our Services</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed">Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php" class="active">Media</a></li>
                    <li><a href="contact.php">Contact Us</a></li>
                </ul>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Media Gallery</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Media Gallery</span>
            </nav>
        </div>
    </section>

    <!-- Media Intro -->
    <section class="media-intro">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Our Work in Pictures</h2>
                <p class="section-subtitle">Explore our portfolio through images and videos showcasing our construction projects and expertise.</p>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="filter-section">
        <div class="container">
            <div class="filter-controls">
                <div class="filter-tabs">
                    <a href="media.php" class="filter-tab <?= !$category && !$type ? 'active' : '' ?>">All Media</a>
                    <a href="media.php?category=projects" class="filter-tab <?= $category === 'projects' ? 'active' : '' ?>">Projects</a>
                    <a href="media.php?category=services" class="filter-tab <?= $category === 'services' ? 'active' : '' ?>">Services</a>
                    <a href="media.php?category=gallery" class="filter-tab <?= $category === 'gallery' ? 'active' : '' ?>">Gallery</a>
                </div>

                <div class="type-filters">
                    <a href="media.php?type=image<?= $category ? '&category=' . $category : '' ?>" class="type-filter <?= $type === 'image' ? 'active' : '' ?>">
                        <i class="fas fa-image"></i> Images
                    </a>
                    <a href="media.php?type=video<?= $category ? '&category=' . $category : '' ?>" class="type-filter <?= $type === 'video' ? 'active' : '' ?>">
                        <i class="fas fa-video"></i> Videos
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Media Grid -->
    <section class="media-grid-section">
        <div class="container">
            <?php if (empty($mediaItems)): ?>
            <div class="no-results">
                <i class="fas fa-images"></i>
                <h3>No media found</h3>
                <p>No media items match your current filter criteria.</p>
                <a href="media.php" class="btn btn-primary">View All Media</a>
            </div>
            <?php else: ?>
            <div class="media-grid">
                <?php foreach ($mediaItems as $item): ?>
                <div class="media-item" data-category="<?= $item['category'] ?>" data-type="<?= $item['file_type'] ?>">
                    <?php if ($item['file_type'] === 'video'): ?>
                    <div class="media-video">
                        <video poster="<?= $item['thumbnail'] ? UPLOAD_URL . '/' . $item['thumbnail'] : '' ?>">
                            <source src="<?= UPLOAD_URL . '/' . $item['file_path'] ?>" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        <div class="video-overlay" onclick="playVideo(this)">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="media-image">
                        <img src="<?= UPLOAD_URL . '/' . $item['file_path'] ?>"
                             alt="<?= htmlspecialchars($item['alt_text'] ?: $item['title']) ?>"
                             onclick="openLightbox('<?= UPLOAD_URL . '/' . $item['file_path'] ?>', '<?= htmlspecialchars($item['title']) ?>')">
                    </div>
                    <?php endif; ?>

                    <div class="media-info">
                        <h4><?= htmlspecialchars($item['title']) ?></h4>
                        <?php if ($item['description']): ?>
                        <p><?= htmlspecialchars(substr($item['description'], 0, 100)) ?>...</p>
                        <?php endif; ?>
                        <div class="media-meta">
                            <span class="media-category"><?= ucfirst($item['category']) ?></span>
                            <span class="media-date"><?= formatDate($item['created_at']) ?></span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?= $page - 1 ?><?= $category ? '&category=' . $category : '' ?><?= $type ? '&type=' . $type : '' ?>" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
                <?php endif; ?>

                <div class="pagination-numbers">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $page): ?>
                        <span class="pagination-number active"><?= $i ?></span>
                        <?php else: ?>
                        <a href="?page=<?= $i ?><?= $category ? '&category=' . $category : '' ?><?= $type ? '&type=' . $type : '' ?>" class="pagination-number"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>

                <?php if ($page < $totalPages): ?>
                <a href="?page=<?= $page + 1 ?><?= $category ? '&category=' . $category : '' ?><?= $type ? '&type=' . $type : '' ?>" class="pagination-btn">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Like What You See?</h2>
                <p>Contact us to discuss how we can bring similar quality and craftsmanship to your construction project.</p>
                <div class="cta-buttons">
                    <a href="contact.php" class="btn btn-primary">Get In Touch</a>
                    <a href="projects.php" class="btn btn-outline">View Projects</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox Modal -->
    <div id="lightbox" class="lightbox" onclick="closeLightbox()">
        <div class="lightbox-content">
            <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
            <img id="lightbox-image" src="" alt="">
            <div id="lightbox-caption" class="lightbox-caption"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <img src="<?= ASSETS_URL ?>/images/logo-white.png" alt="<?= SITE_NAME ?>" class="footer-logo">
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="social-links">
                        <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="projects.php">Our Projects</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="service.php?slug=civil-engineering">Civil Engineering</a></li>
                        <li><a href="service.php?slug=groundworks">Groundworks</a></li>
                        <li><a href="service.php?slug=rc-frames">RC Frames</a></li>
                        <li><a href="service.php?slug=basements">Basements</a></li>
                        <li><a href="service.php?slug=hard-landscaping">Hard Landscaping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> <?= SITE_ADDRESS ?></p>
                        <p><i class="fas fa-phone"></i> <?= SITE_PHONE ?></p>
                        <p><i class="fas fa-mobile-alt"></i> <?= SITE_MOBILE ?></p>
                        <p><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
    <script>
        // Lightbox functionality
        function openLightbox(imageSrc, caption) {
            document.getElementById('lightbox').style.display = 'flex';
            document.getElementById('lightbox-image').src = imageSrc;
            document.getElementById('lightbox-caption').textContent = caption || '';
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox() {
            document.getElementById('lightbox').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Video play functionality
        function playVideo(overlay) {
            const video = overlay.previousElementSibling;
            if (video.paused) {
                video.play();
                overlay.style.display = 'none';
            }
        }

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLightbox();
            }
        });

        // Video event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const videos = document.querySelectorAll('.media-video video');
            videos.forEach(video => {
                video.addEventListener('pause', function() {
                    const overlay = this.nextElementSibling;
                    overlay.style.display = 'flex';
                });

                video.addEventListener('ended', function() {
                    const overlay = this.nextElementSibling;
                    overlay.style.display = 'flex';
                });
            });
        });
    </script>
</body>
</html>
