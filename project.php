<?php
require_once 'config/config.php';

// Get project slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: projects.php');
    exit;
}

// Get project details
$project = $db->fetchOne("SELECT * FROM projects WHERE slug = ? AND is_active = 1", [$slug]);

if (!$project) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Get related projects (same type or random)
$relatedProjects = $db->fetchAll(
    "SELECT * FROM projects WHERE slug != ? AND is_active = 1 ORDER BY RAND() LIMIT 3",
    [$slug]
);

// Process gallery and services if they exist
$gallery = $project['gallery'] ? json_decode($project['gallery'], true) : [];
$services = $project['services'] ? json_decode($project['services'], true) : [];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($project['meta_title'] ?: $project['title']) ?> - <?= SITE_NAME ?></title>
    <meta name="description" content="<?= htmlspecialchars($project['meta_description'] ?: substr($project['description'], 0, 160)) ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?= SITE_PHONE ?></span>
                    <span><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></span>
                </div>
                <div class="social-links">
                    <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                    <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>

        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="about.php">About Us</a></li>
                    <li><a href="services.php">Our Services</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle active">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed">Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="contact.php">Contact Us</a></li>
                </ul>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1><?= htmlspecialchars($project['title']) ?></h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <a href="projects.php">Projects</a>
                <span>/</span>
                <span><?= htmlspecialchars($project['title']) ?></span>
            </nav>
        </div>
    </section>

    <!-- Project Detail -->
    <section class="project-detail">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="project-content">
                        <?php if ($project['featured_image']): ?>
                        <div class="project-featured-image">
                            <img src="<?= UPLOAD_URL . '/' . $project['featured_image'] ?>"
                                 alt="<?= htmlspecialchars($project['title']) ?>">
                        </div>
                        <?php endif; ?>

                        <div class="project-description">
                            <h2>Project Overview</h2>
                            <p><?= nl2br(htmlspecialchars($project['description'])) ?></p>
                        </div>

                        <?php if (!empty($gallery)): ?>
                        <div class="project-gallery">
                            <h3>Project Gallery</h3>
                            <div class="gallery-grid">
                                <?php foreach ($gallery as $image): ?>
                                <div class="gallery-item">
                                    <img src="<?= UPLOAD_URL . '/' . $image['path'] ?>"
                                         alt="<?= htmlspecialchars($image['alt'] ?? $project['title']) ?>"
                                         onclick="openLightbox('<?= UPLOAD_URL . '/' . $image['path'] ?>')">
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="project-sidebar">
                        <!-- Project Info -->
                        <div class="sidebar-widget">
                            <h3>Project Details</h3>
                            <div class="project-info">
                                <div class="info-item">
                                    <strong>Status:</strong>
                                    <span class="project-status status-<?= $project['project_type'] ?>">
                                        <?= ucfirst($project['project_type']) ?>
                                    </span>
                                </div>
                                <?php if ($project['location']): ?>
                                <div class="info-item">
                                    <strong>Location:</strong>
                                    <span><?= htmlspecialchars($project['location']) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if ($project['start_date']): ?>
                                <div class="info-item">
                                    <strong>Start Date:</strong>
                                    <span><?= formatDate($project['start_date']) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if ($project['completion_date']): ?>
                                <div class="info-item">
                                    <strong>Completion Date:</strong>
                                    <span><?= formatDate($project['completion_date']) ?></span>
                                </div>
                                <?php endif; ?>
                                <?php if ($project['client_name']): ?>
                                <div class="info-item">
                                    <strong>Client:</strong>
                                    <span><?= htmlspecialchars($project['client_name']) ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Services Used -->
                        <?php if (!empty($services)): ?>
                        <div class="sidebar-widget">
                            <h3>Services Used</h3>
                            <div class="services-list">
                                <?php foreach ($services as $service): ?>
                                <div class="service-tag">
                                    <?= htmlspecialchars($service) ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Contact Widget -->
                        <div class="sidebar-widget">
                            <h3>Interested in Similar Work?</h3>
                            <p>Contact us to discuss your project requirements and get a personalized quote.</p>
                            <div class="contact-buttons">
                                <a href="contact.php?project=<?= urlencode($project['title']) ?>" class="btn btn-primary btn-block">
                                    <i class="fas fa-envelope"></i> Get Quote
                                </a>
                                <a href="tel:<?= SITE_PHONE ?>" class="btn btn-outline btn-block">
                                    <i class="fas fa-phone"></i> Call Now
                                </a>
                            </div>
                        </div>

                        <!-- Related Projects -->
                        <?php if (!empty($relatedProjects)): ?>
                        <div class="sidebar-widget">
                            <h3>Related Projects</h3>
                            <div class="related-projects">
                                <?php foreach ($relatedProjects as $relatedProject): ?>
                                <div class="related-project-item">
                                    <div class="related-project-image">
                                        <img src="<?= $relatedProject['featured_image'] ? UPLOAD_URL . '/' . $relatedProject['featured_image'] : ASSETS_URL . '/images/project-default.jpg' ?>"
                                             alt="<?= htmlspecialchars($relatedProject['title']) ?>">
                                    </div>
                                    <div class="related-project-content">
                                        <h4><a href="project.php?slug=<?= $relatedProject['slug'] ?>"><?= htmlspecialchars($relatedProject['title']) ?></a></h4>
                                        <p><?= htmlspecialchars(substr($relatedProject['description'], 0, 80)) ?>...</p>
                                        <span class="project-type"><?= ucfirst($relatedProject['project_type']) ?></span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Start Your Project?</h2>
                <p>Contact us today to discuss your construction needs and get a personalized quote for your project.</p>
                <div class="cta-buttons">
                    <a href="contact.php?project=<?= urlencode($project['title']) ?>" class="btn btn-primary">Get Quote</a>
                    <a href="projects.php" class="btn btn-outline">View More Projects</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox Modal -->
    <div id="lightbox" class="lightbox" onclick="closeLightbox()">
        <div class="lightbox-content">
            <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
            <img id="lightbox-image" src="" alt="">
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <img src="<?= ASSETS_URL ?>/images/logo-white.png" alt="<?= SITE_NAME ?>" class="footer-logo">
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="social-links">
                        <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="projects.php">Our Projects</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="service.php?slug=civil-engineering">Civil Engineering</a></li>
                        <li><a href="service.php?slug=groundworks">Groundworks</a></li>
                        <li><a href="service.php?slug=rc-frames">RC Frames</a></li>
                        <li><a href="service.php?slug=basements">Basements</a></li>
                        <li><a href="service.php?slug=hard-landscaping">Hard Landscaping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> <?= SITE_ADDRESS ?></p>
                        <p><i class="fas fa-phone"></i> <?= SITE_PHONE ?></p>
                        <p><i class="fas fa-mobile-alt"></i> <?= SITE_MOBILE ?></p>
                        <p><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
    <script>
        // Lightbox functionality
        function openLightbox(imageSrc) {
            document.getElementById('lightbox').style.display = 'flex';
            document.getElementById('lightbox-image').src = imageSrc;
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox() {
            document.getElementById('lightbox').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLightbox();
            }
        });
    </script>
</body>
</html>
