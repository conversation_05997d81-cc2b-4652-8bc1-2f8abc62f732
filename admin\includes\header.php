<?php
/**
 * Admin Header
 * Flori Construction Ltd
 */

// Get current page title
$pageTitle = 'Dashboard';
$currentPage = basename($_SERVER['PHP_SELF']);

$pageTitles = [
    'index.php' => 'Dashboard',
    'projects.php' => 'Projects Management',
    'services.php' => 'Services Management',
    'media.php' => 'Media Library',
    'content.php' => 'Content Management',
    'inquiries.php' => 'Contact Inquiries',
    'testimonials.php' => 'Testimonials',
    'analytics.php' => 'Analytics Dashboard',
    'seo.php' => 'SEO Management',
    'users.php' => 'User Management',
    'branding.php' => 'Branding',
    'email-test.php' => 'Email Testing',
    'settings.php' => 'Settings',
    'profile.php' => 'Profile'
];

$pageTitle = $pageTitles[$currentPage] ?? 'Admin Panel';
$user = getCurrentUser();
?>

<!-- Modern Header - Hirelly Style -->
<header class="admin-header">
    <div class="header-content">
        <!-- Mobile Menu Toggle -->
        <button class="sidebar-toggle" aria-label="Toggle sidebar">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Search Bar -->
        <div class="header-search">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text"
                       class="search-input"
                       placeholder="Search anything here..."
                       aria-label="Search">
            </div>
        </div>

        <!-- Header Right -->
        <div class="header-right">
            <!-- Notifications -->
            <button class="header-action notifications" title="Notifications" aria-label="Notifications">
                <i class="fas fa-bell"></i>
            </button>

            <!-- User Profile -->
            <div class="user-profile">
                <div class="dropdown">
                    <button class="user-profile-btn" aria-label="User menu" aria-expanded="false">
                        <div class="user-avatar">
                            <img src="<?= ASSETS_URL ?>/images/default-avatar.svg"
                                 alt="<?= htmlspecialchars($user['full_name']) ?>"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                            <div class="avatar-fallback" style="display: none;">
                                <?= strtoupper(substr($user['full_name'], 0, 1)) ?>
                            </div>
                        </div>
                        <span class="user-name"><?= htmlspecialchars($user['full_name']) ?></span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                        <div class="dropdown-header">
                            <div class="user-info">
                                <div class="user-avatar-small">
                                    <img src="<?= ASSETS_URL ?>/images/default-avatar.svg"
                                         alt="<?= htmlspecialchars($user['full_name']) ?>"
                                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                    <div class="avatar-fallback-small" style="display: none;">
                                        <?= strtoupper(substr($user['full_name'], 0, 1)) ?>
                                    </div>
                                </div>
                                <div class="user-details">
                                    <span class="user-name"><?= htmlspecialchars($user['full_name']) ?></span>
                                    <span class="user-role"><?= ucfirst($user['role']) ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="dropdown-divider"></div>
                        <a href="profile.php" class="dropdown-item" role="menuitem">
                            <i class="fas fa-user"></i>
                            <span>Profile</span>
                        </a>
                        <a href="settings.php" class="dropdown-item" role="menuitem">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                        <a href="../" target="_blank" class="dropdown-item" role="menuitem">
                            <i class="fas fa-external-link-alt"></i>
                            <span>View Website</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="logout.php" class="dropdown-item logout" role="menuitem">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
