<?php
/**
 * Admin Layout Status Checker
 * Verifies which admin pages have been updated to use the new layout
 */

require_once '../config/config.php';

// List of all admin pages
$adminPages = [
    'index.php' => 'Dashboard',
    'projects.php' => 'Projects Management',
    'services.php' => 'Services Management',
    'media.php' => 'Media Library',
    'content.php' => 'Content Management',
    'inquiries.php' => 'Contact Inquiries',
    'testimonials.php' => 'Testimonials',
    'analytics.php' => 'Analytics Dashboard',
    'seo.php' => 'SEO Management',
    'users.php' => 'User Management',
    'branding.php' => 'Branding',
    'email-test.php' => 'Email Testing',
    'settings.php' => 'Settings',
    'profile.php' => 'Profile'
];

function checkPageLayout($filename) {
    if (!file_exists($filename)) {
        return ['status' => 'missing', 'message' => 'File not found'];
    }
    
    $content = file_get_contents($filename);
    
    // Check if using new includes
    $hasNewSidebar = strpos($content, "include 'includes/sidebar.php'") !== false;
    $hasNewHeader = strpos($content, "include 'includes/header.php'") !== false;
    $hasNewContainer = strpos($content, 'admin-container') !== false;
    
    // Check if still has old structure
    $hasOldSidebar = strpos($content, '<nav class="admin-sidebar">') !== false;
    $hasOldWrapper = strpos($content, 'admin-wrapper') !== false;
    
    if ($hasNewSidebar && $hasNewHeader && $hasNewContainer && !$hasOldSidebar) {
        return ['status' => 'updated', 'message' => 'Using new layout system'];
    } elseif ($hasNewSidebar || $hasNewHeader) {
        return ['status' => 'partial', 'message' => 'Partially updated - needs completion'];
    } else {
        return ['status' => 'old', 'message' => 'Still using old hardcoded layout'];
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Layout Status - <?= SITE_NAME ?></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f8f9fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .status-grid {
            display: grid;
            gap: 15px;
            margin-bottom: 30px;
        }
        .status-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            border-radius: 8px;
            border-left: 4px solid;
        }
        .status-updated {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .status-partial {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .status-old {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .status-missing {
            background: #e2e3e5;
            border-left-color: #6c757d;
            color: #495057;
        }
        .page-name {
            font-weight: 600;
            margin-right: 15px;
        }
        .status-message {
            font-size: 14px;
            opacity: 0.8;
        }
        .summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .stat-card {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Admin Layout Status</h1>
            <p>Checking which admin pages have been updated to use the new layout system</p>
        </div>
        
        <div class="content">
            <?php
            $stats = ['updated' => 0, 'partial' => 0, 'old' => 0, 'missing' => 0];
            $results = [];
            
            foreach ($adminPages as $file => $title) {
                $result = checkPageLayout($file);
                $results[$file] = ['title' => $title, 'result' => $result];
                $stats[$result['status']]++;
            }
            ?>
            
            <div class="summary">
                <h3>📊 Summary</h3>
                <p>Status of admin pages layout update:</p>
                <div class="summary-stats">
                    <div class="stat-card">
                        <div class="stat-number" style="color: #28a745;"><?= $stats['updated'] ?></div>
                        <div class="stat-label">Updated</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #ffc107;"><?= $stats['partial'] ?></div>
                        <div class="stat-label">Partial</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #dc3545;"><?= $stats['old'] ?></div>
                        <div class="stat-label">Old Layout</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" style="color: #6c757d;"><?= $stats['missing'] ?></div>
                        <div class="stat-label">Missing</div>
                    </div>
                </div>
            </div>

            <h3>📋 Detailed Status</h3>
            <div class="status-grid">
                <?php foreach ($results as $file => $data): ?>
                    <div class="status-item status-<?= $data['result']['status'] ?>">
                        <div>
                            <div class="page-name"><?= $data['title'] ?></div>
                            <div class="status-message"><?= $data['result']['message'] ?></div>
                        </div>
                        <div>
                            <strong><?= ucfirst($data['result']['status']) ?></strong>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if ($stats['updated'] > 0): ?>
            <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #155724; margin-top: 0;">✅ Benefits of Updated Pages:</h4>
                <ul style="color: #155724; margin-bottom: 0;">
                    <li><strong>Consistent Navigation:</strong> All menu items visible on every page</li>
                    <li><strong>Enhanced Features:</strong> Analytics, SEO, Users, Testimonials menus</li>
                    <li><strong>Role-based Access:</strong> Admin vs Editor permissions</li>
                    <li><strong>Quick Actions:</strong> View Website, Mobile App, Notifications</li>
                    <li><strong>Better UX:</strong> Professional sidebar with user info</li>
                    <li><strong>Easy Maintenance:</strong> Centralized sidebar and header</li>
                </ul>
            </div>
            <?php endif; ?>

            <div class="actions">
                <a href="index.php" class="btn btn-primary">← Back to Dashboard</a>
                <?php if ($stats['old'] > 0 || $stats['partial'] > 0): ?>
                <a href="batch-update-layout.php" class="btn btn-secondary">🔄 Update Remaining Pages</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
