<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle actions
$action = $_GET['action'] ?? 'list';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    if ($action === 'add' || $action === 'edit') {
        $projectId = $_POST['id'] ?? null;
        $title = sanitize($_POST['title']);
        $slug = generateSlug($_POST['slug'] ?: $title);
        $description = sanitize($_POST['description']);
        $location = sanitize($_POST['location']);
        $projectType = sanitize($_POST['project_type']);
        $startDate = $_POST['start_date'] ?: null;
        $completionDate = $_POST['completion_date'] ?: null;
        $clientName = sanitize($_POST['client_name']);
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
        $sortOrder = (int)($_POST['sort_order'] ?: 0);
        $metaTitle = sanitize($_POST['meta_title']);
        $metaDescription = sanitize($_POST['meta_description']);

        // Validate required fields
        if (empty($title) || empty($description) || empty($projectType)) {
            $message = 'Title, description, and project type are required';
            $messageType = 'error';
        } elseif (!in_array($projectType, ['completed', 'ongoing'])) {
            $message = 'Invalid project type selected';
            $messageType = 'error';
        } else {
            // Handle file upload
            $featuredImage = '';
            if (isset($_FILES['featured_image']) && $_FILES['featured_image']['error'] === UPLOAD_ERR_OK) {
                try {
                    $uploadResult = uploadFile($_FILES['featured_image'], 'projects');
                    $featuredImage = $uploadResult['file_path'];
                } catch (Exception $e) {
                    $message = 'Error uploading image: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }

            if (!$message) {
            try {
                $projectData = [
                    'title' => $title,
                    'slug' => $slug,
                    'description' => $description,
                    'location' => $location,
                    'project_type' => $projectType,
                    'start_date' => $startDate,
                    'completion_date' => $completionDate,
                    'client_name' => $clientName,
                    'is_featured' => $isFeatured,
                    'sort_order' => $sortOrder,
                    'meta_title' => $metaTitle,
                    'meta_description' => $metaDescription,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($featuredImage) {
                    $projectData['featured_image'] = $featuredImage;
                }

                if ($action === 'add') {
                    $projectData['created_at'] = date('Y-m-d H:i:s');
                    $db->insert('projects', $projectData);
                    $message = 'Project added successfully!';
                } else {
                    $db->update('projects', $projectData, 'id = ?', [$projectId]);
                    $message = 'Project updated successfully!';
                }

                $messageType = 'success';
                $action = 'list';

            } catch (Exception $e) {
                $message = 'Error saving project: ' . $e->getMessage();
                $messageType = 'error';
            }
            }
        }
    } elseif ($action === 'delete') {
        $projectId = $_POST['id'];
        try {
            $db->update('projects', ['is_active' => 0], 'id = ?', [$projectId]);
            $message = 'Project deleted successfully!';
            $messageType = 'success';
            $action = 'list';
        } catch (Exception $e) {
            $message = 'Error deleting project: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get project for editing
$project = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $project = $db->fetchOne("SELECT * FROM projects WHERE id = ? AND is_active = 1", [$_GET['id']]);
    if (!$project) {
        $action = 'list';
        $message = 'Project not found';
        $messageType = 'error';
    }
}

// Get projects list
if ($action === 'list') {
    $page = (int)($_GET['page'] ?? 1);
    $limit = ADMIN_ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';
    $filter = $_GET['filter'] ?? '';

    $where = ['is_active = 1'];
    $params = [];

    if ($search) {
        $where[] = '(title LIKE ? OR description LIKE ? OR location LIKE ?)';
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    if ($filter && in_array($filter, ['completed', 'ongoing'])) {
        $where[] = 'project_type = ?';
        $params[] = $filter;
    }

    $whereClause = implode(' AND ', $where);

    $totalQuery = "SELECT COUNT(*) as total FROM projects WHERE $whereClause";
    $total = $db->fetchOne($totalQuery, $params)['total'];

    $projectsQuery = "SELECT * FROM projects WHERE $whereClause ORDER BY sort_order ASC, created_at DESC LIMIT $limit OFFSET $offset";
    $projects = $db->fetchAll($projectsQuery, $params);

    $totalPages = ceil($total / $limit);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects Management - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                <!-- Enhanced Projects Header -->
                <div class="projects-header">
                    <div class="projects-header-content">
                        <div class="projects-title-section">
                            <h2 class="projects-main-title">
                                <i class="fas fa-building projects-icon"></i>
                                Projects Management
                            </h2>
                            <p class="projects-subtitle">Manage your construction projects and portfolio</p>
                            <div class="projects-stats">
                                <div class="stat-item">
                                    <span class="stat-number"><?= $total ?></span>
                                    <span class="stat-label">Total Projects</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?= count(array_filter($projects, fn($p) => $p['project_type'] === 'completed')) ?></span>
                                    <span class="stat-label">Completed</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?= count(array_filter($projects, fn($p) => $p['project_type'] === 'ongoing')) ?></span>
                                    <span class="stat-label">Ongoing</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?= count(array_filter($projects, fn($p) => $p['is_featured'])) ?></span>
                                    <span class="stat-label">Featured</span>
                                </div>
                            </div>
                        </div>
                        <div class="projects-actions">
                            <a href="?action=add" class="btn btn-success btn-large">
                                <i class="fas fa-plus"></i>
                                <span>Add New Project</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Search & Filters -->
                <div class="search-filters-container">
                    <div class="search-section">
                        <form method="GET" class="modern-search-form">
                            <input type="hidden" name="action" value="list">
                            <div class="search-input-group">
                                <div class="search-icon">
                                    <i class="fas fa-search"></i>
                                </div>
                                <input type="text" name="search" placeholder="Search projects by title, description, location..."
                                       value="<?= htmlspecialchars($search) ?>" class="search-input">
                                <div class="filter-section">
                                    <select name="filter" class="filter-select">
                                        <option value="">All Types</option>
                                        <option value="completed" <?= $filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                                        <option value="ongoing" <?= $filter === 'ongoing' ? 'selected' : '' ?>>Ongoing</option>
                                    </select>
                                </div>
                                <div class="search-actions">
                                    <button type="submit" class="search-btn">
                                        <i class="fas fa-search"></i>
                                        <span>Search</span>
                                    </button>
                                    <?php if ($search || $filter): ?>
                                    <a href="?action=list" class="clear-btn">
                                        <i class="fas fa-times"></i>
                                        <span>Clear</span>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Projects Content -->
                <?php if (empty($projects)): ?>
                <div class="empty-state-container">
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="empty-state-content">
                            <h3>No Projects Found</h3>
                            <p>You haven't created any projects yet. Start building your project portfolio by adding your first construction project.</p>
                            <div class="empty-state-actions">
                                <a href="?action=add" class="btn btn-success btn-large">
                                    <i class="fas fa-plus"></i>
                                    <span>Create Your First Project</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="projects-table-container">
                    <div class="table-header">
                        <div class="table-title">
                            <h3>Projects List</h3>
                            <span class="table-count"><?= count($projects) ?> of <?= $total ?> projects</span>
                        </div>
                        <div class="table-actions">
                            <button class="view-toggle active" data-view="table" title="Table View">
                                <i class="fas fa-table"></i>
                            </button>
                            <button class="view-toggle" data-view="grid" title="Grid View">
                                <i class="fas fa-th-large"></i>
                            </button>
                        </div>
                    </div>

                    <div class="modern-table-wrapper">
                        <table class="modern-table">
                            <thead>
                                <tr>
                                    <th class="col-image">
                                        <span class="th-content">
                                            <i class="fas fa-image"></i>
                                            Image
                                        </span>
                                    </th>
                                    <th class="col-title">
                                        <span class="th-content">
                                            <i class="fas fa-heading"></i>
                                            Project Details
                                        </span>
                                    </th>
                                    <th class="col-location">
                                        <span class="th-content">
                                            <i class="fas fa-map-marker-alt"></i>
                                            Location
                                        </span>
                                    </th>
                                    <th class="col-status">
                                        <span class="th-content">
                                            <i class="fas fa-tasks"></i>
                                            Status
                                        </span>
                                    </th>
                                    <th class="col-meta">
                                        <span class="th-content">
                                            <i class="fas fa-info-circle"></i>
                                            Timeline
                                        </span>
                                    </th>
                                    <th class="col-actions">
                                        <span class="th-content">
                                            <i class="fas fa-cogs"></i>
                                            Actions
                                        </span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                        <?php foreach ($projects as $proj): ?>
                        <tr class="project-row" data-project-id="<?= $proj['id'] ?>">
                            <td class="col-image">
                                <div class="project-image-container">
                                    <?php if ($proj['featured_image']): ?>
                                    <img src="<?= UPLOAD_URL . '/' . $proj['featured_image'] ?>"
                                         alt="<?= htmlspecialchars($proj['title']) ?>"
                                         class="project-thumbnail"
                                         loading="lazy">
                                    <?php else: ?>
                                    <div class="project-thumbnail-placeholder">
                                        <i class="fas fa-image"></i>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="col-title">
                                <div class="project-details">
                                    <h4 class="project-title"><?= htmlspecialchars($proj['title']) ?></h4>
                                    <p class="project-description"><?= htmlspecialchars(substr($proj['description'], 0, 120)) ?><?= strlen($proj['description']) > 120 ? '...' : '' ?></p>
                                    <div class="project-meta">
                                        <span class="meta-item">
                                            <i class="fas fa-link"></i>
                                            <code>/<?= htmlspecialchars($proj['slug']) ?></code>
                                        </span>
                                        <?php if ($proj['client_name']): ?>
                                        <span class="meta-item">
                                            <i class="fas fa-user"></i>
                                            <?= htmlspecialchars($proj['client_name']) ?>
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="col-location">
                                <div class="location-info">
                                    <?php if ($proj['location']): ?>
                                    <span class="location-name">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?= htmlspecialchars($proj['location']) ?>
                                    </span>
                                    <?php else: ?>
                                    <span class="location-empty">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Not specified
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="col-status">
                                <div class="status-indicators">
                                    <span class="status-badge project-type-<?= $proj['project_type'] ?>">
                                        <i class="fas fa-<?= $proj['project_type'] === 'completed' ? 'check-circle' : 'clock' ?>"></i>
                                        <?= ucfirst($proj['project_type']) ?>
                                    </span>
                                    <?php if ($proj['is_featured']): ?>
                                    <span class="status-badge featured">
                                        <i class="fas fa-star"></i>
                                        Featured
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td class="col-meta">
                                <div class="timeline-info">
                                    <?php if ($proj['start_date']): ?>
                                    <div class="timeline-row">
                                        <span class="timeline-label">Started:</span>
                                        <span class="timeline-value"><?= formatDate($proj['start_date']) ?></span>
                                    </div>
                                    <?php endif; ?>
                                    <?php if ($proj['completion_date']): ?>
                                    <div class="timeline-row">
                                        <span class="timeline-label">Completed:</span>
                                        <span class="timeline-value"><?= formatDate($proj['completion_date']) ?></span>
                                    </div>
                                    <?php else: ?>
                                    <div class="timeline-row">
                                        <span class="timeline-label">Status:</span>
                                        <span class="timeline-value ongoing">In Progress</span>
                                    </div>
                                    <?php endif; ?>
                                    <div class="timeline-row">
                                        <span class="timeline-label">Created:</span>
                                        <span class="timeline-value"><?= formatDate($proj['created_at']) ?></span>
                                    </div>
                                </div>
                            </td>
                            <td class="col-actions">
                                <div class="action-buttons">
                                    <a href="?action=edit&id=<?= $proj['id'] ?>"
                                       class="action-btn edit-btn"
                                       title="Edit Project">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="../project.php?slug=<?= $proj['slug'] ?>"
                                       target="_blank"
                                       class="action-btn view-btn"
                                       title="View Project">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    <form method="POST" class="delete-form"
                                          onsubmit="return confirm('Are you sure you want to delete this project? This action cannot be undone.')">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?= $proj['id'] ?>">
                                        <button type="submit"
                                                class="action-btn delete-btn"
                                                title="Delete Project">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                        </table>
                    </div>

                    <!-- Enhanced Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <div class="table-footer">
                        <div class="pagination-info">
                            <span>Showing <?= (($page - 1) * $limit) + 1 ?> to <?= min($page * $limit, $total) ?> of <?= $total ?> projects</span>
                        </div>
                        <nav class="modern-pagination" aria-label="Projects pagination">
                            <div class="pagination-controls">
                                <?php if ($page > 1): ?>
                                <a href="?action=list&page=1<?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>"
                                   class="pagination-btn first-btn" title="First page">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                                <a href="?action=list&page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>"
                                   class="pagination-btn prev-btn" title="Previous page">
                                    <i class="fas fa-angle-left"></i>
                                    <span>Previous</span>
                                </a>
                                <?php endif; ?>

                                <div class="pagination-numbers">
                                    <?php
                                    $start = max(1, $page - 2);
                                    $end = min($totalPages, $page + 2);

                                    if ($start > 1): ?>
                                        <a href="?action=list&page=1<?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>" class="pagination-number">1</a>
                                        <?php if ($start > 2): ?>
                                            <span class="pagination-ellipsis">...</span>
                                        <?php endif; ?>
                                    <?php endif; ?>

                                    <?php for ($i = $start; $i <= $end; $i++): ?>
                                        <?php if ($i == $page): ?>
                                        <span class="pagination-number active"><?= $i ?></span>
                                        <?php else: ?>
                                        <a href="?action=list&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>" class="pagination-number"><?= $i ?></a>
                                        <?php endif; ?>
                                    <?php endfor; ?>

                                    <?php if ($end < $totalPages): ?>
                                        <?php if ($end < $totalPages - 1): ?>
                                            <span class="pagination-ellipsis">...</span>
                                        <?php endif; ?>
                                        <a href="?action=list&page=<?= $totalPages ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>" class="pagination-number"><?= $totalPages ?></a>
                                    <?php endif; ?>
                                </div>

                                <?php if ($page < $totalPages): ?>
                                <a href="?action=list&page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>"
                                   class="pagination-btn next-btn" title="Next page">
                                    <span>Next</span>
                                    <i class="fas fa-angle-right"></i>
                                </a>
                                <a href="?action=list&page=<?= $totalPages ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $filter ? '&filter=' . $filter : '' ?>"
                                   class="pagination-btn last-btn" title="Last page">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                                <?php endif; ?>
                            </div>
                        </nav>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Enhanced Form Header -->
                <div class="form-header">
                    <div class="form-header-content">
                        <div class="form-title-section">
                            <h2 class="form-main-title">
                                <i class="fas fa-<?= $action === 'add' ? 'plus' : 'edit' ?> form-icon"></i>
                                <?= $action === 'add' ? 'Create New Project' : 'Edit Project' ?>
                            </h2>
                            <p class="form-subtitle">
                                <?= $action === 'add'
                                    ? 'Add a new construction project to your portfolio'
                                    : 'Update project information and details' ?>
                            </p>
                        </div>
                        <div class="form-actions-header">
                            <a href="?action=list" class="btn btn-outline btn-large">
                                <i class="fas fa-arrow-left"></i>
                                <span>Back to Projects</span>
                            </a>
                        </div>
                    </div>
                </div>

                <form method="POST" enctype="multipart/form-data" class="modern-project-form">
                    <?= getCSRFField() ?>
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="id" value="<?= $project['id'] ?>">
                    <?php endif; ?>

                    <!-- Project Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                Project Information
                            </h3>
                            <p class="section-description">Basic information about your construction project</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label for="title" class="field-label">
                                        <i class="fas fa-heading"></i>
                                        Project Title *
                                    </label>
                                    <input type="text"
                                           id="title"
                                           name="title"
                                           class="form-input"
                                           value="<?= htmlspecialchars($project['title'] ?? '') ?>"
                                           placeholder="Enter project title..."
                                           required>
                                    <div class="field-help">This will be the main title displayed for your project</div>
                                </div>

                                <div class="form-field">
                                    <label for="slug" class="field-label">
                                        <i class="fas fa-link"></i>
                                        URL Slug
                                    </label>
                                    <input type="text"
                                           id="slug"
                                           name="slug"
                                           class="form-input"
                                           value="<?= htmlspecialchars($project['slug'] ?? '') ?>"
                                           placeholder="auto-generated-from-title">
                                    <div class="field-help">Used in the project URL. Leave empty to auto-generate from title.</div>
                                </div>

                                <div class="form-field">
                                    <label for="location" class="field-label">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Location
                                    </label>
                                    <input type="text"
                                           id="location"
                                           name="location"
                                           class="form-input"
                                           value="<?= htmlspecialchars($project['location'] ?? '') ?>"
                                           placeholder="e.g., London, UK">
                                    <div class="field-help">Where this project is located</div>
                                </div>

                                <div class="form-field">
                                    <label for="project_type" class="field-label">
                                        <i class="fas fa-tasks"></i>
                                        Project Type *
                                    </label>
                                    <select id="project_type" name="project_type" class="form-input" required>
                                        <option value="">Select project type...</option>
                                        <option value="completed" <?= ($project['project_type'] ?? '') === 'completed' ? 'selected' : '' ?>>Completed Project</option>
                                        <option value="ongoing" <?= ($project['project_type'] ?? '') === 'ongoing' ? 'selected' : '' ?>>Ongoing Project</option>
                                    </select>
                                    <div class="field-help">Current status of the project</div>
                                </div>

                                <div class="form-field">
                                    <label for="client_name" class="field-label">
                                        <i class="fas fa-user"></i>
                                        Client Name
                                    </label>
                                    <input type="text"
                                           id="client_name"
                                           name="client_name"
                                           class="form-input"
                                           value="<?= htmlspecialchars($project['client_name'] ?? '') ?>"
                                           placeholder="Client or company name">
                                    <div class="field-help">Name of the client for this project (optional)</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="description" class="field-label">
                                        <i class="fas fa-file-alt"></i>
                                        Project Description *
                                    </label>
                                    <textarea id="description"
                                              name="description"
                                              class="form-textarea"
                                              rows="6"
                                              placeholder="Detailed description of your project..."
                                              required><?= htmlspecialchars($project['description'] ?? '') ?></textarea>
                                    <div class="field-help">Detailed description of the project, its scope, and key features</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Project Timeline Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-calendar-alt"></i>
                                Project Timeline
                            </h3>
                            <p class="section-description">Set project start and completion dates</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label for="start_date" class="field-label">
                                        <i class="fas fa-play"></i>
                                        Start Date
                                    </label>
                                    <input type="date"
                                           id="start_date"
                                           name="start_date"
                                           class="form-input"
                                           value="<?= $project['start_date'] ?? '' ?>">
                                    <div class="field-help">When the project started or is planned to start</div>
                                </div>

                                <div class="form-field">
                                    <label for="completion_date" class="field-label">
                                        <i class="fas fa-flag-checkered"></i>
                                        Completion Date
                                    </label>
                                    <input type="date"
                                           id="completion_date"
                                           name="completion_date"
                                           class="form-input"
                                           value="<?= $project['completion_date'] ?? '' ?>">
                                    <div class="field-help">Leave empty for ongoing projects</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Image Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-image"></i>
                                Featured Image
                            </h3>
                            <p class="section-description">Upload an image to showcase your project</p>
                        </div>
                        <div class="section-content">
                            <?php if ($action === 'edit' && $project['featured_image']): ?>
                            <div class="current-image-preview">
                                <label class="preview-label">Current Image:</label>
                                <div class="image-preview-container">
                                    <img src="<?= UPLOAD_URL . '/' . $project['featured_image'] ?>"
                                         alt="Current featured image"
                                         class="current-image">
                                    <div class="image-overlay">
                                        <span class="image-info">Click to replace</span>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="form-field">
                                <label for="featured_image" class="field-label">
                                    <i class="fas fa-upload"></i>
                                    <?= ($action === 'edit' && $project['featured_image']) ? 'Replace Image' : 'Upload Image' ?>
                                </label>
                                <div class="modern-file-upload">
                                    <input type="file"
                                           id="featured_image"
                                           name="featured_image"
                                           accept="image/*"
                                           class="file-input">
                                    <label for="featured_image" class="file-upload-area">
                                        <div class="upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="upload-text">
                                            <strong>Choose a file</strong> or drag it here
                                        </div>
                                        <div class="upload-formats">
                                            Supported formats: JPG, PNG, GIF, WebP (Max: 10MB)
                                        </div>
                                    </label>
                                    <div class="file-preview" style="display: none;">
                                        <div class="preview-image"></div>
                                        <div class="preview-info">
                                            <span class="file-name"></span>
                                            <span class="file-size"></span>
                                        </div>
                                        <button type="button" class="remove-file">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="field-help">Upload a high-quality image that showcases your project. Recommended size: 800x600px or larger.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings & SEO Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-cogs"></i>
                                Settings & SEO
                            </h3>
                            <p class="section-description">Configure project settings and search engine optimization</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label class="field-label">
                                        <i class="fas fa-star"></i>
                                        Project Options
                                    </label>
                                    <div class="checkbox-group">
                                        <label class="modern-checkbox">
                                            <input type="checkbox"
                                                   name="is_featured"
                                                   value="1"
                                                   <?= ($project['is_featured'] ?? 0) ? 'checked' : '' ?>>
                                            <span class="checkbox-mark"></span>
                                            <span class="checkbox-text">
                                                <strong>Featured Project</strong>
                                                <small>Display prominently on homepage and project listings</small>
                                            </span>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-field">
                                    <label for="sort_order" class="field-label">
                                        <i class="fas fa-sort-numeric-down"></i>
                                        Sort Order
                                    </label>
                                    <input type="number"
                                           id="sort_order"
                                           name="sort_order"
                                           class="form-input"
                                           value="<?= $project['sort_order'] ?? 0 ?>"
                                           min="0"
                                           placeholder="0">
                                    <div class="field-help">Lower numbers appear first in listings</div>
                                </div>

                                <div class="form-field">
                                    <label for="meta_title" class="field-label">
                                        <i class="fas fa-tag"></i>
                                        Meta Title (SEO)
                                    </label>
                                    <input type="text"
                                           id="meta_title"
                                           name="meta_title"
                                           class="form-input"
                                           value="<?= htmlspecialchars($project['meta_title'] ?? '') ?>"
                                           maxlength="60"
                                           placeholder="SEO-optimized title for search engines">
                                    <div class="char-count" data-target="meta_title" data-max="60">
                                        <span class="count-text">0 / 60 characters</span>
                                        <div class="count-bar">
                                            <div class="count-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="field-help">Recommended: 50-60 characters for optimal search engine display</div>
                                </div>

                                <div class="form-field">
                                    <label for="meta_description" class="field-label">
                                        <i class="fas fa-file-text"></i>
                                        Meta Description (SEO)
                                    </label>
                                    <textarea id="meta_description"
                                              name="meta_description"
                                              class="form-textarea"
                                              rows="3"
                                              maxlength="160"
                                              placeholder="Brief description for search engine results..."><?= htmlspecialchars($project['meta_description'] ?? '') ?></textarea>
                                    <div class="char-count" data-target="meta_description" data-max="160">
                                        <span class="count-text">0 / 160 characters</span>
                                        <div class="count-bar">
                                            <div class="count-progress" style="width: 0%"></div>
                                        </div>
                                    </div>
                                    <div class="field-help">Recommended: 150-160 characters for optimal search engine display</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <div class="actions-container">
                            <div class="primary-actions">
                                <button type="submit" class="btn btn-success btn-large">
                                    <i class="fas fa-save"></i>
                                    <span><?= $action === 'add' ? 'Create Project' : 'Update Project' ?></span>
                                </button>
                            </div>
                            <div class="secondary-actions">
                                <a href="?action=list" class="btn btn-outline btn-large">
                                    <i class="fas fa-times"></i>
                                    <span>Cancel</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-generate slug from title
            const titleField = document.getElementById('title');
            const slugField = document.getElementById('slug');

            if (titleField && slugField) {
                titleField.addEventListener('input', function() {
                    if (!slugField.value || slugField.dataset.autoGenerated) {
                        const slug = this.value.toLowerCase()
                            .replace(/[^a-z0-9\s-]/g, '')
                            .replace(/[\s-]+/g, '-')
                            .replace(/^-+|-+$/g, '');
                        slugField.value = slug;
                        slugField.dataset.autoGenerated = 'true';
                    }
                });

                slugField.addEventListener('input', function() {
                    this.dataset.autoGenerated = 'false';
                });
            }

            // Character counters
            function initCharCounter(target, max) {
                const field = document.getElementById(target);
                const counter = document.querySelector(`[data-target="${target}"]`);

                if (field && counter) {
                    const countText = counter.querySelector('.count-text');
                    const countProgress = counter.querySelector('.count-progress');

                    function updateCounter() {
                        const length = field.value.length;
                        const percentage = (length / max) * 100;

                        countText.textContent = `${length} / ${max} characters`;
                        countProgress.style.width = `${Math.min(percentage, 100)}%`;

                        // Update counter styling based on length
                        counter.className = 'char-count';
                        if (length > max) {
                            counter.classList.add('over-limit');
                        } else if (length > max * 0.8) {
                            counter.classList.add('near-limit');
                        } else {
                            counter.classList.add('normal');
                        }
                    }

                    field.addEventListener('input', updateCounter);
                    updateCounter(); // Initial count
                }
            }

            // Initialize character counters
            initCharCounter('meta_title', 60);
            initCharCounter('meta_description', 160);

            // Modern file upload
            const fileInput = document.getElementById('featured_image');
            const fileUploadArea = document.querySelector('.file-upload-area');
            const filePreview = document.querySelector('.file-preview');

            if (fileInput && fileUploadArea) {
                // Drag and drop functionality
                fileUploadArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('drag-over');
                });

                fileUploadArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('drag-over');
                });

                fileUploadArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('drag-over');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        fileInput.files = files;
                        handleFileSelect(files[0]);
                    }
                });

                // File input change
                fileInput.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        handleFileSelect(e.target.files[0]);
                    }
                });

                // Handle file selection
                function handleFileSelect(file) {
                    if (file && file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewImage = filePreview.querySelector('.preview-image');
                            const fileName = filePreview.querySelector('.file-name');
                            const fileSize = filePreview.querySelector('.file-size');

                            previewImage.style.backgroundImage = `url(${e.target.result})`;
                            fileName.textContent = file.name;
                            fileSize.textContent = formatFileSize(file.size);

                            fileUploadArea.style.display = 'none';
                            filePreview.style.display = 'flex';
                        };
                        reader.readAsDataURL(file);
                    }
                }

                // Remove file
                const removeFileBtn = filePreview.querySelector('.remove-file');
                if (removeFileBtn) {
                    removeFileBtn.addEventListener('click', function() {
                        fileInput.value = '';
                        fileUploadArea.style.display = 'flex';
                        filePreview.style.display = 'none';
                    });
                }

                // Format file size
                function formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            }

            // Enhanced form validation
            const form = document.querySelector('.modern-project-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.classList.add('error');
                            isValid = false;
                        } else {
                            field.classList.remove('error');
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        // Scroll to first error
                        const firstError = form.querySelector('.error');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            firstError.focus();
                        }
                    }
                });
            }

            // View toggle functionality (for future grid view)
            const viewToggles = document.querySelectorAll('.view-toggle');
            viewToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    viewToggles.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    const view = this.dataset.view;
                    // Future implementation for grid/table view switching
                    console.log('Switching to view:', view);
                });
            });
        });
    </script>
</body>
</html>
