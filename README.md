# Flori Construction Ltd - Modern Website & Mobile App

A complete modern website and mobile app solution for Flori Construction Ltd, built with PHP, MySQL, and Progressive Web App (PWA) technology.

## 🚀 Features

### Website Features
- **Responsive Design**: Mobile-first approach with modern CSS Grid and Flexbox
- **Dynamic Content Management**: Easy-to-update content through admin panel
- **Project Showcase**: Completed and ongoing projects with image galleries
- **Service Pages**: Detailed information about construction services
- **Contact System**: Contact form with email notifications
- **SEO Optimized**: Meta tags, structured data, and clean URLs
- **Fast Loading**: Optimized images and efficient caching

### Mobile App Features (PWA)
- **Progressive Web App**: Installable on mobile devices
- **Offline Functionality**: Works without internet connection
- **Real-time Sync**: Changes sync between mobile app and website
- **Media Upload**: Camera integration for project photos
- **Content Management**: Update projects, services, and content
- **Push Notifications**: Stay updated with new inquiries
- **Responsive Design**: Works on all screen sizes

### Admin Panel Features
- **Dashboard**: Overview of projects, media, and inquiries
- **Project Management**: Add, edit, and delete projects
- **Media Library**: Upload and manage images and videos
- **Content Editor**: Update website content dynamically
- **User Management**: Admin and editor roles
- **Contact Inquiries**: View and manage contact form submissions

## 📋 Requirements

- **Web Server**: Apache or Nginx
- **PHP**: Version 7.4 or higher
- **MySQL**: Version 5.7 or higher
- **Extensions**: PDO, GD, JSON, OpenSSL

## 🛠️ Installation

### 1. Database Setup

1. Create a new MySQL database:
```sql
CREATE DATABASE flori_construction;
```

2. Import the database schema:
```bash
mysql -u your_username -p flori_construction < database/schema.sql
```

3. Update database credentials in `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'flori_construction';
private $username = 'your_username';
private $password = 'your_password';
```

### 2. File Permissions

Set proper permissions for upload directories:
```bash
chmod 755 uploads/
chmod 755 uploads/projects/
chmod 755 uploads/services/
chmod 755 uploads/gallery/
chmod 755 uploads/general/
```

### 3. Configuration

1. Update site configuration in `config/config.php`:
```php
define('SITE_URL', 'http://your-domain.com');
define('SITE_EMAIL', '<EMAIL>');
```

2. Configure email settings for contact forms:
```php
define('SMTP_HOST', 'your-smtp-host');
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-email-password');
```

### 4. Security

1. Change the default admin password:
   - Login with username: `admin`, password: `admin123`
   - Go to Settings > Change Password
   - Update to a strong password

2. Update JWT secret in `config/config.php`:
```php
define('JWT_SECRET', 'your-unique-secret-key');
```

## 📱 Mobile App Setup

### 1. PWA Installation

The mobile app is a Progressive Web App that can be installed on mobile devices:

1. Open `your-domain.com/mobile-app/` in a mobile browser
2. Tap "Add to Home Screen" when prompted
3. The app will be installed like a native app

### 2. Push Notifications (Optional)

To enable push notifications:

1. Set up Firebase Cloud Messaging (FCM)
2. Add your FCM configuration to `mobile-app/js/notifications.js`
3. Update the service worker with your FCM keys

## 🎯 Usage

### Admin Panel Access

1. **Web Admin**: Visit `your-domain.com/admin/`
2. **Mobile App**: Visit `your-domain.com/mobile-app/`

**Default Login:**
- Username: `admin`
- Password: `admin123`

### Managing Content

#### Projects
1. Go to Projects section
2. Click "Add Project" to create new projects
3. Upload images and set project details
4. Projects automatically appear on the website

#### Media
1. Upload images and videos in Media section
2. Organize files by categories (projects, services, gallery)
3. Media can be used across the website

#### Content
1. Edit dynamic content sections
2. Update company information
3. Modify hero section text and images

### API Endpoints

The system provides REST API endpoints for the mobile app:

- `POST /api/auth.php` - Authentication
- `GET/POST/PUT/DELETE /api/projects.php` - Project management
- `GET/POST/DELETE /api/media.php` - Media management
- `GET/PUT /api/content.php` - Content management

## 🔧 Customization

### Styling

1. **Website Styles**: Edit `assets/css/style.css` and `assets/css/responsive.css`
2. **Admin Styles**: Edit `assets/css/admin.css`
3. **Mobile App Styles**: Edit `mobile-app/css/app.css`

### Functionality

1. **PHP Backend**: Modify files in `api/` and `includes/`
2. **JavaScript**: Update files in `assets/js/` and `mobile-app/js/`
3. **Database**: Add new tables or modify existing ones in `database/`

## 📊 Performance Optimization

### Image Optimization
- Images are automatically resized and compressed
- WebP format support for modern browsers
- Lazy loading for better performance

### Caching
- Browser caching for static assets
- Service worker caching for PWA
- Database query optimization

### SEO
- Clean URLs with mod_rewrite
- Meta tags and Open Graph support
- Structured data markup
- XML sitemap generation

## 🔒 Security Features

- **SQL Injection Protection**: Prepared statements
- **XSS Prevention**: Input sanitization and output escaping
- **CSRF Protection**: Token-based form validation
- **Authentication**: Secure password hashing
- **File Upload Security**: Type and size validation
- **API Security**: JWT token authentication

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running

2. **File Upload Issues**
   - Check directory permissions (755)
   - Verify PHP upload limits in `php.ini`

3. **Mobile App Not Loading**
   - Check service worker registration
   - Verify HTTPS for PWA features

4. **Email Not Sending**
   - Configure SMTP settings in `config/config.php`
   - Check firewall and port settings

### Debug Mode

Enable debug mode in `config/config.php`:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📞 Support

For technical support or customization requests:

- **Email**: <EMAIL>
- **Phone**: 0208 914 7883
- **Website**: https://floriconstructionltd.com

## 📄 License

This project is proprietary software developed for Flori Construction Ltd. All rights reserved.

## 🔄 Updates

### Version 1.0.0
- Initial release with website and mobile app
- Admin panel and API endpoints
- PWA functionality and offline support

### Planned Features
- Advanced analytics dashboard
- Client portal for project updates
- Integration with accounting software
- Multi-language support
- Advanced SEO tools

---

**Built with ❤️ for Flori Construction Ltd**
