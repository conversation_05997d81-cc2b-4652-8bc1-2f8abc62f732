<?php
/**
 * Debug Authentication Issues
 * This script helps diagnose mobile app authentication problems
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Debug - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔍 Authentication Debug Tool</h1>
    <p>This tool helps diagnose issues with the mobile app authentication system.</p>

    <?php
    echo "<div class='test-section'>";
    echo "<h2>1. Database Connection Test</h2>";
    
    try {
        require_once 'config/config.php';
        echo "<div class='success'>✅ Config loaded successfully</div>";
        
        // Test database connection
        $testQuery = $db->fetchOne("SELECT 1 as test");
        if ($testQuery && $testQuery['test'] == 1) {
            echo "<div class='success'>✅ Database connection working</div>";
        } else {
            echo "<div class='error'>❌ Database connection failed</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Config/Database error: " . $e->getMessage() . "</div>";
        echo "</div>";
        exit;
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>2. Required Tables Check</h2>";
    
    try {
        $tables = $db->fetchAll("SHOW TABLES");
        $tableNames = array_column($tables, array_keys($tables[0])[0]);
        
        $requiredTables = ['users', 'api_tokens'];
        $missingTables = [];
        
        foreach ($requiredTables as $table) {
            if (in_array($table, $tableNames)) {
                echo "<div class='success'>✅ Table '$table' exists</div>";
            } else {
                echo "<div class='error'>❌ Table '$table' missing</div>";
                $missingTables[] = $table;
            }
        }
        
        if (!empty($missingTables)) {
            echo "<div class='warning'>⚠️ Missing tables detected. Please run the database setup.</div>";
            echo "<p><a href='setup.php' class='btn'>Run Database Setup</a></p>";
            echo "<p><a href='database/update-schema.php' class='btn'>Run Schema Update</a></p>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Table check failed: " . $e->getMessage() . "</div>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>3. Users Table Check</h2>";
    
    try {
        $users = $db->fetchAll("SELECT id, username, email, full_name, role, is_active FROM users WHERE is_active = 1");
        
        if (empty($users)) {
            echo "<div class='error'>❌ No active users found</div>";
            echo "<div class='info'>You need to create an admin user. Default credentials should be:</div>";
            echo "<pre>Username: admin\nPassword: admin123</pre>";
        } else {
            echo "<div class='success'>✅ Found " . count($users) . " active user(s):</div>";
            echo "<ul>";
            foreach ($users as $user) {
                echo "<li><strong>{$user['username']}</strong> ({$user['email']}) - Role: {$user['role']}</li>";
            }
            echo "</ul>";
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Users check failed: " . $e->getMessage() . "</div>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>4. API Authentication Test</h2>";
    
    // Test login API
    echo "<div class='info'>Testing login API with default credentials...</div>";
    
    $testCredentials = [
        'username' => 'admin',
        'password' => 'admin123'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/erdevwe/api/auth.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testCredentials));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testCredentials))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    echo "<div class='info'><strong>HTTP Status Code:</strong> $httpCode</div>";
    
    if ($httpCode == 200) {
        echo "<div class='success'>✅ API responded successfully</div>";
        $data = json_decode($body, true);
        if ($data && isset($data['success']) && $data['success']) {
            echo "<div class='success'>✅ Login successful! Token generated.</div>";
            echo "<pre>Response: " . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<div class='error'>❌ Login failed</div>";
            echo "<pre>Response: " . htmlspecialchars($body) . "</pre>";
        }
    } else {
        echo "<div class='error'>❌ API request failed (HTTP $httpCode)</div>";
        echo "<pre>Headers:\n" . htmlspecialchars($headers) . "\n\nBody:\n" . htmlspecialchars($body) . "</pre>";
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>5. Mobile App Files Check</h2>";
    
    $mobileFiles = [
        'mobile-app/index.html',
        'mobile-app/js/app.js',
        'mobile-app/js/auth.js',
        'api/auth.php'
    ];
    
    foreach ($mobileFiles as $file) {
        if (file_exists($file)) {
            echo "<div class='success'>✅ $file exists</div>";
        } else {
            echo "<div class='error'>❌ $file missing</div>";
        }
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>6. Server Configuration</h2>";
    
    echo "<div class='info'><strong>PHP Version:</strong> " . PHP_VERSION . "</div>";
    echo "<div class='info'><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</div>";
    
    $extensions = ['pdo', 'pdo_mysql', 'curl', 'json'];
    foreach ($extensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<div class='success'>✅ $ext extension loaded</div>";
        } else {
            echo "<div class='error'>❌ $ext extension missing</div>";
        }
    }
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>7. Quick Fixes</h2>";
    
    echo "<div class='info'>If you're experiencing issues, try these solutions:</div>";
    echo "<ol>";
    echo "<li><strong>Database Setup:</strong> <a href='setup.php' class='btn'>Run Setup</a></li>";
    echo "<li><strong>Update Schema:</strong> <a href='database/update-schema.php' class='btn'>Update Schema</a></li>";
    echo "<li><strong>Test Connection:</strong> <a href='test-connection.php' class='btn'>Test Connection</a></li>";
    echo "<li><strong>Mobile App:</strong> <a href='mobile-app/' class='btn'>Open Mobile App</a></li>";
    echo "</ol>";
    
    echo "<div class='warning'>";
    echo "<strong>Common Issues:</strong><br>";
    echo "• <strong>401 Unauthorized:</strong> Usually means the database tables are missing or the user credentials are wrong<br>";
    echo "• <strong>500 Server Error:</strong> Check PHP error logs for detailed error messages<br>";
    echo "• <strong>Database Connection Failed:</strong> Check database credentials in config/database.php<br>";
    echo "• <strong>Missing Tables:</strong> Run the database setup or schema update scripts<br>";
    echo "</div>";
    echo "</div>";

    echo "<div class='test-section'>";
    echo "<h2>8. Manual Login Test</h2>";
    echo "<form method='POST' action='api/auth.php' target='_blank'>";
    echo "<p><label>Username: <input type='text' name='username' value='admin' style='margin-left: 10px; padding: 5px;'></label></p>";
    echo "<p><label>Password: <input type='password' name='password' value='admin123' style='margin-left: 10px; padding: 5px;'></label></p>";
    echo "<p><button type='submit' class='btn'>Test Login API</button></p>";
    echo "</form>";
    echo "<div class='info'>This will open the API response in a new tab. You should see a JSON response with success: true and a token.</div>";
    echo "</div>";
    ?>

    <script>
        // Auto-refresh every 30 seconds if there are errors
        const errors = document.querySelectorAll('.error');
        if (errors.length > 0) {
            console.log('Errors detected, will auto-refresh in 30 seconds');
            setTimeout(() => {
                location.reload();
            }, 30000);
        }
    </script>
</body>
</html>
