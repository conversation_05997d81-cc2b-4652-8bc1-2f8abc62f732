# 🚀 Flori Construction Website - Enhanced Features Setup Guide

This guide will help you set up and use the new enhanced features for your construction website.

## 📋 Prerequisites

Before starting, ensure you have:
- ✅ XAMPP or similar local server environment
- ✅ PHP 7.4 or higher
- ✅ MySQL database
- ✅ Basic website already installed and working

## 🔧 Installation Steps

### Step 1: Update Database Schema

1. **Run the database update script:**
   - Open your browser and go to: `http://localhost/erdevwe/database/update-schema.php`
   - This will automatically create the new tables and add required columns
   - You should see success messages for each table created

2. **Verify the update:**
   - Check that these new tables exist in your database:
     - `site_settings` (for branding and configuration)
     - `site_content` (for editable content)
     - `page_seo` (for SEO meta tags)
   - The `contact_inquiries` table should have new columns for enhanced tracking

### Step 2: Access New Admin Features

1. **Login to Admin Panel:**
   - Go to: `http://localhost/erdevwe/admin/`
   - Use your existing admin credentials

2. **New Admin Pages Available:**
   - 🎨 **Branding** (`admin/branding.php`) - Customize colors, fonts, and logo
   - 📝 **Content** (`admin/content.php`) - Edit website text content
   - 📧 **Email Test** (`admin/email-test.php`) - Test contact form emails
   - 📊 **Enhanced Media** (`admin/media.php`) - Improved file upload with bulk support

## 🎨 1. Customize Branding

### Colors
- **Primary Color**: Main brand color (buttons, links, accents)
- **Secondary Color**: Header/footer background
- **Accent Color**: Secondary buttons and highlights
- **Text Color**: Main text color
- **Background Color**: Page background

### Fonts
- **Heading Font**: For titles and headings
- **Body Font**: For regular text content

### Logo
- Upload your company logo (PNG recommended)
- Optimal size: 200x100px with transparent background

**How to use:**
1. Go to `Admin Panel > Branding`
2. Adjust colors using the color pickers
3. Select fonts from the dropdown menus
4. Upload your logo file
5. Preview changes in real-time
6. Click "Update" buttons to save changes

## 📸 2. Add Real Images

### Bulk Upload Feature
- Select multiple files at once
- Drag and drop support
- Automatic categorization (Projects, Services, Gallery, General)
- Image optimization and metadata management

**How to upload images:**
1. Go to `Admin Panel > Media`
2. Click "Upload Media"
3. Select multiple files or drag them to the upload area
4. Fill in titles, descriptions, and alt text for SEO
5. Choose appropriate category
6. Click "Upload Media"

### Organize Your Images
- **Projects**: Photos of completed and ongoing projects
- **Services**: Images representing your services
- **Gallery**: General company and work photos
- **General**: Logos, team photos, certificates

## 📧 3. Test Contact Form

### Email Configuration
The system uses PHP's built-in mail() function by default. For production, you may want to configure SMTP.

**Test your email setup:**
1. Go to `Admin Panel > Email Test`
2. Enter your email address
3. Click "Send Test Email"
4. Check your inbox (and spam folder)

### Contact Form Features
- **Admin Notifications**: You receive an email when someone submits the form
- **Auto-Reply**: Customers receive a confirmation email
- **Enhanced Tracking**: IP address, user agent, and referral tracking
- **Service Integration**: Forms can be pre-filled from service/project pages

**Test the complete workflow:**
1. Go to `Admin Panel > Email Test`
2. Use "Test Contact Form Emails"
3. This sends both notification and auto-reply emails

## 📝 4. Review and Edit Content

### Content Management System
Edit website text without touching code files.

**Available Content Sections:**
- **Homepage**: Hero title, subtitle, section headings
- **About Page**: Company description and mission
- **Services**: Service descriptions and introductions
- **Projects**: Project showcase text
- **Contact**: Contact form instructions and information

**How to edit content:**
1. Go to `Admin Panel > Content`
2. Use the tabs to navigate between page sections
3. Edit text in the provided fields
4. Use the rich text editor for formatted content
5. Click "Update" to save changes
6. Preview changes on the live website

### SEO Management
Optimize your website for search engines:

**For each page, you can set:**
- **Meta Title**: Appears in search results and browser tabs
- **Meta Description**: Summary shown in search results
- **Meta Keywords**: Relevant keywords for the page

**SEO Best Practices:**
- Keep titles under 60 characters
- Keep descriptions under 160 characters
- Use relevant keywords naturally
- Make each page's meta data unique

## 🔍 Testing Your Setup

### 1. Test Image Upload
- Upload a few sample images
- Verify they appear in the media library
- Check that thumbnails are generated correctly

### 2. Test Branding Changes
- Change a color and verify it appears on the website
- Upload a logo and check it displays correctly
- Try different font combinations

### 3. Test Contact Form
- Fill out the contact form on your website
- Verify you receive the notification email
- Check that the auto-reply is sent to the customer
- Confirm the inquiry appears in `Admin Panel > Inquiries`

### 4. Test Content Changes
- Edit a homepage title
- Refresh the website to see the change
- Test the rich text editor with formatting

## 🚨 Troubleshooting

### Email Issues
**Problem**: Emails not being sent
**Solutions**:
- Check if PHP mail() function is enabled
- Verify your server's mail configuration
- Check spam/junk folders
- Contact your hosting provider about email sending

**Problem**: Emails going to spam
**Solutions**:
- Set up SPF, DKIM, and DMARC records
- Use a professional email address (not Gmail/Yahoo)
- Consider using a transactional email service

### Upload Issues
**Problem**: Files not uploading
**Solutions**:
- Check file size limits in PHP configuration
- Verify upload directory permissions (755)
- Ensure file types are allowed
- Check available disk space

### Database Issues
**Problem**: New features not working
**Solutions**:
- Re-run the database update script
- Check database connection settings
- Verify all new tables were created
- Check PHP error logs

## 📞 Support

If you encounter any issues:

1. **Check the browser console** for JavaScript errors
2. **Check PHP error logs** for server-side issues
3. **Verify file permissions** are set correctly
4. **Test with different browsers** to isolate issues

For technical support:
- **Email**: <EMAIL>
- **Phone**: 0208 914 7883

## 🎯 Next Steps

After setting up these features:

1. **Upload your real project photos** to replace placeholder images
2. **Customize the branding** to match your company colors and fonts
3. **Test the contact form** thoroughly with different scenarios
4. **Update all content** to reflect your actual services and information
5. **Optimize SEO settings** for better search engine visibility
6. **Train your team** on using the admin panel

## 📈 Advanced Features

### Mobile App Integration
Your website includes a Progressive Web App (PWA) that works on mobile devices:
- Accessible at: `http://localhost/erdevwe/mobile-app/`
- Allows content management from mobile devices
- Works offline with cached content

### API Endpoints
The system includes API endpoints for future integrations:
- Contact form submissions
- Project data
- Media management
- User authentication

---

**Congratulations!** 🎉 Your construction website now has professional-grade content management, branding customization, and email functionality. Take some time to explore all the features and customize them to match your business needs.
