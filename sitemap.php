<?php
/**
 * XML Sitemap Generator for Flori Construction Ltd
 * Dynamically generates sitemap based on database content
 */

require_once 'config/config.php';

// Set XML header
header('Content-Type: application/xml; charset=utf-8');

// Get base URL
$baseUrl = SITE_URL;

// Start XML output
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

// Static pages
$staticPages = [
    '' => ['priority' => '1.0', 'changefreq' => 'weekly'],
    'about' => ['priority' => '0.8', 'changefreq' => 'monthly'],
    'services' => ['priority' => '0.9', 'changefreq' => 'weekly'],
    'projects' => ['priority' => '0.9', 'changefreq' => 'weekly'],
    'contact' => ['priority' => '0.7', 'changefreq' => 'monthly'],
    'gallery' => ['priority' => '0.6', 'changefreq' => 'weekly']
];

foreach ($staticPages as $page => $meta) {
    $url = $baseUrl . ($page ? '/' . $page : '');
    $lastmod = date('Y-m-d');
    
    echo "  <url>\n";
    echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
    echo "    <lastmod>" . $lastmod . "</lastmod>\n";
    echo "    <changefreq>" . $meta['changefreq'] . "</changefreq>\n";
    echo "    <priority>" . $meta['priority'] . "</priority>\n";
    echo "  </url>\n";
}

try {
    // Dynamic pages - Projects
    $projects = $db->fetchAll(
        "SELECT slug, updated_at FROM projects WHERE is_active = 1 ORDER BY updated_at DESC"
    );
    
    foreach ($projects as $project) {
        $url = $baseUrl . '/project/' . $project['slug'];
        $lastmod = date('Y-m-d', strtotime($project['updated_at']));
        
        echo "  <url>\n";
        echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        echo "    <lastmod>" . $lastmod . "</lastmod>\n";
        echo "    <changefreq>monthly</changefreq>\n";
        echo "    <priority>0.8</priority>\n";
        echo "  </url>\n";
    }
    
    // Dynamic pages - Services
    $services = $db->fetchAll(
        "SELECT slug, updated_at FROM services WHERE is_active = 1 ORDER BY updated_at DESC"
    );
    
    foreach ($services as $service) {
        $url = $baseUrl . '/service/' . $service['slug'];
        $lastmod = date('Y-m-d', strtotime($service['updated_at']));
        
        echo "  <url>\n";
        echo "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        echo "    <lastmod>" . $lastmod . "</lastmod>\n";
        echo "    <changefreq>monthly</changefreq>\n";
        echo "    <priority>0.7</priority>\n";
        echo "  </url>\n";
    }
    
} catch (Exception $e) {
    // Log error but continue with static pages
    error_log("Sitemap generation error: " . $e->getMessage());
}

echo '</urlset>' . "\n";
?>
