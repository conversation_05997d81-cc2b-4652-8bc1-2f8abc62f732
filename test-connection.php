<?php
/**
 * Database Connection Test for Flori Construction Ltd
 * Use this to test your database connection
 */

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>Database Connection Test</h1>";

// Test basic PHP functionality
echo "<h2>PHP Environment</h2>";
echo "<div class='info'>PHP Version: " . phpversion() . "</div>";

// Check required extensions
$required_extensions = ['pdo', 'pdo_mysql', 'json'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<div class='success'>✓ Extension '$ext' is loaded</div>";
    } else {
        echo "<div class='error'>✗ Extension '$ext' is NOT loaded</div>";
    }
}

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'flori_construction';

echo "<h2>Database Configuration</h2>";
echo "<pre>";
echo "Host: $host\n";
echo "Username: $username\n";
echo "Password: " . (empty($password) ? '(empty)' : '(set)') . "\n";
echo "Database: $database";
echo "</pre>";

// Test MySQL server connection
echo "<h2>MySQL Server Connection</h2>";
try {
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✓ Successfully connected to MySQL server</div>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='success'>✓ Database '$database' exists</div>";
        
        // Connect to specific database
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "<div class='success'>✓ Successfully connected to database '$database'</div>";
            
            // Check tables
            echo "<h2>Database Tables</h2>";
            $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($tables)) {
                echo "<div class='error'>No tables found in database. Please run setup.php first.</div>";
            } else {
                echo "<div class='success'>Found " . count($tables) . " tables:</div>";
                echo "<ul>";
                foreach ($tables as $table) {
                    echo "<li>$table</li>";
                }
                echo "</ul>";
                
                // Test users table specifically
                if (in_array('users', $tables)) {
                    echo "<h2>Users Table Test</h2>";
                    try {
                        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
                        $count = $stmt->fetch()['count'];
                        echo "<div class='success'>✓ Users table has $count records</div>";
                        
                        // Check if admin user exists
                        $stmt = $pdo->prepare("SELECT username, email, full_name FROM users WHERE username = 'admin'");
                        $stmt->execute();
                        $admin = $stmt->fetch();
                        
                        if ($admin) {
                            echo "<div class='success'>✓ Admin user found:</div>";
                            echo "<pre>";
                            echo "Username: " . $admin['username'] . "\n";
                            echo "Email: " . $admin['email'] . "\n";
                            echo "Full Name: " . $admin['full_name'];
                            echo "</pre>";
                        } else {
                            echo "<div class='error'>✗ Admin user not found</div>";
                        }
                        
                    } catch (PDOException $e) {
                        echo "<div class='error'>Error querying users table: " . $e->getMessage() . "</div>";
                    }
                }
            }
            
        } catch (PDOException $e) {
            echo "<div class='error'>Failed to connect to database '$database': " . $e->getMessage() . "</div>";
        }
        
    } else {
        echo "<div class='error'>Database '$database' does not exist. Please run setup.php first.</div>";
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>Failed to connect to MySQL server: " . $e->getMessage() . "</div>";
    
    echo "<h2>Troubleshooting Tips</h2>";
    echo "<div class='info'>";
    echo "<ul>";
    echo "<li>Make sure XAMPP/WAMP is running</li>";
    echo "<li>Check if MySQL service is started</li>";
    echo "<li>Verify database credentials</li>";
    echo "<li>Try accessing phpMyAdmin</li>";
    echo "</ul>";
    echo "</div>";
}

// Test file permissions
echo "<h2>File Permissions</h2>";
$directories = ['uploads', 'uploads/projects', 'uploads/services', 'uploads/gallery', 'uploads/general'];

foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<div class='success'>✓ Directory '$dir' is writable</div>";
        } else {
            echo "<div class='error'>✗ Directory '$dir' is not writable</div>";
        }
    } else {
        if (mkdir($dir, 0755, true)) {
            echo "<div class='success'>✓ Created directory '$dir'</div>";
        } else {
            echo "<div class='error'>✗ Failed to create directory '$dir'</div>";
        }
    }
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ul>";
echo "<li>If all tests pass, visit <a href='admin/login.php'>admin/login.php</a></li>";
echo "<li>If database doesn't exist, run <a href='setup.php'>setup.php</a></li>";
echo "<li>If there are errors, check the troubleshooting tips above</li>";
echo "</ul>";

echo "</body></html>";
?>
