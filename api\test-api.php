<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flori Construction API Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
        }
        .response {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }
        .success {
            border-left-color: #27ae60;
        }
        .error {
            border-left-color: #e74c3c;
        }
        .token-display {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ Flori Construction API Tester</h1>
        
        <div class="test-section">
            <h3>🔐 Authentication</h3>
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" value="admin" placeholder="Enter username">
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" placeholder="Enter password">
            </div>
            <button onclick="testLogin()">Login</button>
            <button onclick="testVerifyToken()">Verify Token</button>
            <button onclick="testLogout()">Logout</button>
            
            <div id="token-display" class="token-display" style="display: none;">
                <strong>Token:</strong> <span id="current-token"></span>
            </div>
            <div id="auth-response" class="response" style="display: none;"></div>
        </div>

        <div class="grid">
            <div class="test-section">
                <h3>📊 Dashboard</h3>
                <button onclick="testDashboard()">Get Dashboard Data</button>
                <div id="dashboard-response" class="response" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>🏗️ Projects</h3>
                <button onclick="testGetProjects()">Get Projects</button>
                <button onclick="testCreateProject()">Create Test Project</button>
                <div id="projects-response" class="response" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>📸 Media</h3>
                <button onclick="testGetMedia()">Get Media</button>
                <div class="form-group">
                    <label for="test-file">Upload Test File:</label>
                    <input type="file" id="test-file" accept="image/*">
                </div>
                <button onclick="testUploadMedia()">Upload Media</button>
                <div id="media-response" class="response" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>📄 Content</h3>
                <button onclick="testGetContent()">Get Content</button>
                <div id="content-response" class="response" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>🔔 Notifications</h3>
                <button onclick="testGetNotifications()">Get Notifications</button>
                <button onclick="testSendNotification()">Send Test Notification</button>
                <div id="notifications-response" class="response" style="display: none;"></div>
            </div>

            <div class="test-section">
                <h3>🔄 Sync</h3>
                <button onclick="testGetSyncData()">Get Sync Data</button>
                <button onclick="testSyncData()">Test Sync</button>
                <div id="sync-response" class="response" style="display: none;"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>🛠️ Database</h3>
            <button onclick="testInitDatabase()">Initialize Database</button>
            <div id="database-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        let currentToken = localStorage.getItem('flori_test_token') || '';
        
        if (currentToken) {
            document.getElementById('token-display').style.display = 'block';
            document.getElementById('current-token').textContent = currentToken.substring(0, 50) + '...';
        }

        async function apiRequest(endpoint, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(currentToken && { 'Authorization': `Bearer ${currentToken}` })
                }
            };

            const mergedOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            try {
                const response = await fetch(endpoint, mergedOptions);
                const data = await response.json();
                return { status: response.status, data };
            } catch (error) {
                return { status: 0, data: { error: error.message } };
            }
        }

        function displayResponse(elementId, response) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = JSON.stringify(response, null, 2);
            element.className = 'response ' + (response.data.success ? 'success' : 'error');
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            const response = await apiRequest('auth.php?action=login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });

            if (response.data.success && response.data.token) {
                currentToken = response.data.token;
                localStorage.setItem('flori_test_token', currentToken);
                document.getElementById('token-display').style.display = 'block';
                document.getElementById('current-token').textContent = currentToken.substring(0, 50) + '...';
            }

            displayResponse('auth-response', response);
        }

        async function testVerifyToken() {
            const response = await apiRequest('auth.php?action=verify');
            displayResponse('auth-response', response);
        }

        async function testLogout() {
            const response = await apiRequest('auth.php?action=logout', { method: 'POST' });
            if (response.data.success) {
                currentToken = '';
                localStorage.removeItem('flori_test_token');
                document.getElementById('token-display').style.display = 'none';
            }
            displayResponse('auth-response', response);
        }

        async function testDashboard() {
            const response = await apiRequest('mobile.php?action=dashboard');
            displayResponse('dashboard-response', response);
        }

        async function testGetProjects() {
            const response = await apiRequest('mobile.php?action=projects&page=1&limit=10');
            displayResponse('projects-response', response);
        }

        async function testCreateProject() {
            const projectData = {
                title: 'Test Project ' + Date.now(),
                description: 'This is a test project created via API',
                location: 'London, UK',
                project_type: 'ongoing',
                client_name: 'Test Client'
            };

            const response = await apiRequest('mobile.php?action=project', {
                method: 'POST',
                body: JSON.stringify(projectData)
            });

            displayResponse('projects-response', response);
        }

        async function testGetMedia() {
            const response = await apiRequest('mobile.php?action=media&page=1&limit=10');
            displayResponse('media-response', response);
        }

        async function testUploadMedia() {
            const fileInput = document.getElementById('test-file');
            if (!fileInput.files[0]) {
                alert('Please select a file first');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('alt_text', 'Test image uploaded via API');
            formData.append('caption', 'This is a test upload');

            const response = await apiRequest('mobile.php?action=upload', {
                method: 'POST',
                body: formData,
                headers: {
                    ...(currentToken && { 'Authorization': `Bearer ${currentToken}` })
                }
            });

            displayResponse('media-response', response);
        }

        async function testGetContent() {
            const response = await apiRequest('mobile.php?action=content&page=1&limit=10');
            displayResponse('content-response', response);
        }

        async function testGetNotifications() {
            const response = await apiRequest('notifications.php?action=history&page=1&limit=10');
            displayResponse('notifications-response', response);
        }

        async function testSendNotification() {
            const notificationData = {
                action: 'send',
                title: 'Test Notification',
                body: 'This is a test notification sent via API at ' + new Date().toLocaleTimeString(),
                data: { test: true, timestamp: Date.now() }
            };

            const response = await apiRequest('notifications.php', {
                method: 'POST',
                body: JSON.stringify(notificationData)
            });

            displayResponse('notifications-response', response);
        }

        async function testGetSyncData() {
            const response = await apiRequest('mobile.php?action=sync&last_sync=2024-01-01 00:00:00');
            displayResponse('sync-response', response);
        }

        async function testSyncData() {
            const syncData = {
                projects: [
                    {
                        title: 'Synced Project ' + Date.now(),
                        description: 'This project was synced via API',
                        project_type: 'completed'
                    }
                ]
            };

            const response = await apiRequest('mobile.php?action=sync', {
                method: 'POST',
                body: JSON.stringify(syncData)
            });

            displayResponse('sync-response', response);
        }

        async function testInitDatabase() {
            const response = await apiRequest('init-database.php');
            displayResponse('database-response', response);
        }
    </script>
</body>
</html>
