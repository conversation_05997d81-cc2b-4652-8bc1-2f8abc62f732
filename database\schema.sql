-- Flori Construction Ltd Database Schema
-- Created for modern website and mobile app

CREATE DATABASE IF NOT EXISTS flori_construction;
USE flori_construction;

-- Users table for admin authentication
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HAR(100) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Services table
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    featured_image VARCHAR(255),
    gallery JSON,
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    meta_title VARCHAR(100),
    meta_description VARCHAR(160),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Projects table
CREATE TABLE projects (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(150) NOT NULL,
    slug VARCHAR(150) UNIQUE NOT NULL,
    description TEXT,
    short_description VARCHAR(255),
    client_name VARCHAR(100),
    location VARCHAR(150),
    project_type ENUM('completed', 'ongoing') NOT NULL,
    start_date DATE,
    end_date DATE,
    featured_image VARCHAR(255),
    gallery JSON,
    services JSON, -- Array of service IDs
    project_value DECIMAL(12,2),
    is_featured BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    meta_title VARCHAR(100),
    meta_description VARCHAR(160),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Media table for centralized media management
CREATE TABLE media (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    alt_text VARCHAR(255),
    caption TEXT,
    uploaded_by INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Content management for dynamic pages
CREATE TABLE content (
    id INT PRIMARY KEY AUTO_INCREMENT,
    section_key VARCHAR(100) UNIQUE NOT NULL,
    title VARCHAR(200),
    content TEXT,
    meta_data JSON,
    is_active BOOLEAN DEFAULT TRUE,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Testimonials/Reviews
CREATE TABLE testimonials (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_name VARCHAR(100) NOT NULL,
    company VARCHAR(100),
    rating INT CHECK (rating >= 1 AND rating <= 5),
    review TEXT NOT NULL,
    project_id INT,
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);

-- Contact inquiries
CREATE TABLE contact_inquiries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(100),
    subject VARCHAR(150),
    message TEXT NOT NULL,
    inquiry_type ENUM('general', 'quote', 'support') DEFAULT 'general',
    status ENUM('new', 'contacted', 'closed') DEFAULT 'new',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- API tokens for mobile app authentication
CREATE TABLE api_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123 - should be changed)
-- Insert default editor user (password: editor123 - should be changed)
INSERT INTO users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'admin'),
('editor', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Editor', 'editor');

-- Insert default services
INSERT INTO services (title, slug, description, short_description, is_featured, sort_order) VALUES
('Civil Engineering', 'civil-engineering', 'Comprehensive civil engineering services for infrastructure development and construction projects.', 'Professional civil engineering solutions for complex infrastructure projects.', TRUE, 1),
('Groundworks', 'groundworks', 'Expert groundwork services including excavation, foundations, and site preparation.', 'Professional groundwork and foundation services.', TRUE, 2),
('RC Frames', 'rc-frames', 'Reinforced concrete frame construction for residential and commercial buildings.', 'Reinforced concrete frame construction services.', TRUE, 3),
('Basements', 'basements', 'Specialist basement construction and waterproofing services.', 'Professional basement construction and waterproofing.', TRUE, 4),
('Hard Landscaping', 'hard-landscaping', 'Professional hard landscaping services including paving, retaining walls, and outdoor structures.', 'Expert hard landscaping and outdoor construction.', TRUE, 5);

-- Insert default content sections
INSERT INTO content (section_key, title, content) VALUES
('hero_title', 'DO IT WITH PASSION', 'or not at all'),
('hero_subtitle', 'Quality Construction Services', 'We take pride in delivering exceptional construction solutions'),
('about_intro', 'About our company', 'Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.'),
('company_address', 'Company Address', '662 High Road North Finchley, London N12 0NL'),
('company_phone', 'Phone Number', '0208 914 7883'),
('company_mobile', 'Mobile Number', '078 8292 3621'),
('company_email', 'Email Address', '<EMAIL>');

-- Site Settings Table for branding and configuration
CREATE TABLE site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Site Content Table for editable content
CREATE TABLE site_content (
    id INT AUTO_INCREMENT PRIMARY KEY,
    content_key VARCHAR(255) UNIQUE NOT NULL,
    content_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Page SEO Table for meta tags
CREATE TABLE page_seo (
    id INT AUTO_INCREMENT PRIMARY KEY,
    page_key VARCHAR(255) UNIQUE NOT NULL,
    meta_title VARCHAR(255),
    meta_description TEXT,
    meta_keywords TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Update contact_inquiries table to match our contact form
ALTER TABLE contact_inquiries
ADD COLUMN service_interest VARCHAR(255) AFTER message,
ADD COLUMN project_reference VARCHAR(255) AFTER service_interest,
ADD COLUMN ip_address VARCHAR(45) AFTER project_reference,
ADD COLUMN user_agent TEXT AFTER ip_address;

-- Push notification subscriptions table
CREATE TABLE push_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    endpoint TEXT NOT NULL,
    p256dh_key TEXT NOT NULL,
    auth_key TEXT NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_active (user_id, is_active)
);

-- Notification history table
CREATE TABLE notification_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSON,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    sent_by INT,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sent_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_sent (user_id, sent_at),
    INDEX idx_read_status (user_id, read_at)
);

