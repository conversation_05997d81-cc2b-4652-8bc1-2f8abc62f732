<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
requireLogin();
$user = getCurrentUser();

if ($user['role'] !== 'admin') {
    header('Location: index.php');
    exit;
}

// Handle actions
$action = $_GET['action'] ?? 'list';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    if ($action === 'add') {
        $username = sanitize($_POST['username']);
        $email = sanitize($_POST['email']);
        $fullName = sanitize($_POST['full_name']);
        $password = $_POST['password'];
        $role = sanitize($_POST['role']);

        // Validate input
        if (empty($username) || empty($email) || empty($fullName) || empty($password)) {
            $message = 'Please fill in all required fields';
            $messageType = 'error';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address';
            $messageType = 'error';
        } elseif (strlen($password) < 6) {
            $message = 'Password must be at least 6 characters long';
            $messageType = 'error';
        } else {
            // Check if username or email already exists
            $existingUser = $db->fetchOne(
                "SELECT id FROM users WHERE username = ? OR email = ?",
                [$username, $email]
            );

            if ($existingUser) {
                $message = 'Username or email already exists';
                $messageType = 'error';
            } else {
                try {
                    $userData = [
                        'username' => $username,
                        'email' => $email,
                        'password_hash' => password_hash($password, PASSWORD_DEFAULT),
                        'full_name' => $fullName,
                        'role' => $role,
                        'is_active' => 1,
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    $db->insert('users', $userData);
                    $message = 'User created successfully!';
                    $messageType = 'success';
                    $action = 'list';

                } catch (Exception $e) {
                    $message = 'Error creating user: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    } elseif ($action === 'edit') {
        $userId = $_POST['id'];
        $username = sanitize($_POST['username']);
        $email = sanitize($_POST['email']);
        $fullName = sanitize($_POST['full_name']);
        $role = sanitize($_POST['role']);
        $isActive = isset($_POST['is_active']) ? 1 : 0;

        // Validate input
        if (empty($username) || empty($email) || empty($fullName)) {
            $message = 'Please fill in all required fields';
            $messageType = 'error';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address';
            $messageType = 'error';
        } else {
            // Check if username or email already exists (excluding current user)
            $existingUser = $db->fetchOne(
                "SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?",
                [$username, $email, $userId]
            );

            if ($existingUser) {
                $message = 'Username or email already exists';
                $messageType = 'error';
            } else {
                try {
                    $updateData = [
                        'username' => $username,
                        'email' => $email,
                        'full_name' => $fullName,
                        'role' => $role,
                        'is_active' => $isActive,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    // Update password if provided
                    if (!empty($_POST['password'])) {
                        if (strlen($_POST['password']) < 6) {
                            $message = 'Password must be at least 6 characters long';
                            $messageType = 'error';
                        } else {
                            $updateData['password_hash'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
                        }
                    }

                    if ($messageType !== 'error') {
                        $db->update('users', $updateData, 'id = ?', [$userId]);
                        $message = 'User updated successfully!';
                        $messageType = 'success';
                        $action = 'list';
                    }

                } catch (Exception $e) {
                    $message = 'Error updating user: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    } elseif ($action === 'delete') {
        $userId = $_POST['id'];

        // Prevent deleting own account
        if ($userId == $user['id']) {
            $message = 'You cannot delete your own account';
            $messageType = 'error';
        } else {
            try {
                // Soft delete
                $db->update('users', [
                    'is_active' => 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$userId]);

                $message = 'User deleted successfully!';
                $messageType = 'success';
                $action = 'list';

            } catch (Exception $e) {
                $message = 'Error deleting user: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get user for editing
$editUser = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $editUser = $db->fetchOne("SELECT * FROM users WHERE id = ?", [$_GET['id']]);
    if (!$editUser) {
        $action = 'list';
        $message = 'User not found';
        $messageType = 'error';
    }
}

// Get users list
if ($action === 'list') {
    $page = (int)($_GET['page'] ?? 1);
    $limit = ADMIN_ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';
    $roleFilter = $_GET['role'] ?? '';

    $where = ['1=1'];
    $params = [];

    if ($search) {
        $where[] = '(username LIKE ? OR email LIKE ? OR full_name LIKE ?)';
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    if ($roleFilter && in_array($roleFilter, ['admin', 'editor'])) {
        $where[] = 'role = ?';
        $params[] = $roleFilter;
    }

    $whereClause = implode(' AND ', $where);

    $totalQuery = "SELECT COUNT(*) as total FROM users WHERE $whereClause";
    $total = $db->fetchOne($totalQuery, $params)['total'];

    $usersQuery = "SELECT * FROM users WHERE $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
    $users = $db->fetchAll($usersQuery, $params);

    $totalPages = ceil($total / $limit);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($action === 'list'): ?>
                <!-- Enhanced Users Header -->
                <div class="users-header">
                    <div class="header-content">
                        <div class="users-title-section">
                            <h1 class="page-title">
                                <i class="fas fa-users users-icon"></i>
                                User Management
                            </h1>
                            <p class="page-description">Manage system users, roles, and access permissions</p>
                            <div class="users-stats">
                                <div class="stat-item">
                                    <span class="stat-number"><?= $total ?></span>
                                    <span class="stat-label">Total Users</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?= count(array_filter($users, fn($u) => $u['is_active'])) ?></span>
                                    <span class="stat-label">Active</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number"><?= count(array_filter($users, fn($u) => $u['role'] === 'admin')) ?></span>
                                    <span class="stat-label">Admins</span>
                                </div>
                            </div>
                        </div>
                        <div class="users-actions">
                            <a href="?action=add" class="btn btn-success btn-large">
                                <i class="fas fa-plus"></i>
                                <span>Add User</span>
                            </a>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="dashboard-card mb-20">
                    <div class="card-content">
                        <form method="GET" class="d-flex gap-20 align-center">
                            <input type="hidden" name="action" value="list">
                            <div class="form-group mb-0">
                                <input type="text" name="search" placeholder="Search users..."
                                       value="<?= htmlspecialchars($search) ?>" class="form-control">
                            </div>
                            <div class="form-group mb-0">
                                <select name="role" class="form-control">
                                    <option value="">All Roles</option>
                                    <option value="admin" <?= $roleFilter === 'admin' ? 'selected' : '' ?>>Admin</option>
                                    <option value="editor" <?= $roleFilter === 'editor' ? 'selected' : '' ?>>Editor</option>
                                </select>
                            </div>
                            <button type="submit" class="btn">Filter</button>
                            <?php if ($search || $roleFilter): ?>
                            <a href="?action=list" class="btn btn-outline">Clear</a>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>

                <!-- Users Table -->
                <?php if (empty($users)): ?>
                <div class="dashboard-card">
                    <div class="card-content text-center">
                        <i class="fas fa-users" style="font-size: 48px; color: #ddd; margin-bottom: 20px;"></i>
                        <h3>No users found</h3>
                        <p>Start by adding your first user.</p>
                        <a href="?action=add" class="btn btn-success">Add User</a>
                    </div>
                </div>
                <?php else: ?>
                <div class="dashboard-card">
                    <div class="card-content">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>Last Login</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $u): ?>
                                <tr>
                                    <td>
                                        <div>
                                            <strong><?= htmlspecialchars($u['full_name']) ?></strong>
                                            <br>
                                            <small><?= htmlspecialchars($u['username']) ?> | <?= htmlspecialchars($u['email']) ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $u['role'] === 'admin' ? 'danger' : 'info' ?>">
                                            <?= ucfirst($u['role']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?= $u['is_active'] ? 'success' : 'warning' ?>">
                                            <?= $u['is_active'] ? 'Active' : 'Inactive' ?>
                                        </span>
                                    </td>
                                    <td><?= $u['last_login'] ? formatDate($u['last_login']) : 'Never' ?></td>
                                    <td><?= formatDate($u['created_at']) ?></td>
                                    <td>
                                        <div class="d-flex gap-10">
                                            <a href="?action=edit&id=<?= $u['id'] ?>" class="btn btn-sm">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($u['id'] != $user['id']): ?>
                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete this user?')">
                                                <?= getCSRFField() ?>
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="id" value="<?= $u['id'] ?>">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $page): ?>
                        <span class="active"><?= $i ?></span>
                        <?php else: ?>
                        <a href="?action=list&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $roleFilter ? '&role=' . $roleFilter : '' ?>"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Add/Edit Form -->
                <div class="d-flex justify-between align-center mb-20">
                    <h2><?= $action === 'add' ? 'Add User' : 'Edit User' ?></h2>
                    <a href="?action=list" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Users
                    </a>
                </div>

                <form method="POST">
                    <?= getCSRFField() ?>
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="id" value="<?= $editUser['id'] ?>">
                    <?php endif; ?>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>User Information</h3>
                        </div>
                        <div class="card-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="username">Username *</label>
                                        <input type="text" id="username" name="username" class="form-control"
                                               value="<?= $editUser ? htmlspecialchars($editUser['username']) : '' ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="email">Email Address *</label>
                                        <input type="email" id="email" name="email" class="form-control"
                                               value="<?= $editUser ? htmlspecialchars($editUser['email']) : '' ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="full_name">Full Name *</label>
                                        <input type="text" id="full_name" name="full_name" class="form-control"
                                               value="<?= $editUser ? htmlspecialchars($editUser['full_name']) : '' ?>" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="password">Password <?= $action === 'add' ? '*' : '(leave blank to keep current)' ?></label>
                                        <input type="password" id="password" name="password" class="form-control"
                                               <?= $action === 'add' ? 'required' : '' ?> minlength="6">
                                        <small>Minimum 6 characters</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="role">Role *</label>
                                        <select id="role" name="role" class="form-control" required>
                                            <option value="">Select Role</option>
                                            <option value="admin" <?= ($editUser && $editUser['role'] === 'admin') ? 'selected' : '' ?>>Admin</option>
                                            <option value="editor" <?= ($editUser && $editUser['role'] === 'editor') ? 'selected' : '' ?>>Editor</option>
                                        </select>
                                    </div>

                                    <?php if ($action === 'edit'): ?>
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="is_active" <?= $editUser['is_active'] ? 'checked' : '' ?>>
                                            Active User
                                        </label>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-10">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> <?= $action === 'add' ? 'Create User' : 'Update User' ?>
                        </button>
                        <a href="?action=list" class="btn btn-outline">Cancel</a>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
</body>
</html>
