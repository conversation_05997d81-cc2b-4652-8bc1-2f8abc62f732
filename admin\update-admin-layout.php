<?php
/**
 * <PERSON>ript to update all admin pages to use the new sidebar and header includes
 * Run this once to update all admin pages
 */

require_once '../config/config.php';

// List of admin pages to update
$adminPages = [
    'services.php',
    'media.php',
    'content.php',
    'inquiries.php',
    'testimonials.php',
    'users.php',
    'branding.php',
    'email-test.php',
    'settings.php',
    'profile.php',
    'analytics.php',
    'seo.php'
];

function updateAdminPage($filename) {
    if (!file_exists($filename)) {
        return "⚠️ File not found";
    }

    $content = file_get_contents($filename);

    // Check if already updated
    if (strpos($content, "include 'includes/sidebar.php'") !== false) {
        return "✅ Already updated";
    }

    // Pattern to match the old structure
    $pattern = '/(<div class="admin-wrapper">)\s*<!-- Sidebar -->\s*<nav class="admin-sidebar">.*?<\/nav>\s*<!-- Main Content -->\s*<div class="admin-main">\s*<!-- Header -->\s*<header class="admin-header">.*?<\/header>\s*<!-- Content -->\s*<main class="admin-content">/s';

    $replacement = '<div class="admin-container">
        <?php include \'includes/sidebar.php\'; ?>

        <main class="admin-main">
            <?php include \'includes/header.php\'; ?>

            <div class="admin-content">';

    if (preg_match($pattern, $content)) {
        $content = preg_replace($pattern, $replacement, $content);

        // Fix closing tags
        $content = str_replace('</main>
        </div>
    </div>', '</div>
        </main>
    </div>', $content);

        file_put_contents($filename, $content);
        return "✅ Updated successfully";
    }

    return "⚠️ Pattern not matched - needs manual update";
}

echo "<h2>Updating Admin Pages Layout</h2>\n";

foreach ($adminPages as $page) {
    $result = updateAdminPage($page);
    echo "<p>$page: $result</p>\n";
}

echo "<h3>Update Complete!</h3>\n";
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Layout Update</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h2 { color: #2c3e50; }
        p { margin: 10px 0; }
        .success { color: #27ae60; }
        .warning { color: #f39c12; }
        .error { color: #e74c3c; }
    </style>
</head>
<body>
    <h1>Admin Layout Update Complete</h1>
    <p>The admin pages have been updated with the new layout structure.</p>
    <p><a href="index.php">← Back to Admin Dashboard</a></p>
</body>
</html>
