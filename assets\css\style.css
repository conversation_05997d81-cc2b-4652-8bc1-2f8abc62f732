/* Flori Construction Ltd - Modern Frame Homes Stylesheet */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background-color: #fafafa;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

/* Enhanced Link Animations */
a {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

a.scrolling {
    position: relative;
    overflow: hidden;
}

a.scrolling::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.3), transparent);
    animation: shimmer 0.8s ease-in-out;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }

    100% {
        left: 100%;
    }
}

/* Modern Header Styles */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px 0;
    transition: all 0.3s ease;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.header-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-logo .logo {
    height: 32px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

/* Custom logo specific styles */
.header-logo .custom-logo {
    height: 40px;
    max-height: 40px;
    width: auto;
    max-width: 200px;
    object-fit: contain;
}

/* Default logo fallback */
.default-logo-fallback {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-text {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: -0.5px;
}

.header-info {
    flex: 1;
    display: flex;
    justify-content: center;
}

.contact-info {
    text-align: center;
}

.phone {
    display: block;
    font-size: 11px;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    margin-bottom: 2px;
    text-decoration: none;
    transition: color 0.3s ease;
}

.phone:hover {
    color: #d4a574;
}

.mobile {
    display: block;
    font-size: 10px;
    font-weight: 500;
    color: #666;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    text-decoration: none;
    transition: color 0.3s ease;
}

.mobile:hover {
    color: #d4a574;
}

.header-nav {
    display: flex;
    align-items: center;
    gap: 40px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 32px;
    margin: 0;
    padding: 0;
}

.nav-link {
    font-size: 13px;
    font-weight: 500;
    color: #1a1a1a;
    text-decoration: none;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #d4a574;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #d4a574;
    border-radius: 1px;
}

.nav-link .count {
    font-size: 11px;
    color: #999;
    font-weight: 400;
}

.book-now-btn {
    background: #1a1a1a;
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.book-now-btn:hover {
    background: #d4a574;
    transform: translateY(-1px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
}

.mobile-menu-toggle span {
    width: 24px;
    height: 2px;
    background: #1a1a1a;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 1px;
}

.section-title {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
}

.section-subtitle {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
    border-color: transparent;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.btn-outline {
    background: transparent;
    color: #e74c3c;
    border-color: #e74c3c;
}

.btn-outline:hover {
    background: #e74c3c;
    color: white;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.top-bar {
    background: #2c3e50;
    color: white;
    padding: 8px 0;
    font-size: 14px;
}

.top-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info span {
    margin-right: 20px;
}

.contact-info i {
    margin-right: 5px;
    color: #e74c3c;
}

.social-links a {
    color: white;
    margin-left: 10px;
    font-size: 16px;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #e74c3c;
}

.navbar {
    padding: 15px 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    margin-left: 30px;
    position: relative;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 1px;
    transition: color 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #e74c3c;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 5px;
    padding: 10px 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-menu a {
    display: block;
    padding: 8px 20px;
    text-transform: none;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Modern Hero Section */
.modern-hero {
    height: 100vh;
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(26, 26, 26, 0.7) 0%,
            rgba(26, 26, 26, 0.4) 50%,
            rgba(26, 26, 26, 0.6) 100%);
}

.hero-content {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 40px;
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto 1fr auto;
    height: 100vh;
    color: white;
}

.hero-badge {
    grid-column: 1;
    grid-row: 1;
    align-self: start;
    margin-top: 120px;
}

.hero-badge span {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-main {
    grid-column: 1;
    grid-row: 2;
    align-self: center;
    margin-top: -80px;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 600;
    line-height: 0.9;
    margin-bottom: 0;
    letter-spacing: -2px;
    text-transform: uppercase;
}

.house-specs {
    grid-column: 2;
    grid-row: 1 / 3;
    align-self: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 32px;
    margin-left: 40px;
    max-width: 320px;
}

.specs-content h3 {
    font-family: 'Inter', sans-serif;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #d4a574;
}

.specs-content p {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 16px;
    color: rgba(255, 255, 255, 0.9);
}

.specs-content strong {
    color: white;
    font-weight: 600;
}

.hero-bottom {
    grid-column: 1 / 3;
    grid-row: 3;
    display: flex;
    justify-content: space-between;
    align-items: end;
    margin-bottom: 60px;
}

.hero-tagline h2 {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 400;
    line-height: 1.2;
    margin-bottom: 0;
}

.hero-tagline em {
    font-style: italic;
    color: #d4a574;
}

.hero-actions {
    display: flex;
    align-items: end;
    gap: 60px;
}

.scroll-indicator {
    text-align: center;
}

.scroll-indicator span {
    display: block;
    font-size: 10px;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.scroll-arrow {
    animation: bounce 2s infinite;
}

.showreel-button {
    display: flex;
    align-items: center;
    gap: 12px;
}

.play-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 12px 20px;
    color: white;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.play-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.duration {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.house-model {
    position: absolute;
    bottom: 40px;
    right: 40px;
    z-index: 3;
    width: 200px;
    height: 120px;
}

.model-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.3));
}

/* Enhanced Hero Navigation Dots */
.hero-nav-dots {
    position: fixed;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.nav-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10px);
}

.nav-dot::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: #d4a574;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0;
}

.nav-dot.active {
    background: #d4a574;
    border-color: #d4a574;
    transform: scale(1.2);
    box-shadow: 0 0 20px rgba(212, 165, 116, 0.5);
}

.nav-dot.active::before {
    width: 6px;
    height: 6px;
    opacity: 1;
    background: white;
}

.nav-dot:hover {
    background: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
    border-color: rgba(255, 255, 255, 0.8);
}

.nav-dot:hover::before {
    width: 4px;
    height: 4px;
    opacity: 0.7;
    background: #d4a574;
}

/* Navigation dot tooltip */
.nav-dot-tooltip {
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    letter-spacing: 0.5px;
    z-index: 1001;
}

/* Pulse animation for active dots */
@keyframes pulse {
    0% {
        transform: scale(1.2);
    }

    50% {
        transform: scale(1.4);
        box-shadow: 0 0 25px rgba(212, 165, 116, 0.7);
    }

    100% {
        transform: scale(1.2);
    }
}

.nav-dot::after {
    content: attr(title);
    position: absolute;
    right: 120%;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1001;
}

.nav-dot:hover::after {
    opacity: 1;
}

/* Enhanced Hero Background */
.hero-image-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
            rgba(26, 26, 26, 0.8) 0%,
            rgba(26, 26, 26, 0.3) 30%,
            rgba(26, 26, 26, 0.1) 60%,
            rgba(26, 26, 26, 0.6) 100%);
    z-index: 2;
}

/* Enhanced Badge Animations */
.hero-badge {
    position: relative;
    overflow: hidden;
}

.badge-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #d4a574, #f4d03f, #d4a574);
    border-radius: 22px;
    opacity: 0;
    animation: badgeGlow 3s ease-in-out infinite;
}

.badge-line {
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 2px;
    background: #d4a574;
    transition: width 0.8s ease;
}

.hero-badge.animate .badge-line {
    width: 80%;
}

/* Enhanced Title Animations */
.title-line {
    display: block;
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.title-line.highlight {
    background: linear-gradient(135deg, #d4a574 0%, #f4d03f 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    margin-top: 24px;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 1s ease 1.2s forwards;
}

.hero-subtitle p {
    font-size: 18px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    max-width: 500px;
}

/* Enhanced House Specifications */
.specs-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-icon {
    width: 40px;
    height: 40px;
    background: rgba(212, 165, 116, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.specs-icon i {
    color: #d4a574;
    font-size: 18px;
}

.specs-header h3 {
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    color: #d4a574;
    margin: 0;
}

.spec-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.spec-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
}

.spec-value {
    font-size: 14px;
    color: white;
    font-weight: 600;
}

.specs-footer {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-btn {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.specs-btn:hover {
    background: rgba(212, 165, 116, 0.2);
    border-color: #d4a574;
}

.house-specs.animate {
    animation: slideInRight 0.8s ease forwards;
}

/* Enhanced Tagline */
.tagline-accent {
    width: 60px;
    height: 3px;
    background: linear-gradient(45deg, #d4a574, #f4d03f);
    margin-top: 16px;
    border-radius: 2px;
}

/* Enhanced Showreel Section */
.showreel-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.play-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    transition: all 0.3s ease;
}

.play-btn {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 16px 24px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover .play-icon {
    background: #d4a574;
    transform: scale(1.1);
}

.showreel-info {
    display: flex;
    gap: 16px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
}

.quality {
    background: rgba(212, 165, 116, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    color: #d4a574;
    font-weight: 600;
}

/* Interactive Elements */
.hero-interactive {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

.hero-interactive>* {
    pointer-events: auto;
}

.model-hotspots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hotspot {
    position: absolute;
    width: 16px;
    height: 16px;
    background: #d4a574;
    border: 3px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    cursor: pointer;
    animation: pulse 2s infinite;
    transform: translate(-50%, -50%);
}

.hotspot-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    margin-bottom: 8px;
}

.floating-stats {
    position: absolute;
    top: 20%;
    left: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    color: white;
    min-width: 120px;
}

.stat-number {
    font-family: 'Playfair Display', serif;
    font-size: 32px;
    font-weight: 700;
    color: #d4a574;
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Particle Animation */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(212, 165, 116, 0.6);
    border-radius: 50%;
    animation: float linear infinite;
}

@keyframes badgeGlow {

    0%,
    100% {
        opacity: 0;
    }

    50% {
        opacity: 0.3;
    }
}

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
    }

    50% {
        transform: translate(-50%, -50%) scale(1.2);
    }

    100% {
        transform: translate(-50%, -50%) scale(1);
    }
}

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }

    40% {
        transform: translateY(-10px);
    }

    60% {
        transform: translateY(-5px);
    }
}

/* Modern Sections */
section {
    padding: 120px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-badge {
    display: inline-block;
    background: #f5f5f5;
    color: #666;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.section-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 600;
    color: #1a1a1a;
    line-height: 1.1;
    margin-bottom: 20px;
    letter-spacing: -1px;
}

.section-subtitle {
    font-size: 18px;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Enhanced Modern About Section */
.modern-about {
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.section-bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(212, 165, 116, 0.05), rgba(244, 208, 63, 0.05));
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 10%;
    right: -150px;
}

.shape-2 {
    width: 200px;
    height: 200px;
    bottom: 20%;
    left: -100px;
}

.about-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.about-content .section-header {
    text-align: left;
    margin-bottom: 40px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(212, 165, 116, 0.1);
    color: #d4a574;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 20px;
    border: 1px solid rgba(212, 165, 116, 0.2);
}

.section-badge i {
    font-size: 12px;
}

.title-highlight {
    background: linear-gradient(135deg, #d4a574 0%, #f4d03f 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-decoration {
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #d4a574, #f4d03f);
    border-radius: 2px;
    margin-top: 16px;
}

.about-description {
    margin-bottom: 40px;
}

.lead-text {
    font-size: 20px;
    line-height: 1.7;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
}

.supporting-text {
    font-size: 16px;
    line-height: 1.7;
    color: #666;
}

.feature-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    margin-bottom: 40px;
}

.feature-item {
    background: white;
    padding: 24px;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(45deg, #d4a574, #f4d03f);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-item:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, rgba(212, 165, 116, 0.1), rgba(244, 208, 63, 0.1));
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-bottom: 16px;
    position: relative;
}

.icon-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #d4a574, #f4d03f);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-item:hover .icon-bg {
    opacity: 0.1;
}

.feature-icon i {
    color: #d4a574;
    font-size: 24px;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.feature-item:hover .feature-icon i {
    color: #d4a574;
    transform: scale(1.1);
}

.feature-content h4 {
    font-family: 'Playfair Display', serif;
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 8px;
    line-height: 1.3;
}

.feature-content p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
    margin-bottom: 16px;
}

.feature-metric {
    display: flex;
    align-items: baseline;
    gap: 8px;
    margin-top: 12px;
}

.metric-value {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 700;
    color: #d4a574;
}

.metric-label {
    font-size: 11px;
    color: #999;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.about-actions {
    display: flex;
    align-items: center;
    gap: 32px;
}

.btn-modern {
    background: #1a1a1a;
    color: white;
    padding: 16px 32px;
    border-radius: 12px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
}

.btn-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #d4a574, #f4d03f);
    transition: left 0.3s ease;
    z-index: 1;
}

.btn-modern:hover::before {
    left: 0;
}

.btn-modern span,
.btn-modern i {
    position: relative;
    z-index: 2;
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(212, 165, 116, 0.3);
}

.btn-link {
    color: #1a1a1a;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.btn-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: #d4a574;
    transition: width 0.3s ease;
}

.btn-link:hover::after {
    width: 100%;
}

.btn-link:hover {
    color: #d4a574;
}

.btn-link i {
    transition: transform 0.3s ease;
}

.btn-link:hover i {
    transform: translateX(4px);
}

/* Enhanced Visual Section */
.about-visual {
    position: relative;
}

.image-composition {
    position: relative;
    height: 600px;
}

.main-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
}

.main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.main-image-container:hover .main-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
            rgba(26, 26, 26, 0.1) 0%,
            rgba(26, 26, 26, 0.3) 100%);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    pointer-events: auto;
    transition: all 0.3s ease;
}

.floating-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 30px 60px rgba(0, 0, 0, 0.2);
}

.experience-card {
    bottom: 40px;
    right: 40px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    min-width: 200px;
}

.card-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #d4a574, #f4d03f);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.card-content h4 {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 4px;
    line-height: 1;
}

.card-content p {
    font-size: 13px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.cert-badge {
    top: 40px;
    left: 40px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.badge-icon {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #d4a574, #f4d03f);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.badge-text span {
    display: block;
    font-size: 14px;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1;
}

.badge-text small {
    font-size: 11px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.process-indicator {
    position: absolute;
    top: 50%;
    left: -20px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.process-step {
    background: rgba(255, 255, 255, 0.9);
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.process-step.active {
    background: #d4a574;
    color: white;
}

.process-step::after {
    content: '';
    position: absolute;
    top: 50%;
    right: -8px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left-color: inherit;
}

.decorative-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

/* Photography Style About Section */
.photo-about {
    background: #1a1a1a;
    color: #ffffff;
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.about-top-section {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 60px;
    margin-bottom: 120px;
    align-items: start;
}

.section-number {
    font-size: 14px;
    color: #666;
    font-weight: 400;
    letter-spacing: 1px;
    margin-bottom: 8px;
}

.section-label {
    font-size: 14px;
    color: #999;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.about-hero-content {
    max-width: 800px;
}

.about-main-title {
    font-family: 'Playfair Display', serif;
    font-size: 48px;
    line-height: 1.2;
    font-weight: 400;
    margin-bottom: 40px;
    color: #ffffff;
}

.title-highlight {
    color: #ffffff;
    font-weight: 400;
}

.title-bold {
    font-weight: 600;
    color: #ffffff;
}

.hero-description {
    font-size: 16px;
    line-height: 1.7;
    color: #999;
    margin-bottom: 50px;
    max-width: 600px;
}

.learn-more-btn {
    display: inline-block;
}

.btn-learn-more {
    display: inline-flex;
    align-items: center;
    gap: 20px;
    color: #ffffff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 2px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 16px 32px;
    border-radius: 50px;
}

.btn-arrow {
    width: 40px;
    height: 40px;
    background: #ffffff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1a1a1a;
    transition: all 0.3s ease;
}

.btn-learn-more:hover {
    border-color: #ffffff;
    transform: translateY(-2px);
}

.btn-learn-more:hover .btn-arrow {
    transform: translateX(4px);
}

.about-bottom-section {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 80px;
    align-items: start;
}

.bottom-left .section-number {
    margin-bottom: 8px;
}

.bottom-left .section-label {
    margin-bottom: 40px;
}

.expertise-title {
    font-family: 'Playfair Display', serif;
    font-size: 36px;
    line-height: 1.3;
    font-weight: 400;
    color: #ffffff;
    margin: 0;
}

.bottom-right {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.expertise-categories {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 40px;
}

.category-item {
    font-size: 16px;
    color: #666;
    padding: 12px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.category-item.active,
.category-item:hover {
    color: #ffffff;
}

.category-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -1px;
    width: 100%;
    height: 1px;
    background: #ffffff;
}

.project-showcase {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    height: 400px;
}

.main-project-image {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: #2a2a2a;
}

.project-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.main-project-image:hover .project-img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 40px 30px 30px;
    color: #ffffff;
}

.project-info h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.project-info p {
    font-size: 14px;
    color: #ccc;
    margin: 0;
}

.secondary-project-image {
    border-radius: 12px;
    overflow: hidden;
    background: #2a2a2a;
}

.secondary-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.secondary-project-image:hover .secondary-img {
    transform: scale(1.05);
}

.navigation-controls {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.nav-btn {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: #ffffff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.see-all-link {
    margin-left: auto;
}

.see-all-link a {
    color: #999;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
    position: relative;
}

.see-all-link a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: #ffffff;
    transition: width 0.3s ease;
}

.see-all-link a:hover {
    color: #ffffff;
}

.see-all-link a:hover::after {
    width: 100%;
}

/* Responsive Design for Photography About Section */
@media (max-width: 1024px) {
    .about-top-section {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 80px;
    }

    .about-bottom-section {
        grid-template-columns: 1fr;
        gap: 60px;
    }

    .about-main-title {
        font-size: 40px;
    }

    .expertise-title {
        font-size: 32px;
    }

    .project-showcase {
        height: 300px;
    }
}

@media (max-width: 768px) {
    .photo-about {
        padding: 80px 0;
    }

    .about-main-title {
        font-size: 32px;
    }

    .expertise-title {
        font-size: 28px;
    }

    .project-showcase {
        grid-template-columns: 1fr;
        height: auto;
        gap: 16px;
    }

    .main-project-image,
    .secondary-project-image {
        height: 250px;
    }

    .btn-learn-more {
        padding: 14px 28px;
        font-size: 12px;
    }

    .btn-arrow {
        width: 36px;
        height: 36px;
    }

    .navigation-controls {
        justify-content: center;
        margin-top: 20px;
    }

    .see-all-link {
        margin-left: 0;
        margin-top: 20px;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .about-main-title {
        font-size: 28px;
    }

    .expertise-title {
        font-size: 24px;
    }

    .expertise-categories {
        gap: 12px;
    }

    .category-item {
        font-size: 14px;
        padding: 10px 0;
    }

    .main-project-image,
    .secondary-project-image {
        height: 200px;
    }
}

.deco-circle {
    position: absolute;
    top: 20%;
    right: 20%;
    width: 120px;
    height: 120px;
    border: 2px solid rgba(212, 165, 116, 0.3);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.deco-line {
    position: absolute;
    bottom: 30%;
    left: 10%;
    width: 80px;
    height: 2px;
    background: linear-gradient(45deg, #d4a574, #f4d03f);
    transform: rotate(-45deg);
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* About Preview */
.about-preview {
    background: #f8f9fa;
}

.about-content {
    padding-right: 30px;
}

.features {
    margin: 30px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.feature-item i {
    color: #e74c3c;
    margin-right: 10px;
    font-size: 18px;
}

.about-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Modern Services Section */
.modern-services {
    background: #f8f8f8;
    padding: 120px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.service-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-image {
    height: 240px;
    overflow: hidden;
    position: relative;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 26, 26, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-card:hover .service-overlay {
    opacity: 1;
}

.service-card:hover .service-image img {
    transform: scale(1.05);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-icon i {
    color: white;
    font-size: 24px;
}

.service-content {
    padding: 32px;
}

.service-content h3 {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 12px;
    line-height: 1.3;
}

.service-content p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
    font-size: 15px;
}

.service-link {
    color: #1a1a1a;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.service-link:hover {
    color: #d4a574;
}

.service-link i {
    transition: transform 0.3s ease;
}

.service-link:hover i {
    transform: translateX(4px);
}

/* Modern Projects Section */
.modern-projects {
    background: white;
    padding: 120px 0;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    margin-bottom: 60px;
}

.project-card {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    height: 400px;
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.project-image {
    position: relative;
    width: 100%;
    height: 100%;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom,
            rgba(26, 26, 26, 0.2) 0%,
            rgba(26, 26, 26, 0.4) 50%,
            rgba(26, 26, 26, 0.8) 100%);
    display: flex;
    align-items: flex-end;
    padding: 32px;
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    width: 100%;
}

.project-type {
    background: rgba(212, 165, 116, 0.9);
    backdrop-filter: blur(10px);
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 16px;
    display: inline-block;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.project-info h3 {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.2;
}

.project-description {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 20px;
    line-height: 1.5;
}

.project-link {
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.project-link:hover {
    color: #d4a574;
}

.project-link i {
    transition: transform 0.3s ease;
}

.project-link:hover i {
    transform: translateX(4px);
}

.section-footer {
    text-align: center;
}

.text-center {
    text-align: center;
}

/* Modern Footer */
.modern-footer {
    background: #1a1a1a;
    color: white;
    padding: 80px 0 40px;
}

.footer-content {
    margin-bottom: 60px;
}

.footer-main {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
    align-items: start;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
}

.footer-logo img {
    height: 40px;
    width: auto;
}

.footer-logo .logo-text {
    font-family: 'Playfair Display', serif;
    font-size: 28px;
    font-weight: 600;
    color: white;
    letter-spacing: -0.5px;
}

.footer-description {
    font-size: 16px;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 32px;
}

.social-links {
    display: flex;
    gap: 16px;
}

.social-links a {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
    background: #d4a574;
    border-color: #d4a574;
    transform: translateY(-2px);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 60px;
}

.footer-column h4 {
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
}

.footer-column ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-column ul li {
    margin-bottom: 12px;
}

.footer-column ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-column ul li a:hover {
    color: #d4a574;
}

.contact-info p {
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
}

.contact-info i {
    margin-right: 12px;
    color: #d4a574;
    width: 16px;
    font-size: 14px;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 32px;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-bottom-content p {
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
    margin: 0;
}

.footer-bottom-links {
    display: flex;
    gap: 32px;
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
    color: #d4a574;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 90%;
    max-width: 800px;
    background: white;
    border-radius: 16px;
    overflow: hidden;
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 32px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #d4a574;
}

.video-container {
    position: relative;
    width: 100%;
    height: 450px;
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background-color: #1a1a1a;
    margin: 5% auto;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 900px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    overflow: hidden;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(212, 165, 116, 0.8);
    transform: scale(1.1);
}

.video-fallback {
    padding: 40px;
    text-align: center;
    color: white;
}

.video-fallback a {
    color: #d4a574;
    text-decoration: none;
}

.video-fallback a:hover {
    text-decoration: underline;
}

/* Image Fallbacks */
img {
    max-width: 100%;
    height: auto;
}

img[src=""],
img:not([src]),
img[src*="placeholder"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    text-align: center;
    position: relative;
}

img[src=""]:before,
img:not([src]):before {
    content: "Image Loading...";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 24px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 400px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    border-left: 4px solid #27ae60;
}

.notification-error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-left: 4px solid #e74c3c;
}

.notification-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-left: 4px solid #3498db;
}

.notification-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-left: 4px solid #f39c12;
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 500;
    border-radius: 12px;
    text-transform: uppercase;
}

.badge-primary {
    background: #3498db;
    color: white;
}

.badge-success {
    background: #27ae60;
    color: white;
}

.badge-warning {
    background: #f39c12;
    color: white;
}

.badge-danger {
    background: #e74c3c;
    color: white;
}

.badge-info {
    background: #17a2b8;
    color: white;
}

.badge-secondary {
    background: #6c757d;
    color: white;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 120px 0 60px;
    text-align: center;
    margin-top: 90px;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 20px;
}

.breadcrumb {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.breadcrumb a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: white;
}

.breadcrumb span {
    color: #7f8c8d;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-10 {
    margin-bottom: 10px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.d-none {
    display: none;
}

.d-block {
    display: block;
}

.d-flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.align-center {
    align-items: center;
}

.gap-10 {
    gap: 10px;
}

.gap-20 {
    gap: 20px;
}

/* Enhanced Modern Footer */
.enhanced-modern-footer {
    background: #0a0a0a;
    color: #ffffff;
    position: relative;
    overflow: hidden;
    padding: 0;
}

.footer-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
}

.footer-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
}

.footer-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
}

.enhanced-modern-footer .container {
    position: relative;
    z-index: 1;
}

/* Top Navigation Bar */
.footer-top-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.footer-nav-left .footer-brand-name,
.footer-nav-left .footer-default-logo {
    font-family: 'Playfair Display', serif;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.footer-logo-img {
    height: 32px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

.footer-nav-menu {
    display: flex;
    gap: 40px;
}

.footer-nav-link {
    color: #cccccc;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: color 0.3s ease;
    position: relative;
}

.footer-nav-link:hover {
    color: #ffffff;
}

.footer-contact-link {
    color: #ffffff;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: color 0.3s ease;
}

.footer-contact-link:hover {
    color: #d4a574;
}

/* Main Footer Content */
.footer-main-content {
    padding: 80px 0 60px;
}

/* Work With Us Section */
.footer-cta-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 120px;
    padding-bottom: 60px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}

.footer-cta-title {
    font-family: 'Playfair Display', serif;
    font-size: 48px;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.footer-cta-line {
    width: 80px;
    height: 2px;
    background: #d4a574;
    margin-top: 20px;
}

.footer-email-link {
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    font-weight: 300;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
    letter-spacing: 1px;
}

.footer-email-link:hover {
    color: #d4a574;
}

/* Footer Links Grid */
.footer-links-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr;
    gap: 80px;
    margin-bottom: 100px;
}

.footer-column-title {
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 30px;
}

.footer-links-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links-list li {
    margin-bottom: 16px;
}

.footer-links-list a {
    color: #888888;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    transition: color 0.3s ease;
    line-height: 1.6;
}

.footer-links-list a:hover {
    color: #ffffff;
}

/* Contact Information */
.footer-contact-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-label {
    font-size: 12px;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
}

.contact-value {
    font-size: 14px;
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
    line-height: 1.4;
}

.contact-value:hover {
    color: #ffffff;
}

/* Large Brand Section */
.footer-brand-section {
    margin-bottom: 60px;
}

.footer-large-brand {
    font-family: 'Playfair Display', serif;
    font-size: clamp(60px, 12vw, 120px);
    font-weight: 700;
    color: #ffffff;
    margin: 0;
    line-height: 0.9;
    letter-spacing: -2px;
    text-transform: uppercase;
}

.brand-trademark {
    font-size: 0.4em;
    vertical-align: top;
    color: #d4a574;
    font-weight: 400;
}

/* Footer Bottom */
.footer-bottom-section {
    border-top: 1px solid rgba(255, 255, 255, 0.08);
    padding: 30px 0;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-copyright {
    color: #666666;
    font-size: 12px;
    margin: 0;
    font-weight: 400;
}

.footer-bottom-links {
    display: flex;
    gap: 30px;
}

.footer-bottom-link {
    color: #666666;
    text-decoration: none;
    font-size: 12px;
    font-weight: 400;
    transition: color 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.footer-bottom-link:hover {
    color: #ffffff;
}

/* Modern Testimonials Section */
.modern-testimonials {
    padding: 120px 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
}

.modern-testimonials::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    margin-bottom: 80px;
}

.testimonial-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.testimonial-content {
    margin-bottom: 30px;
}

.quote-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #d4a574, #c19660);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3);
}

.quote-icon i {
    color: #ffffff;
    font-size: 20px;
}

.testimonial-text {
    margin-bottom: 25px;
}

.testimonial-text p {
    font-size: 16px;
    line-height: 1.7;
    color: #444444;
    font-style: italic;
    margin: 0;
    position: relative;
}

.testimonial-rating {
    display: flex;
    gap: 4px;
    margin-bottom: 20px;
}

.testimonial-rating i {
    font-size: 16px;
    color: #e0e0e0;
    transition: color 0.3s ease;
}

.testimonial-rating i.active {
    color: #ffc107;
}

.testimonial-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 25px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.client-info {
    flex: 1;
}

.client-name {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 5px 0;
    font-family: 'Playfair Display', serif;
}

.client-company {
    font-size: 14px;
    color: #666666;
    margin: 0;
    font-weight: 500;
}

.verified-badge {
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(40, 167, 69, 0.1);
    padding: 6px 12px;
    border-radius: 20px;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.verified-badge i {
    color: #28a745;
    font-size: 14px;
}

.verified-badge span {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Testimonials Summary */
.testimonials-summary {
    display: flex;
    justify-content: center;
    margin-top: 60px;
}

.summary-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 50px;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 60px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    max-width: 800px;
    width: 100%;
}

.summary-rating {
    text-align: center;
    padding-right: 40px;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.rating-number {
    font-size: 48px;
    font-weight: 700;
    color: #1a1a1a;
    font-family: 'Playfair Display', serif;
    line-height: 1;
    margin-bottom: 10px;
}

.rating-stars {
    display: flex;
    justify-content: center;
    gap: 4px;
    margin-bottom: 10px;
}

.rating-stars i {
    color: #ffc107;
    font-size: 18px;
}

.rating-text {
    font-size: 14px;
    color: #666666;
    font-weight: 500;
}

.summary-stats {
    display: flex;
    gap: 40px;
    flex: 1;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: 700;
    color: #d4a574;
    font-family: 'Playfair Display', serif;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 12px;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 500;
    line-height: 1.3;
}

/* Responsive Design for Testimonials */
@media (max-width: 768px) {
    .modern-testimonials {
        padding: 80px 0;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        margin-bottom: 60px;
    }

    .testimonial-card {
        padding: 30px;
    }

    .summary-card {
        flex-direction: column;
        gap: 40px;
        padding: 40px 30px;
        text-align: center;
    }

    .summary-rating {
        padding-right: 0;
        border-right: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding-bottom: 30px;
    }

    .summary-stats {
        gap: 30px;
    }

    .stat-number {
        font-size: 28px;
    }
}

/* Enhanced Scroll Elements */
.scroll-progress {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    height: 3px !important;
    background: linear-gradient(90deg, #d4a574, #c19660) !important;
    z-index: 1001 !important;
    transition: width 0.1s ease !important;
}

.back-to-top-btn {
    position: fixed !important;
    bottom: 30px !important;
    right: 30px !important;
    width: 50px !important;
    height: 50px !important;
    background: linear-gradient(135deg, #d4a574, #c19660) !important;
    color: white !important;
    border: none !important;
    border-radius: 50% !important;
    cursor: pointer !important;
    font-size: 18px !important;
    box-shadow: 0 4px 15px rgba(212, 165, 116, 0.3) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    z-index: 1000 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.back-to-top-btn:hover {
    transform: translateY(-3px) scale(1.05) !important;
    box-shadow: 0 6px 20px rgba(212, 165, 116, 0.4) !important;
}

.back-to-top-btn:active {
    transform: translateY(-1px) scale(0.95) !important;
}

/* Enhanced Section Reveal Animations */
.reveal-element {
    opacity: 0;
    transform: translateY(60px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
}

.reveal-element.animate-revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Animation Types */
.reveal-element[data-animation="fade-up"] {
    opacity: 0;
    transform: translateY(60px);
}

.reveal-element[data-animation="fade-up"].animate-revealed {
    opacity: 1;
    transform: translateY(0);
}

.reveal-element[data-animation="fade-down"] {
    opacity: 0;
    transform: translateY(-60px);
}

.reveal-element[data-animation="fade-down"].animate-revealed {
    opacity: 1;
    transform: translateY(0);
}

.reveal-element[data-animation="fade-left"] {
    opacity: 0;
    transform: translateX(-60px);
}

.reveal-element[data-animation="fade-left"].animate-revealed {
    opacity: 1;
    transform: translateX(0);
}

.reveal-element[data-animation="fade-right"] {
    opacity: 0;
    transform: translateX(60px);
}

.reveal-element[data-animation="fade-right"].animate-revealed {
    opacity: 1;
    transform: translateX(0);
}

.reveal-element[data-animation="scale-up"] {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.reveal-element[data-animation="scale-up"].animate-revealed {
    opacity: 1;
    transform: scale(1);
}

.reveal-element[data-animation="rotate-in"] {
    opacity: 0;
    transform: rotate(-10deg) scale(0.9);
}

.reveal-element[data-animation="rotate-in"].animate-revealed {
    opacity: 1;
    transform: rotate(0deg) scale(1);
}

/* Stagger Children Animation */
.stagger-child {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.stagger-child.animate-revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Legacy support */
.section-reveal {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth scroll offset for fixed header */
section[id] {
    scroll-margin-top: 100px;
}

/* Enhanced navigation link states */
.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #d4a574, #c19660);
    transition: width 0.3s ease;
}

.nav-link:hover::before {
    width: 100%;
}

.nav-link.active::before {
    width: 100%;
}

/* Section Navigation Links */
.section-nav-links {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.section-nav-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(212, 165, 116, 0.1);
    color: #d4a574;
    text-decoration: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(212, 165, 116, 0.2);
}

.section-nav-link:hover {
    background: rgba(212, 165, 116, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(212, 165, 116, 0.2);
}

.section-nav-link i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.section-nav-link:hover i {
    transform: translateY(-1px);
}

/* Mobile responsive adjustments for scroll elements */
@media (max-width: 768px) {
    .back-to-top-btn {
        bottom: 20px !important;
        right: 20px !important;
        width: 45px !important;
        height: 45px !important;
        font-size: 16px !important;
    }

    .scroll-progress {
        height: 2px !important;
    }

    .hero-nav-dots {
        display: none;
    }

    section[id] {
        scroll-margin-top: 80px;
    }
}

/* Advanced Animation Keyframes */
@keyframes floating {

    0%,
    100% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(-20px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(60px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-60px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-60px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(60px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-10deg) scale(0.9);
    }

    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes slideInFromBottom {
    from {
        opacity: 0;
        transform: translateY(100px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }

    50% {
        opacity: 1;
        transform: scale(1.05);
    }

    70% {
        transform: scale(0.9);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes typewriter {
    from {
        width: 0;
    }

    to {
        width: 100%;
    }
}

@keyframes blink {

    0%,
    50% {
        opacity: 1;
    }

    51%,
    100% {
        opacity: 0;
    }
}

@keyframes glow {

    0%,
    100% {
        box-shadow: 0 0 20px rgba(212, 165, 116, 0.3);
    }

    50% {
        box-shadow: 0 0 30px rgba(212, 165, 116, 0.6);
    }
}

@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }

    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Advanced Animation Classes */
.floating-element {
    animation: floating 3s ease-in-out infinite;
}

.magnetic-element {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.parallax-element {
    will-change: transform;
}

.typewriter-text {
    overflow: hidden;
    border-right: 2px solid #d4a574;
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
}

.typewriter-complete {
    border-right: none;
    animation: none;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

.bounce-in {
    animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Hover Animations */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

/* Loading Animations */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(212, 165, 116, 0.3);
    border-top: 4px solid #d4a574;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {

    0%,
    20% {
        content: '';
    }

    40% {
        content: '.';
    }

    60% {
        content: '..';
    }

    80%,
    100% {
        content: '...';
    }
}

/* Performance Optimizations */
.reveal-element,
.floating-element,
.magnetic-element,
.parallax-element {
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {

    .reveal-element,
    .floating-element,
    .magnetic-element,
    .parallax-element {
        animation: none !important;
        transition: none !important;
    }

    .reveal-element {
        opacity: 1 !important;
        transform: none !important;
    }
}