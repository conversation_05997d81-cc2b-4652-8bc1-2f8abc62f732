<?php
/**
 * Push Notifications API
 * Handles push notification subscriptions and sending notifications
 */

require_once '../config/config.php';

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    // All notification operations require authentication
    $user = authenticateRequest();
    if (!$user) {
        jsonResponse(['error' => 'Authentication required'], 401);
    }

    switch ($method) {
        case 'GET':
            if (isset($_GET['action']) && $_GET['action'] === 'history') {
                handleGetNotificationHistory($user);
            } else {
                handleGetSubscription($user);
            }
            break;

        case 'POST':
            $action = $input['action'] ?? '';
            if ($action === 'subscribe') {
                handleSubscribe($input, $user);
            } elseif ($action === 'send') {
                handleSendNotification($input, $user);
            } else {
                jsonResponse(['error' => 'Invalid action'], 400);
            }
            break;

        case 'PUT':
            $action = $input['action'] ?? '';
            if ($action === 'mark_read') {
                handleMarkAsRead($input, $user);
            } else {
                jsonResponse(['error' => 'Invalid action'], 400);
            }
            break;

        case 'DELETE':
            handleUnsubscribe($user);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    jsonResponse(['error' => $e->getMessage()], 500);
}

function handleGetSubscription($user) {
    global $db;

    try {
        $subscription = $db->fetchOne(
            "SELECT * FROM push_subscriptions WHERE user_id = ? AND is_active = 1",
            [$user['id']]
        );

        jsonResponse([
            'success' => true,
            'subscribed' => $subscription !== null,
            'subscription' => $subscription
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to get subscription: ' . $e->getMessage()], 500);
    }
}

function handleSubscribe($input, $user) {
    global $db;

    if (!isset($input['subscription'])) {
        jsonResponse(['error' => 'Subscription data is required'], 400);
    }

    $subscription = $input['subscription'];

    // Validate subscription data
    if (!isset($subscription['endpoint']) || !isset($subscription['keys'])) {
        jsonResponse(['error' => 'Invalid subscription data'], 400);
    }

    try {
        // Check if subscription already exists
        $existing = $db->fetchOne(
            "SELECT id FROM push_subscriptions WHERE user_id = ? AND endpoint = ?",
            [$user['id'], $subscription['endpoint']]
        );

        if ($existing) {
            // Update existing subscription
            $db->update('push_subscriptions', [
                'p256dh_key' => $subscription['keys']['p256dh'],
                'auth_key' => $subscription['keys']['auth'],
                'is_active' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'id = ?', [$existing['id']]);

            $message = 'Subscription updated successfully';
        } else {
            // Create new subscription
            $db->insert('push_subscriptions', [
                'user_id' => $user['id'],
                'endpoint' => $subscription['endpoint'],
                'p256dh_key' => $subscription['keys']['p256dh'],
                'auth_key' => $subscription['keys']['auth'],
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            $message = 'Subscription created successfully';
        }

        jsonResponse([
            'success' => true,
            'message' => $message
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to save subscription: ' . $e->getMessage()], 500);
    }
}

function handleUnsubscribe($user) {
    global $db;

    try {
        $db->update('push_subscriptions', [
            'is_active' => 0,
            'updated_at' => date('Y-m-d H:i:s')
        ], 'user_id = ?', [$user['id']]);

        jsonResponse([
            'success' => true,
            'message' => 'Unsubscribed successfully'
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to unsubscribe: ' . $e->getMessage()], 500);
    }
}

function handleSendNotification($input, $user) {
    global $db;

    // Only admins can send notifications
    if ($user['role'] !== 'admin') {
        jsonResponse(['error' => 'Admin access required'], 403);
    }

    $title = $input['title'] ?? 'Flori Construction';
    $body = $input['body'] ?? '';
    $targetUserId = $input['user_id'] ?? null;
    $data = $input['data'] ?? [];

    if (empty($body)) {
        jsonResponse(['error' => 'Notification body is required'], 400);
    }

    try {
        // Get target subscriptions
        $whereClause = 'is_active = 1';
        $params = [];

        if ($targetUserId) {
            $whereClause .= ' AND user_id = ?';
            $params[] = $targetUserId;
        }

        $subscriptions = $db->fetchAll(
            "SELECT * FROM push_subscriptions WHERE $whereClause",
            $params
        );

        if (empty($subscriptions)) {
            jsonResponse(['error' => 'No active subscriptions found'], 404);
        }

        $sentCount = 0;
        $failedCount = 0;

        foreach ($subscriptions as $subscription) {
            try {
                $success = sendPushNotification($subscription, $title, $body, $data);
                if ($success) {
                    $sentCount++;

                    // Log notification
                    $db->insert('notification_history', [
                        'user_id' => $subscription['user_id'],
                        'title' => $title,
                        'body' => $body,
                        'data' => json_encode($data),
                        'sent_at' => date('Y-m-d H:i:s'),
                        'sent_by' => $user['id']
                    ]);
                } else {
                    $failedCount++;
                }
            } catch (Exception $e) {
                $failedCount++;
                error_log('Push notification failed: ' . $e->getMessage());
            }
        }

        jsonResponse([
            'success' => true,
            'message' => "Notification sent to $sentCount users",
            'sent_count' => $sentCount,
            'failed_count' => $failedCount
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to send notification: ' . $e->getMessage()], 500);
    }
}

function handleGetNotificationHistory($user) {
    global $db;

    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;

    try {
        // Get user's notification history
        $notifications = $db->fetchAll(
            "SELECT nh.*, u.full_name as sent_by_name
             FROM notification_history nh
             LEFT JOIN users u ON nh.sent_by = u.id
             WHERE nh.user_id = ?
             ORDER BY nh.sent_at DESC
             LIMIT $limit OFFSET $offset",
            [$user['id']]
        );

        // Get total count
        $total = $db->fetchOne(
            "SELECT COUNT(*) as total FROM notification_history WHERE user_id = ?",
            [$user['id']]
        )['total'];

        jsonResponse([
            'success' => true,
            'notifications' => $notifications,
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit)
            ]
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to get notification history: ' . $e->getMessage()], 500);
    }
}

function handleMarkAsRead($input, $user) {
    global $db;

    if (!isset($input['notification_id'])) {
        jsonResponse(['error' => 'Notification ID is required'], 400);
    }

    $notificationId = (int)$input['notification_id'];

    try {
        $db->update('notification_history', [
            'read_at' => date('Y-m-d H:i:s')
        ], 'id = ? AND user_id = ?', [$notificationId, $user['id']]);

        jsonResponse([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);

    } catch (Exception $e) {
        jsonResponse(['error' => 'Failed to mark notification as read: ' . $e->getMessage()], 500);
    }
}

function sendPushNotification($subscription, $title, $body, $data = []) {
    // Web Push implementation using VAPID keys

    $payload = json_encode([
        'title' => $title,
        'body' => $body,
        'icon' => '/mobile-app/icons/icon-192x192.png',
        'badge' => '/mobile-app/icons/icon-72x72.png',
        'data' => $data,
        'actions' => [
            [
                'action' => 'view',
                'title' => 'View',
                'icon' => '/mobile-app/icons/icon-96x96.png'
            ],
            [
                'action' => 'dismiss',
                'title' => 'Dismiss'
            ]
        ],
        'requireInteraction' => false,
        'vibrate' => [200, 100, 200]
    ]);

    try {
        // VAPID keys (these should be stored securely in environment variables)
        $vapidPublicKey = 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLuxazjqAKVXTJtkKGlXCB3Z4K1-H2TpQhYOUqFGoHqVcS6PRzs5E8';
        $vapidPrivateKey = 'your-vapid-private-key-here'; // This should be stored securely

        // For now, use a simple HTTP request to the endpoint
        // In production, you should use a proper Web Push library like web-push-php

        $headers = [
            'Content-Type: application/json',
            'TTL: 86400', // 24 hours
            'Urgency: normal'
        ];

        // If using FCM endpoint, add Authorization header
        if (strpos($subscription['endpoint'], 'fcm.googleapis.com') !== false) {
            // Extract registration token from FCM endpoint
            $parts = explode('/', $subscription['endpoint']);
            $token = end($parts);

            // Use FCM HTTP v1 API (requires service account key)
            return sendFCMNotification($token, $title, $body, $data);
        }

        // For other endpoints, use Web Push Protocol
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $subscription['endpoint'],
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $payload,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_TIMEOUT => 30
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // Log for debugging
        error_log("Push notification sent to: " . $subscription['endpoint']);
        error_log("HTTP Code: " . $httpCode);
        error_log("Response: " . $response);

        return $httpCode >= 200 && $httpCode < 300;

    } catch (Exception $e) {
        error_log("Push notification error: " . $e->getMessage());
        return false;
    }
}

function sendFCMNotification($token, $title, $body, $data = []) {
    // Firebase Cloud Messaging implementation
    // This requires a service account key file

    $fcmUrl = 'https://fcm.googleapis.com/v1/projects/flori-construction/messages:send';

    // You would need to implement OAuth2 authentication here
    // For now, return true as placeholder
    error_log("FCM notification would be sent to token: " . $token);
    return true;
}

function generateVAPIDKeys() {
    // This function generates VAPID keys for Web Push
    // In production, generate these once and store securely

    // This is a placeholder - use a proper VAPID key generation library
    return [
        'public_key' => 'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLuxazjqAKVXTJtkKGlXCB3Z4K1-H2TpQhYOUqFGoHqVcS6PRzs5E8',
        'private_key' => 'your-generated-private-key'
    ];
}

// Include authentication function from auth.php
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getallheaders();

    if (isset($headers['Authorization'])) {
        $matches = [];
        if (preg_match('/Bearer\s+(.*)$/i', $headers['Authorization'], $matches)) {
            return $matches[1];
        }
    }

    return null;
}
?>
