/**
 * Admin Panel JavaScript for Flori Construction Ltd
 * Handles admin interface interactions and functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize admin functionality
    initSidebar();
    initModals();
    initForms();
    initFileUploads();
    initTables();
    initConfirmations();
    initDropdowns();
    initTooltips();
    initAnimations();
    initDashboard();
    initModernHeader();
});

// Sidebar functionality
function initSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.admin-sidebar');
    let overlay = null;

    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            toggleSidebar();
        });
    }

    function toggleSidebar() {
        const isOpen = sidebar.classList.contains('show');

        if (isOpen) {
            closeSidebar();
        } else {
            openSidebar();
        }
    }

    function openSidebar() {
        sidebar.classList.add('show');

        // Create overlay if it doesn't exist
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            overlay.addEventListener('click', closeSidebar);
        }

        document.body.appendChild(overlay);

        // Trigger animation
        setTimeout(() => {
            overlay.classList.add('show');
        }, 10);

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus management
        sidebar.setAttribute('aria-hidden', 'false');
        const firstFocusable = sidebar.querySelector('a, button');
        if (firstFocusable) {
            firstFocusable.focus();
        }
    }

    function closeSidebar() {
        sidebar.classList.remove('show');

        if (overlay) {
            overlay.classList.remove('show');
            setTimeout(() => {
                if (overlay && overlay.parentNode) {
                    overlay.parentNode.removeChild(overlay);
                }
            }, 250);
        }

        // Restore body scroll
        document.body.style.overflow = '';

        // Focus management
        sidebar.setAttribute('aria-hidden', 'true');
        sidebarToggle.focus();
    }

    // Close sidebar on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
        }
    });

    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('show')) {
            closeSidebar();
        }
    });

    // Initialize ARIA attributes
    sidebar.setAttribute('aria-hidden', 'true');
    sidebarToggle.setAttribute('aria-expanded', 'false');
}

// Modal functionality
function initModals() {
    const modals = document.querySelectorAll('.modal');
    const modalTriggers = document.querySelectorAll('[data-modal]');
    const modalCloses = document.querySelectorAll('.modal-close, [data-modal-close]');

    // Open modals
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                openModal(modal);
            }
        });
    });

    // Close modals
    modalCloses.forEach(close => {
        close.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                closeModal(modal);
            }
        });
    });

    // Close modal on overlay click
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this);
            }
        });
    });

    // Close modal on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal);
            }
        }
    });
}

function openModal(modal) {
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
}

function closeModal(modal) {
    modal.classList.remove('show');
    document.body.style.overflow = 'auto';
}

// Form functionality
function initForms() {
    const forms = document.querySelectorAll('form[data-ajax]');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitFormAjax(this);
        });
    });

    // Form validation
    const inputs = document.querySelectorAll('.form-control[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', validateInput);
        input.addEventListener('input', clearValidation);
    });
}

function validateInput(e) {
    const input = e.target;
    const value = input.value.trim();

    if (!value) {
        showInputError(input, 'This field is required');
        return false;
    }

    if (input.type === 'email' && !isValidEmail(value)) {
        showInputError(input, 'Please enter a valid email address');
        return false;
    }

    clearInputError(input);
    return true;
}

function clearValidation(e) {
    clearInputError(e.target);
}

function showInputError(input, message) {
    input.classList.add('error');

    let errorElement = input.parentNode.querySelector('.error-message');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        input.parentNode.appendChild(errorElement);
    }
    errorElement.textContent = message;
}

function clearInputError(input) {
    input.classList.remove('error');
    const errorElement = input.parentNode.querySelector('.error-message');
    if (errorElement) {
        errorElement.remove();
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// AJAX form submission
function submitFormAjax(form) {
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    // Show loading state
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="loading"></span> Processing...';

    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message || 'Operation completed successfully');
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 1500);
            } else if (data.reload) {
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } else {
            showAlert('error', data.message || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('error', 'An unexpected error occurred');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
    });
}

// File upload functionality
function initFileUploads() {
    const fileUploads = document.querySelectorAll('.file-upload input[type="file"]');

    fileUploads.forEach(input => {
        input.addEventListener('change', function() {
            const label = this.parentNode.querySelector('.file-upload-label');
            const files = this.files;

            if (files.length > 0) {
                const fileNames = Array.from(files).map(file => file.name).join(', ');
                label.innerHTML = `<i class="fas fa-check"></i><strong>Selected:</strong> ${fileNames}`;
                label.style.borderColor = '#27ae60';
                label.style.background = '#d4edda';
            }
        });
    });

    // Drag and drop functionality
    const dropZones = document.querySelectorAll('.file-upload');

    dropZones.forEach(zone => {
        zone.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
        });

        zone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
        });

        zone.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const input = this.querySelector('input[type="file"]');
            const files = e.dataTransfer.files;

            if (input && files.length > 0) {
                input.files = files;
                input.dispatchEvent(new Event('change'));
            }
        });
    });
}

// Table functionality
function initTables() {
    // Sortable tables
    const sortableHeaders = document.querySelectorAll('.table th[data-sort]');

    sortableHeaders.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const table = this.closest('table');
            const column = this.getAttribute('data-sort');
            sortTable(table, column);
        });
    });

    // Row selection
    const selectAllCheckbox = document.querySelector('#select-all');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
}

function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.getAttribute('data-sort') === column);

    if (columnIndex === -1) return;

    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');

    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();

        if (isAscending) {
            return aValue.localeCompare(bValue, undefined, { numeric: true });
        } else {
            return bValue.localeCompare(aValue, undefined, { numeric: true });
        }
    });

    rows.forEach(row => tbody.appendChild(row));
}

function updateBulkActions() {
    const selectedRows = document.querySelectorAll('.row-checkbox:checked');
    const bulkActions = document.querySelector('.bulk-actions');

    if (bulkActions) {
        if (selectedRows.length > 0) {
            bulkActions.style.display = 'block';
            bulkActions.querySelector('.selected-count').textContent = selectedRows.length;
        } else {
            bulkActions.style.display = 'none';
        }
    }
}

// Confirmation dialogs
function initConfirmations() {
    const confirmButtons = document.querySelectorAll('[data-confirm]');

    confirmButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm');
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
    });
}

// Alert system
function showAlert(type, message) {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();

    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = `
        <span>${message}</span>
        <button type="button" class="alert-close" onclick="this.parentNode.remove()">×</button>
    `;

    alertContainer.appendChild(alert);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

function createAlertContainer() {
    const container = document.createElement('div');
    container.className = 'alert-container';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        max-width: 400px;
    `;
    document.body.appendChild(container);
    return container;
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Enhanced dropdown functionality
function initDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');

    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');

        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other dropdowns
                dropdowns.forEach(otherDropdown => {
                    if (otherDropdown !== dropdown) {
                        otherDropdown.querySelector('.dropdown-menu').style.display = 'none';
                        otherDropdown.querySelector('.dropdown-toggle').setAttribute('aria-expanded', 'false');
                    }
                });

                // Toggle current dropdown
                const isOpen = menu.style.display === 'block';
                menu.style.display = isOpen ? 'none' : 'block';
                toggle.setAttribute('aria-expanded', !isOpen);
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    menu.style.display = 'none';
                    toggle.setAttribute('aria-expanded', 'false');
                }
            });

            // Handle keyboard navigation
            toggle.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggle.click();
                }
            });
        }
    });
}

// Tooltip functionality
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');

    tooltipElements.forEach(element => {
        const title = element.getAttribute('title');
        if (title) {
            element.setAttribute('data-tooltip', title);
            element.removeAttribute('title');

            element.addEventListener('mouseenter', showTooltip);
            element.addEventListener('mouseleave', hideTooltip);
            element.addEventListener('focus', showTooltip);
            element.addEventListener('blur', hideTooltip);
        }
    });
}

function showTooltip(e) {
    const element = e.target;
    const text = element.getAttribute('data-tooltip');

    if (!text) return;

    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.id = 'admin-tooltip';

    document.body.appendChild(tooltip);

    const rect = element.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    tooltip.style.left = rect.left + (rect.width / 2) - (tooltipRect.width / 2) + 'px';
    tooltip.style.top = rect.top - tooltipRect.height - 8 + 'px';

    // Ensure tooltip stays within viewport
    const tooltipLeft = parseFloat(tooltip.style.left);
    const tooltipTop = parseFloat(tooltip.style.top);

    if (tooltipLeft < 8) {
        tooltip.style.left = '8px';
    } else if (tooltipLeft + tooltipRect.width > window.innerWidth - 8) {
        tooltip.style.left = (window.innerWidth - tooltipRect.width - 8) + 'px';
    }

    if (tooltipTop < 8) {
        tooltip.style.top = rect.bottom + 8 + 'px';
    }

    tooltip.style.opacity = '1';
}

function hideTooltip() {
    const tooltip = document.getElementById('admin-tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// Animation and interaction enhancements
function initAnimations() {
    // Add loading states to buttons (only for AJAX forms)
    const buttons = document.querySelectorAll('button[type="submit"], .btn');

    buttons.forEach(button => {
        button.addEventListener('click', function() {
            // Only apply animation to AJAX forms or buttons with data-loading attribute
            const form = this.closest('form');
            const isAjaxForm = form && form.hasAttribute('data-ajax');
            const hasLoadingAttr = this.hasAttribute('data-loading');

            if (this.type === 'submit' && !this.disabled && (isAjaxForm || hasLoadingAttr)) {
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                this.disabled = true;

                // Re-enable after form submission or timeout
                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 3000);
            }
        });
    });

    // Add smooth scrolling to anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;

            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add intersection observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe cards and other elements
    const animateElements = document.querySelectorAll('.stat-card, .dashboard-card, .action-card');
    animateElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
}

// Enhanced alert system with better positioning and animations
function showAlert(type, message, duration = 5000) {
    const alertContainer = document.querySelector('.alert-container') || createAlertContainer();

    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible`;
    alert.innerHTML = `
        <div class="alert-content">
            <i class="alert-icon fas ${getAlertIcon(type)}"></i>
            <span class="alert-message">${message}</span>
        </div>
        <button type="button" class="alert-close" onclick="this.parentNode.remove()" aria-label="Close">
            <i class="fas fa-times"></i>
        </button>
    `;

    alertContainer.appendChild(alert);

    // Trigger animation
    setTimeout(() => alert.classList.add('show'), 10);

    // Auto-remove
    setTimeout(() => {
        alert.classList.add('hide');
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 300);
    }, duration);
}

function getAlertIcon(type) {
    const icons = {
        success: 'fa-check-circle',
        error: 'fa-exclamation-circle',
        warning: 'fa-exclamation-triangle',
        info: 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

// Dashboard-specific functionality
function initDashboard() {
    // Add interactive hover effects to stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-6px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });

        // Add click functionality for inquiry card
        if (card.classList.contains('inquiry-card')) {
            card.style.cursor = 'pointer';
            card.addEventListener('click', function() {
                window.location.href = 'inquiries.php';
            });

            // Add special hover effect for notification badge
            const badge = card.querySelector('.notification-badge');
            if (badge) {
                card.addEventListener('mouseenter', function() {
                    badge.style.transform = 'scale(1.1)';
                    badge.style.animation = 'none';
                });

                card.addEventListener('mouseleave', function() {
                    badge.style.transform = 'scale(1)';
                    badge.style.animation = 'pulse 2s infinite';
                });
            }
        }
    });

    // Add click effects to action cards
    const actionCards = document.querySelectorAll('.action-card');
    actionCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            ripple.className = 'ripple';

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(52, 152, 219, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;

            this.style.position = 'relative';
            this.appendChild(ripple);

            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.remove();
                }
            }, 600);
        });
    });

    // Add real-time clock to welcome banner
    const welcomeBanner = document.querySelector('.welcome-banner');
    if (welcomeBanner) {
        const clockElement = document.createElement('div');
        clockElement.className = 'dashboard-clock';
        clockElement.style.cssText = `
            position: absolute;
            top: 20px;
            right: 20px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
        `;

        welcomeBanner.appendChild(clockElement);

        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('en-US', {
                hour12: true,
                hour: 'numeric',
                minute: '2-digit'
            });
            const dateString = now.toLocaleDateString('en-US', {
                weekday: 'short',
                month: 'short',
                day: 'numeric'
            });
            clockElement.innerHTML = `${dateString}<br>${timeString}`;
        }

        updateClock();
        setInterval(updateClock, 1000);
    }

    // Add smooth number counting animation for stats
    const statNumbers = document.querySelectorAll('.stat-info h3, .welcome-stat .stat-number');
    statNumbers.forEach(numberElement => {
        const finalNumber = parseInt(numberElement.textContent);
        if (!isNaN(finalNumber) && finalNumber > 0) {
            animateNumber(numberElement, 0, finalNumber, 1500);
        }
    });

    // Add progress indicators for project status
    const projectItems = document.querySelectorAll('.project-item');
    projectItems.forEach(item => {
        const status = item.querySelector('.project-status');
        if (status && status.classList.contains('ongoing')) {
            // Add a subtle progress indicator
            const progressBar = document.createElement('div');
            progressBar.className = 'project-progress';
            progressBar.style.cssText = `
                position: absolute;
                bottom: 0;
                left: 0;
                height: 2px;
                background: linear-gradient(90deg, #f39c12, #e67e22);
                width: ${Math.random() * 40 + 30}%;
                border-radius: 1px;
                opacity: 0.7;
            `;
            item.style.position = 'relative';
            item.appendChild(progressBar);
        }
    });
}

// Number counting animation
function animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const range = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.floor(start + (range * easeOutQuart));

        element.textContent = current;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        } else {
            element.textContent = end;
        }
    }

    requestAnimationFrame(updateNumber);
}

// Add CSS for ripple animation
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(2);
            opacity: 0;
        }
    }

    .project-progress {
        animation: progressPulse 2s ease-in-out infinite;
    }

    @keyframes progressPulse {
        0%, 100% { opacity: 0.7; }
        50% { opacity: 1; }
    }
`;
document.head.appendChild(rippleStyle);

// Modern Header functionality
function initModernHeader() {
    // User profile dropdown
    const userProfileBtn = document.querySelector('.user-profile-btn');
    const dropdown = document.querySelector('.user-profile .dropdown');
    const dropdownMenu = document.querySelector('.dropdown-menu');

    if (userProfileBtn && dropdown) {
        userProfileBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const isActive = dropdown.classList.contains('active');

            // Close all other dropdowns
            document.querySelectorAll('.dropdown.active').forEach(d => {
                d.classList.remove('active');
            });

            if (!isActive) {
                dropdown.classList.add('active');
                userProfileBtn.setAttribute('aria-expanded', 'true');
            } else {
                userProfileBtn.setAttribute('aria-expanded', 'false');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('active');
                userProfileBtn.setAttribute('aria-expanded', 'false');
            }
        });

        // Close dropdown on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && dropdown.classList.contains('active')) {
                dropdown.classList.remove('active');
                userProfileBtn.setAttribute('aria-expanded', 'false');
                userProfileBtn.focus();
            }
        });
    }

    // Search functionality
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function(e) {
            const query = e.target.value.trim();
            if (query.length > 2) {
                // Implement search functionality here
                console.log('Searching for:', query);
            }
        }, 300));
    }

    // Dashboard filters functionality
    const timePeriodFilter = document.getElementById('timePeriodFilter');
    const statusFilter = document.getElementById('statusFilter');

    if (timePeriodFilter) {
        timePeriodFilter.addEventListener('change', function(e) {
            const period = e.target.value;
            console.log('Time period changed to:', period);
            // Implement filter logic here
            updateDashboardData(period, statusFilter?.value || 'all');
        });
    }

    if (statusFilter) {
        statusFilter.addEventListener('change', function(e) {
            const status = e.target.value;
            console.log('Status filter changed to:', status);
            // Implement filter logic here
            updateDashboardData(timePeriodFilter?.value || 'this_month', status);
        });
    }
}

// Dashboard data update function
function updateDashboardData(period, status) {
    // Show loading state
    showLoadingState();

    // Simulate API call with timeout
    setTimeout(() => {
        // Hide loading state
        hideLoadingState();

        // Update dashboard content based on filters
        console.log(`Updating dashboard for period: ${period}, status: ${status}`);

        // You can implement actual AJAX calls here to fetch filtered data
        // Example:
        // fetch(`/admin/api/dashboard-data.php?period=${period}&status=${status}`)
        //     .then(response => response.json())
        //     .then(data => updateDashboardUI(data));

    }, 1000);
}

// Loading state functions
function showLoadingState() {
    const overviewCards = document.querySelectorAll('.overview-card, .quick-stat-item');
    overviewCards.forEach(card => {
        card.style.opacity = '0.6';
        card.style.pointerEvents = 'none';
    });

    // Add loading spinner to refresh button
    const refreshBtn = document.querySelector('.refresh-btn i');
    if (refreshBtn) {
        refreshBtn.classList.add('fa-spin');
    }
}

function hideLoadingState() {
    const overviewCards = document.querySelectorAll('.overview-card, .quick-stat-item');
    overviewCards.forEach(card => {
        card.style.opacity = '1';
        card.style.pointerEvents = 'auto';
    });

    // Remove loading spinner from refresh button
    const refreshBtn = document.querySelector('.refresh-btn i');
    if (refreshBtn) {
        refreshBtn.classList.remove('fa-spin');
    }
}

// Export functions for global use
window.AdminJS = {
    openModal,
    closeModal,
    showAlert,
    submitFormAjax,
    debounce,
    formatFileSize,
    showTooltip,
    hideTooltip,
    animateNumber,
    initModernHeader
};
