<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle actions
$action = $_GET['action'] ?? 'list';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'upload') {
        $title = sanitize($_POST['title']);
        $description = sanitize($_POST['description']);
        $altText = sanitize($_POST['alt_text']);
        $category = sanitize($_POST['category']);

        // Handle multiple file uploads
        if (isset($_FILES['media_files']) && !empty($_FILES['media_files']['name'][0])) {
            $uploadedCount = 0;
            $errorCount = 0;
            $errors = [];

            // Process each uploaded file
            for ($i = 0; $i < count($_FILES['media_files']['name']); $i++) {
                // Skip empty files
                if (empty($_FILES['media_files']['name'][$i])) {
                    continue;
                }

                // Check for upload errors
                if ($_FILES['media_files']['error'][$i] !== UPLOAD_ERR_OK) {
                    $errorCount++;
                    $uploadErrors = [
                        UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                        UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
                        UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                        UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                        UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                        UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                        UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
                    ];
                    $errorMsg = isset($uploadErrors[$_FILES['media_files']['error'][$i]])
                        ? $uploadErrors[$_FILES['media_files']['error'][$i]]
                        : 'Unknown upload error';
                    $errors[] = $_FILES['media_files']['name'][$i] . ': ' . $errorMsg;
                    continue;
                }

                // Create file array for uploadFile function
                $file = [
                    'name' => $_FILES['media_files']['name'][$i],
                    'type' => $_FILES['media_files']['type'][$i],
                    'tmp_name' => $_FILES['media_files']['tmp_name'][$i],
                    'error' => $_FILES['media_files']['error'][$i],
                    'size' => $_FILES['media_files']['size'][$i]
                ];

                try {
                    $uploadResult = uploadFile($file, $category);

                    // Determine file type
                    $fileExtension = strtolower(pathinfo($uploadResult['original_name'], PATHINFO_EXTENSION));
                    $fileType = in_array($fileExtension, ALLOWED_VIDEO_TYPES) ? 'video' : 'image';

                    // Use file-specific title if multiple files, otherwise use the form title
                    $fileTitle = (count($_FILES['media_files']['name']) > 1)
                        ? pathinfo($uploadResult['original_name'], PATHINFO_FILENAME)
                        : $title;

                    $mediaData = [
                        'title' => $fileTitle,
                        'description' => $description,
                        'alt_text' => $altText,
                        'file_path' => $uploadResult['file_path'],
                        'file_name' => $uploadResult['filename'],
                        'original_name' => $uploadResult['original_name'],
                        'file_size' => $uploadResult['file_size'],
                        'file_type' => $fileType,
                        'mime_type' => $uploadResult['mime_type'],
                        'category' => $category,
                        'uploaded_by' => $user['id'],
                        'created_at' => date('Y-m-d H:i:s')
                    ];

                    $db->insert('media', $mediaData);
                    $uploadedCount++;

                } catch (Exception $e) {
                    $errorCount++;
                    $errors[] = $_FILES['media_files']['name'][$i] . ': ' . $e->getMessage();
                }
            }

            // Set appropriate message based on results
            if ($uploadedCount > 0 && $errorCount === 0) {
                $message = $uploadedCount === 1
                    ? 'Media uploaded successfully!'
                    : "{$uploadedCount} media files uploaded successfully!";
                $messageType = 'success';
                $action = 'list';
            } elseif ($uploadedCount > 0 && $errorCount > 0) {
                $message = "{$uploadedCount} files uploaded successfully, {$errorCount} failed. Errors: " . implode('; ', $errors);
                $messageType = 'warning';
                $action = 'list';
            } else {
                $message = 'Upload failed. Errors: ' . implode('; ', $errors);
                $messageType = 'error';
            }
        } else {
            $message = 'Please select at least one file to upload';
            $messageType = 'error';
        }
    } elseif ($action === 'delete') {
        $mediaId = $_POST['id'];
        try {
            // Get media info before deleting
            $media = $db->fetchOne("SELECT * FROM media WHERE id = ?", [$mediaId]);
            if ($media) {
                // Delete file from filesystem
                $filePath = UPLOAD_PATH . '/' . $media['file_path'];
                if (file_exists($filePath)) {
                    unlink($filePath);
                }

                // Delete from database
                $db->update('media', ['is_active' => 0], 'id = ?', [$mediaId]);
                $message = 'Media deleted successfully!';
                $messageType = 'success';
            } else {
                $message = 'Media not found';
                $messageType = 'error';
            }
            $action = 'list';
        } catch (Exception $e) {
            $message = 'Error deleting media: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif ($action === 'edit') {
        $mediaId = $_POST['id'];
        $title = sanitize($_POST['title']);
        $description = sanitize($_POST['description']);
        $altText = sanitize($_POST['alt_text']);
        $category = sanitize($_POST['category']);

        try {
            $mediaData = [
                'title' => $title,
                'description' => $description,
                'alt_text' => $altText,
                'category' => $category,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $db->update('media', $mediaData, 'id = ?', [$mediaId]);
            $message = 'Media updated successfully!';
            $messageType = 'success';
            $action = 'list';

        } catch (Exception $e) {
            $message = 'Error updating media: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get media for editing
$media = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $media = $db->fetchOne("SELECT * FROM media WHERE id = ? AND is_active = 1", [$_GET['id']]);
    if (!$media) {
        $action = 'list';
        $message = 'Media not found';
        $messageType = 'error';
    }
}

// Get media list
if ($action === 'list') {
    $page = (int)($_GET['page'] ?? 1);
    $limit = ADMIN_ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';
    $categoryFilter = $_GET['category'] ?? '';
    $typeFilter = $_GET['type'] ?? '';

    $where = ['m.is_active = 1'];
    $params = [];

    if ($search) {
        $where[] = '(m.title LIKE ? OR m.description LIKE ? OR m.original_name LIKE ?)';
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    if ($categoryFilter && in_array($categoryFilter, ['projects', 'services', 'gallery', 'general'])) {
        $where[] = 'm.category = ?';
        $params[] = $categoryFilter;
    }

    if ($typeFilter && in_array($typeFilter, ['image', 'video'])) {
        $where[] = 'm.file_type = ?';
        $params[] = $typeFilter;
    }

    $whereClause = implode(' AND ', $where);

    $totalQuery = "SELECT COUNT(*) as total FROM media m WHERE $whereClause";
    $total = $db->fetchOne($totalQuery, $params)['total'];

    $mediaQuery = "SELECT m.*, u.full_name as uploaded_by_name
                   FROM media m
                   LEFT JOIN users u ON m.uploaded_by = u.id
                   WHERE $whereClause
                   ORDER BY m.created_at DESC
                   LIMIT $limit OFFSET $offset";
    $mediaItems = $db->fetchAll($mediaQuery, $params);

    $totalPages = ceil($total / $limit);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Media Management - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Enhanced Media Page Styles */
        .media-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .media-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .media-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .media-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .media-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .media-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .media-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .media-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .media-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        .enhanced-filters-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
        }

        .enhanced-filters-content {
            padding: var(--spacing-xl);
        }

        .enhanced-filters-form {
            display: flex;
            gap: var(--spacing-lg);
            align-items: end;
            flex-wrap: wrap;
        }

        .enhanced-filter-group {
            flex: 1;
            min-width: 200px;
        }

        .enhanced-filter-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .enhanced-media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-2xl);
        }

        .enhanced-media-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            overflow: hidden;
            position: relative;
        }

        .enhanced-media-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .enhanced-media-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .enhanced-media-card:hover::before {
            opacity: 1;
        }

        .enhanced-media-preview {
            position: relative;
            height: 200px;
            overflow: hidden;
            background: var(--gray-100);
        }

        .enhanced-media-file {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform var(--transition-base);
        }

        .enhanced-media-card:hover .enhanced-media-file {
            transform: scale(1.05);
        }

        .enhanced-media-overlay {
            position: absolute;
            top: var(--spacing-md);
            right: var(--spacing-md);
            display: flex;
            gap: var(--spacing-sm);
        }

        .enhanced-media-type-badge {
            background: rgba(0, 0, 0, 0.7);
            color: var(--white);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            backdrop-filter: blur(10px);
        }

        .enhanced-media-content {
            padding: var(--spacing-lg);
        }

        .enhanced-media-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            margin: 0 0 var(--spacing-sm) 0;
            line-height: var(--line-height-tight);
        }

        .enhanced-media-description {
            font-size: var(--font-size-sm);
            color: var(--gray-600);
            margin: 0 0 var(--spacing-md) 0;
            line-height: var(--line-height-relaxed);
        }

        .enhanced-media-meta {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }

        .enhanced-meta-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            font-size: var(--font-size-xs);
            color: var(--gray-500);
        }

        .enhanced-media-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--gray-200);
        }

        .enhanced-action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }

        .enhanced-action-btn {
            width: 36px;
            height: 36px;
            border-radius: var(--border-radius-md);
            border: 1px solid var(--gray-300);
            background: var(--white);
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-base);
            text-decoration: none;
            font-size: var(--font-size-sm);
        }

        .enhanced-action-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
            transform: translateY(-1px);
        }

        .enhanced-action-btn.delete-btn:hover {
            background: var(--danger-color);
            border-color: var(--danger-color);
        }

        .enhanced-empty-state {
            text-align: center;
            padding: var(--spacing-3xl) var(--spacing-xl);
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
        }

        .enhanced-empty-state-icon {
            font-size: 4rem;
            color: var(--gray-300);
            margin-bottom: var(--spacing-xl);
        }

        .enhanced-empty-state h3 {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-700);
            margin: 0 0 var(--spacing-md) 0;
        }

        .enhanced-empty-state p {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin: 0 0 var(--spacing-xl) 0;
        }

        /* Enhanced Upload Form */
        .enhanced-upload-header {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
        }

        .enhanced-upload-header-content {
            padding: var(--spacing-xl);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            color: var(--white);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .enhanced-upload-header h2 {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .enhanced-file-upload-area {
            border: 2px dashed var(--gray-300);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-2xl);
            text-align: center;
            background: var(--gray-50);
            transition: all var(--transition-base);
            position: relative;
            overflow: hidden;
        }

        .enhanced-file-upload-area:hover {
            border-color: var(--primary-color);
            background: var(--white);
        }

        .enhanced-file-upload-area.dragover {
            border-color: var(--primary-color);
            background: rgba(52, 152, 219, 0.05);
        }

        .enhanced-upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
        }

        .enhanced-upload-text {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-700);
            margin-bottom: var(--spacing-sm);
        }

        .enhanced-upload-formats {
            font-size: var(--font-size-sm);
            color: var(--gray-600);
            line-height: var(--line-height-relaxed);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .media-page-header .header-content {
                flex-direction: column;
                text-align: center;
            }

            .media-stats {
                flex-direction: column;
                align-items: center;
            }

            .enhanced-filters-form {
                flex-direction: column;
            }

            .enhanced-filter-group {
                min-width: auto;
            }

            .enhanced-filter-actions {
                justify-content: center;
            }

            .enhanced-media-grid {
                grid-template-columns: 1fr;
            }

            .enhanced-upload-header-content {
                flex-direction: column;
                text-align: center;
                gap: var(--spacing-lg);
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                <!-- Enhanced Media Header -->
                <div class="media-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-photo-video"></i>
                                Media Library
                            </h1>
                            <p class="page-description">Manage your images, videos, and other media files for projects and content</p>
                        </div>
                        <div class="header-actions">
                            <a href="?action=upload" class="btn btn-success btn-lg">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>Upload Media</span>
                            </a>
                        </div>
                    </div>

                    <?php
                    // Calculate media statistics
                    $totalSize = array_sum(array_column($mediaItems, 'file_size'));
                    $imageCount = count(array_filter($mediaItems, fn($m) => $m['file_type'] === 'image'));
                    $videoCount = count(array_filter($mediaItems, fn($m) => $m['file_type'] === 'video'));
                    $documentCount = count($mediaItems) - $imageCount - $videoCount;
                    ?>

                    <div class="media-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= $total ?></span>
                            <span class="stat-label">Total Files</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $imageCount ?></span>
                            <span class="stat-label">Images</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $videoCount ?></span>
                            <span class="stat-label">Videos</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= formatFileSize($totalSize) ?></span>
                            <span class="stat-label">Total Size</span>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Search & Filters -->
                <div class="enhanced-filters-card">
                    <div class="enhanced-filters-content">
                        <form method="GET" class="enhanced-filters-form">
                            <input type="hidden" name="action" value="list">

                            <div class="enhanced-filter-group">
                                <label for="search" class="form-label">
                                    <i class="fas fa-search"></i>
                                    Search Media
                                </label>
                                <input type="text"
                                       id="search"
                                       name="search"
                                       placeholder="Search by title, description, filename..."
                                       value="<?= htmlspecialchars($search) ?>"
                                       class="form-control">
                            </div>

                            <div class="enhanced-filter-group">
                                <label for="category" class="form-label">
                                    <i class="fas fa-folder"></i>
                                    Category
                                </label>
                                <select id="category" name="category" class="form-control">
                                    <option value="">All Categories</option>
                                    <option value="projects" <?= $categoryFilter === 'projects' ? 'selected' : '' ?>>Projects</option>
                                    <option value="services" <?= $categoryFilter === 'services' ? 'selected' : '' ?>>Services</option>
                                    <option value="gallery" <?= $categoryFilter === 'gallery' ? 'selected' : '' ?>>Gallery</option>
                                    <option value="general" <?= $categoryFilter === 'general' ? 'selected' : '' ?>>General</option>
                                </select>
                            </div>

                            <div class="enhanced-filter-group">
                                <label for="type" class="form-label">
                                    <i class="fas fa-file-alt"></i>
                                    File Type
                                </label>
                                <select id="type" name="type" class="form-control">
                                    <option value="">All Types</option>
                                    <option value="image" <?= $typeFilter === 'image' ? 'selected' : '' ?>>Images</option>
                                    <option value="video" <?= $typeFilter === 'video' ? 'selected' : '' ?>>Videos</option>
                                </select>
                            </div>

                            <div class="enhanced-filter-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    Apply Filters
                                </button>
                                <?php if ($search || $categoryFilter || $typeFilter): ?>
                                <a href="?action=list" class="btn btn-outline">
                                    <i class="fas fa-times"></i>
                                    Clear All
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Media Content -->
                <?php if (empty($mediaItems)): ?>
                <div class="enhanced-empty-state">
                    <div class="enhanced-empty-state-icon">
                        <i class="fas fa-photo-video"></i>
                    </div>
                    <h3>No Media Found</h3>
                    <p>
                        <?php if ($search || $categoryFilter || $typeFilter): ?>
                            No media files match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            You haven't uploaded any media files yet. Start building your media library by uploading your first file.
                        <?php endif; ?>
                    </p>
                    <a href="?action=upload" class="btn btn-success btn-lg">
                        <i class="fas fa-cloud-upload-alt"></i>
                        Upload Your First Media
                    </a>
                </div>
                <?php else: ?>
                <div class="media-table-container">
                    <div class="table-header">
                        <div class="table-title">
                            <h3>Media Library</h3>
                            <span class="table-count"><?= count($mediaItems) ?> of <?= $total ?> files</span>
                        </div>
                        <div class="table-actions">
                            <button class="view-toggle" data-view="grid" title="Grid View">
                                <i class="fas fa-th-large"></i>
                            </button>
                            <button class="view-toggle active" data-view="list" title="List View">
                                <i class="fas fa-list"></i>
                            </button>
                        </div>
                    </div>
                    <div class="enhanced-media-grid">
                        <?php foreach ($mediaItems as $item): ?>
                        <div class="enhanced-media-card">
                            <div class="enhanced-media-preview">
                                <?php if ($item['file_type'] === 'video'): ?>
                                <video class="enhanced-media-file" controls>
                                    <source src="<?= UPLOAD_URL . '/' . $item['file_path'] ?>" type="<?= $item['mime_type'] ?>">
                                    Your browser does not support the video tag.
                                </video>
                                <?php else: ?>
                                <img src="<?= UPLOAD_URL . '/' . $item['file_path'] ?>"
                                     alt="<?= htmlspecialchars($item['alt_text'] ?: $item['title']) ?>"
                                     class="enhanced-media-file"
                                     loading="lazy">
                                <?php endif; ?>

                                <div class="enhanced-media-overlay">
                                    <span class="enhanced-media-type-badge">
                                        <i class="fas fa-<?= $item['file_type'] === 'video' ? 'video' : 'image' ?>"></i>
                                        <?= ucfirst($item['file_type']) ?>
                                    </span>
                                </div>
                            </div>

                            <div class="enhanced-media-content">
                                <h4 class="enhanced-media-title"><?= htmlspecialchars($item['title']) ?></h4>
                                <p class="enhanced-media-description"><?= htmlspecialchars(substr($item['description'], 0, 100)) ?><?= strlen($item['description']) > 100 ? '...' : '' ?></p>

                                <div class="enhanced-media-meta">
                                    <span class="enhanced-meta-item">
                                        <i class="fas fa-folder"></i>
                                        <?= ucfirst($item['category']) ?>
                                    </span>
                                    <span class="enhanced-meta-item">
                                        <i class="fas fa-weight"></i>
                                        <?= formatFileSize($item['file_size']) ?>
                                    </span>
                                    <span class="enhanced-meta-item">
                                        <i class="fas fa-calendar"></i>
                                        <?= formatDate($item['created_at']) ?>
                                    </span>
                                    <span class="enhanced-meta-item">
                                        <i class="fas fa-user"></i>
                                        <?= htmlspecialchars($item['uploaded_by_name']) ?>
                                    </span>
                                </div>

                                <div class="enhanced-media-actions">
                                    <div class="enhanced-action-buttons">
                                        <a href="?action=edit&id=<?= $item['id'] ?>"
                                           class="enhanced-action-btn"
                                           title="Edit Media">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="<?= UPLOAD_URL . '/' . $item['file_path'] ?>"
                                           target="_blank"
                                           class="enhanced-action-btn"
                                           title="View Media">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        <button type="button"
                                                class="enhanced-action-btn"
                                                onclick="copyToClipboard('<?= UPLOAD_URL . '/' . $item['file_path'] ?>')"
                                                title="Copy URL">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                        <form method="POST" style="display: inline;"
                                              onsubmit="return confirm('Are you sure you want to delete this media? This action cannot be undone.')">
                                            <?= getCSRFField() ?>
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?= $item['id'] ?>">
                                            <button type="submit"
                                                    class="enhanced-action-btn delete-btn"
                                                    title="Delete Media">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Enhanced Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="table-footer">
                    <div class="pagination-info">
                        <span>Showing <?= (($page - 1) * $limit) + 1 ?> to <?= min($page * $limit, $total) ?> of <?= $total ?> files</span>
                    </div>
                    <nav class="modern-pagination" aria-label="Media pagination">
                        <div class="pagination-controls">
                            <?php if ($page > 1): ?>
                            <a href="?action=list&page=1<?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>"
                               class="pagination-btn first-btn" title="First page">
                                <i class="fas fa-angle-double-left"></i>
                            </a>
                            <a href="?action=list&page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>"
                               class="pagination-btn prev-btn" title="Previous page">
                                <i class="fas fa-angle-left"></i>
                                <span>Previous</span>
                            </a>
                            <?php endif; ?>

                            <div class="pagination-numbers">
                                <?php
                                $start = max(1, $page - 2);
                                $end = min($totalPages, $page + 2);

                                if ($start > 1): ?>
                                    <a href="?action=list&page=1<?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>" class="pagination-number">1</a>
                                    <?php if ($start > 2): ?>
                                        <span class="pagination-ellipsis">...</span>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $start; $i <= $end; $i++): ?>
                                    <?php if ($i == $page): ?>
                                    <span class="pagination-number active"><?= $i ?></span>
                                    <?php else: ?>
                                    <a href="?action=list&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>" class="pagination-number"><?= $i ?></a>
                                    <?php endif; ?>
                                <?php endfor; ?>

                                <?php if ($end < $totalPages): ?>
                                    <?php if ($end < $totalPages - 1): ?>
                                        <span class="pagination-ellipsis">...</span>
                                    <?php endif; ?>
                                    <a href="?action=list&page=<?= $totalPages ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>" class="pagination-number"><?= $totalPages ?></a>
                                <?php endif; ?>
                            </div>

                            <?php if ($page < $totalPages): ?>
                            <a href="?action=list&page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>"
                               class="pagination-btn next-btn" title="Next page">
                                <span>Next</span>
                                <i class="fas fa-angle-right"></i>
                            </a>
                            <a href="?action=list&page=<?= $totalPages ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $categoryFilter ? '&category=' . $categoryFilter : '' ?><?= $typeFilter ? '&type=' . $typeFilter : '' ?>"
                               class="pagination-btn last-btn" title="Last page">
                                <i class="fas fa-angle-double-right"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </nav>
                </div>
                <?php endif; ?>
                <?php endif; ?>

                <?php elseif ($action === 'upload'): ?>
                <!-- Enhanced Upload Form Header -->
                <div class="enhanced-upload-header">
                    <div class="enhanced-upload-header-content">
                        <h2>
                            <i class="fas fa-cloud-upload-alt"></i>
                            Upload Media Files
                        </h2>
                        <a href="?action=list" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i>
                            Back to Library
                        </a>
                    </div>
                </div>

                <form method="POST" enctype="multipart/form-data" class="modern-form" id="media-upload-form">
                    <?= getCSRFField() ?>

                    <!-- File Upload Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file-upload"></i>
                                Select Files
                            </h3>
                            <p class="section-description">Choose the media files you want to upload</p>
                        </div>
                        <div class="section-content">
                            <div class="form-field full-width">
                                <label for="media_files" class="field-label">
                                    <i class="fas fa-files"></i>
                                    Media Files *
                                </label>
                                <div class="enhanced-file-upload-area">
                                    <input type="file" id="media_files" name="media_files[]" accept="image/*,video/*" multiple required style="display: none;">
                                    <label for="media_files" style="cursor: pointer; display: block;">
                                        <div class="enhanced-upload-icon">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                        </div>
                                        <div class="enhanced-upload-text">
                                            <strong>Choose files</strong> or drag them here
                                        </div>
                                        <div class="enhanced-upload-formats">
                                            Supported formats: JPG, PNG, GIF, WebP, MP4, MOV, AVI (Max: 10MB each)
                                            <br>You can select multiple files at once
                                        </div>
                                    </label>
                                    <div class="file-preview" id="file-preview" style="display: none;">
                                        <h4>Selected Files:</h4>
                                        <div id="file-list" class="file-list"></div>
                                    </div>
                                </div>
                                <div class="field-help">Upload high-quality images and videos for your projects and content.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Media Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                Media Information
                            </h3>
                            <p class="section-description">Provide details about your media files</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label for="title" class="field-label">
                                        <i class="fas fa-heading"></i>
                                        Title *
                                    </label>
                                    <input type="text" id="title" name="title" class="form-input" required placeholder="Enter media title...">
                                    <div class="field-help">A descriptive title for your media file</div>
                                </div>

                                <div class="form-field">
                                    <label for="category" class="field-label">
                                        <i class="fas fa-folder"></i>
                                        Category *
                                    </label>
                                    <select id="category" name="category" class="form-select" required>
                                        <option value="">Select Category</option>
                                        <option value="projects">Projects</option>
                                        <option value="services">Services</option>
                                        <option value="gallery">Gallery</option>
                                        <option value="general">General</option>
                                    </select>
                                    <div class="field-help">Choose the appropriate category for organization</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="description" class="field-label">
                                        <i class="fas fa-align-left"></i>
                                        Description
                                    </label>
                                    <textarea id="description" name="description" class="form-textarea" rows="3" placeholder="Describe your media file..."></textarea>
                                    <div class="field-help">Optional description to provide more context about the media</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="alt_text" class="field-label">
                                        <i class="fas fa-eye"></i>
                                        Alt Text (for images)
                                    </label>
                                    <input type="text" id="alt_text" name="alt_text" class="form-input" placeholder="Describe the image for accessibility...">
                                    <div class="field-help">Describe the image for accessibility and SEO purposes</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-success btn-large">
                            <i class="fas fa-upload"></i>
                            <span>Upload Media</span>
                        </button>
                        <a href="?action=list" class="btn btn-outline btn-large">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </a>
                    </div>
                </form>

                <?php elseif ($action === 'edit'): ?>
                <!-- Enhanced Edit Form Header -->
                <div class="form-header">
                    <div class="form-header-content">
                        <div class="form-title-section">
                            <h2 class="form-main-title">
                                <i class="fas fa-edit form-icon"></i>
                                Edit Media
                            </h2>
                            <p class="form-subtitle">Update media information and settings</p>
                        </div>
                        <div class="form-actions-header">
                            <a href="?action=list" class="btn btn-outline btn-large">
                                <i class="fas fa-arrow-left"></i>
                                <span>Back to Library</span>
                            </a>
                        </div>
                    </div>
                </div>

                <form method="POST" class="modern-form">
                    <?= getCSRFField() ?>
                    <input type="hidden" name="id" value="<?= $media['id'] ?>">

                    <!-- Current Media Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-file"></i>
                                Current Media File
                            </h3>
                            <p class="section-description">Preview of the current media file</p>
                        </div>
                        <div class="section-content">
                            <div class="current-media-preview">
                                <div class="media-preview-container">
                                    <?php if ($media['file_type'] === 'video'): ?>
                                    <video class="current-media-file" controls>
                                        <source src="<?= UPLOAD_URL . '/' . $media['file_path'] ?>" type="<?= $media['mime_type'] ?>">
                                        Your browser does not support the video tag.
                                    </video>
                                    <?php else: ?>
                                    <img src="<?= UPLOAD_URL . '/' . $media['file_path'] ?>"
                                         alt="<?= htmlspecialchars($media['alt_text'] ?: $media['title']) ?>"
                                         class="current-media-file">
                                    <?php endif; ?>
                                    <div class="media-info-overlay">
                                        <div class="media-type-badge <?= $media['file_type'] ?>">
                                            <i class="fas fa-<?= $media['file_type'] === 'video' ? 'video' : 'image' ?>"></i>
                                            <?= ucfirst($media['file_type']) ?>
                                        </div>
                                        <div class="media-size">
                                            <?= formatFileSize($media['file_size']) ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Media Information Section -->
                    <div class="form-section">
                        <div class="section-header">
                            <h3 class="section-title">
                                <i class="fas fa-info-circle"></i>
                                Media Information
                            </h3>
                            <p class="section-description">Update media details and metadata</p>
                        </div>
                        <div class="section-content">
                            <div class="form-grid">
                                <div class="form-field">
                                    <label for="title" class="field-label">
                                        <i class="fas fa-heading"></i>
                                        Title *
                                    </label>
                                    <input type="text" id="title" name="title" class="form-input"
                                           value="<?= htmlspecialchars($media['title']) ?>" required placeholder="Enter media title...">
                                    <div class="field-help">A descriptive title for your media file</div>
                                </div>

                                <div class="form-field">
                                    <label for="category" class="field-label">
                                        <i class="fas fa-folder"></i>
                                        Category *
                                    </label>
                                    <select id="category" name="category" class="form-select" required>
                                        <option value="">Select Category</option>
                                        <option value="projects" <?= $media['category'] === 'projects' ? 'selected' : '' ?>>Projects</option>
                                        <option value="services" <?= $media['category'] === 'services' ? 'selected' : '' ?>>Services</option>
                                        <option value="gallery" <?= $media['category'] === 'gallery' ? 'selected' : '' ?>>Gallery</option>
                                        <option value="general" <?= $media['category'] === 'general' ? 'selected' : '' ?>>General</option>
                                    </select>
                                    <div class="field-help">Choose the appropriate category for organization</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="description" class="field-label">
                                        <i class="fas fa-align-left"></i>
                                        Description
                                    </label>
                                    <textarea id="description" name="description" class="form-textarea" rows="3" placeholder="Describe your media file..."><?= htmlspecialchars($media['description']) ?></textarea>
                                    <div class="field-help">Optional description to provide more context about the media</div>
                                </div>

                                <div class="form-field full-width">
                                    <label for="alt_text" class="field-label">
                                        <i class="fas fa-eye"></i>
                                        Alt Text (for images)
                                    </label>
                                    <input type="text" id="alt_text" name="alt_text" class="form-input"
                                           value="<?= htmlspecialchars($media['alt_text']) ?>" placeholder="Describe the image for accessibility...">
                                    <div class="field-help">Describe the image for accessibility and SEO purposes</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-success btn-large">
                            <i class="fas fa-save"></i>
                            <span>Update Media</span>
                        </button>
                        <a href="?action=list" class="btn btn-outline btn-large">
                            <i class="fas fa-times"></i>
                            <span>Cancel</span>
                        </a>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                AdminJS.showAlert('success', 'URL copied to clipboard!');
            }, function(err) {
                AdminJS.showAlert('error', 'Failed to copy URL');
            });
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // File upload preview functionality
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('media_files');
            const filePreview = document.getElementById('file-preview');
            const fileList = document.getElementById('file-list');
            const titleInput = document.getElementById('title');

            if (fileInput) {
                fileInput.addEventListener('change', function() {
                    const files = this.files;

                    if (files.length > 0) {
                        filePreview.style.display = 'block';
                        fileList.innerHTML = '';

                        // Auto-fill title if single file and title is empty
                        if (files.length === 1 && !titleInput.value.trim()) {
                            const fileName = files[0].name;
                            const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
                            titleInput.value = nameWithoutExt.replace(/[_-]/g, ' ');
                        }

                        Array.from(files).forEach((file, index) => {
                            const fileItem = document.createElement('div');
                            fileItem.className = 'file-item';
                            fileItem.style.cssText = 'display: flex; align-items: center; padding: 10px; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 10px; background: #f8f9fa;';

                            const fileIcon = file.type.startsWith('video/') ? 'fas fa-video' : 'fas fa-image';
                            const fileSize = formatFileSize(file.size);
                            const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
                            const sizeClass = isValidSize ? 'text-success' : 'text-danger';

                            fileItem.innerHTML = `
                                <i class="${fileIcon}" style="margin-right: 10px; color: #6c757d;"></i>
                                <div style="flex: 1;">
                                    <div style="font-weight: 500;">${file.name}</div>
                                    <div style="font-size: 12px; color: #6c757d;">
                                        Type: ${file.type} | Size: <span class="${sizeClass}">${fileSize}</span>
                                        ${!isValidSize ? ' (Too large!)' : ''}
                                    </div>
                                </div>
                                <i class="fas fa-check-circle" style="color: #27ae60; margin-left: 10px;"></i>
                            `;

                            fileList.appendChild(fileItem);
                        });
                    } else {
                        filePreview.style.display = 'none';
                    }
                });
            }

            // Form submission handling
            const uploadForm = document.getElementById('media-upload-form');
            if (uploadForm) {
                uploadForm.addEventListener('submit', function(e) {
                    const files = fileInput.files;
                    let hasOversizedFiles = false;

                    Array.from(files).forEach(file => {
                        if (file.size > 10 * 1024 * 1024) { // 10MB
                            hasOversizedFiles = true;
                        }
                    });

                    if (hasOversizedFiles) {
                        e.preventDefault();
                        AdminJS.showAlert('error', 'Some files are larger than 10MB. Please select smaller files.');
                        return false;
                    }

                    // Show loading state
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
                    }
                });
            }
        });
    </script>
</body>
</html>
