<?php
/**
 * Accessibility Test Page
 * Tests accessibility features and screen reader compatibility
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accessibility Test - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .accessibility-score {
            background: #e8f5e8;
            border: 2px solid #27ae60;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }

        .score-good { background: #e8f5e8; border-color: #27ae60; }
        .score-warning { background: #fff3cd; border-color: #f39c12; }
        .score-error { background: #f8d7da; border-color: #e74c3c; }

        .test-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 6px;
            background: #f8f9fa;
        }

        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .status-pass { background: #27ae60; color: white; }
        .status-fail { background: #e74c3c; color: white; }
        .status-warning { background: #f39c12; color: white; }

        .focus-test-area {
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .focus-test-area:focus-within {
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3);
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        .high-contrast-test {
            background: #000;
            color: #fff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .color-contrast-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .contrast-sample {
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Screen Reader Announcement -->
                <div class="sr-only" aria-live="polite" id="announcements">
                    Accessibility test page loaded
                </div>

                <div class="test-section">
                    <h1>
                        <i class="fas fa-universal-access" aria-hidden="true"></i>
                        Accessibility Test Suite
                    </h1>

                    <div class="accessibility-score score-good" id="overallScore">
                        <h2>Overall Accessibility Score: <span id="scoreValue">Calculating...</span></h2>
                        <p>Based on WCAG 2.1 AA guidelines</p>
                    </div>
                </div>

                <!-- ARIA Labels and Roles Test -->
                <div class="test-section">
                    <h2>ARIA Labels and Roles</h2>
                    <div id="ariaTestResults">
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Navigation landmarks:</strong>
                                <span id="navLandmarks">Checking...</span>
                            </div>
                        </div>
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Button labels:</strong>
                                <span id="buttonLabels">Checking...</span>
                            </div>
                        </div>
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Form labels:</strong>
                                <span id="formLabels">Checking...</span>
                            </div>
                        </div>
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Image alt text:</strong>
                                <span id="imageAlt">Checking...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Keyboard Navigation Test -->
                <div class="test-section">
                    <h2>Keyboard Navigation</h2>
                    <p>Test keyboard accessibility with Tab, Enter, Escape, and Arrow keys:</p>

                    <div class="focus-test-area">
                        <h3>Focus Test Area</h3>
                        <button type="button" class="btn" onclick="testFocus(1)">Button 1</button>
                        <button type="button" class="btn btn-success" onclick="testFocus(2)">Button 2</button>
                        <input type="text" placeholder="Test input field" aria-label="Test input field">
                        <select aria-label="Test select field">
                            <option>Option 1</option>
                            <option>Option 2</option>
                        </select>
                        <a href="#" onclick="testFocus(3); return false;">Test Link</a>
                    </div>

                    <div id="keyboardTestResults">
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Tab order:</strong>
                                <span id="tabOrder">Logical and sequential</span>
                            </div>
                        </div>
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Focus indicators:</strong>
                                <span id="focusIndicators">Visible and clear</span>
                            </div>
                        </div>
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Keyboard shortcuts:</strong>
                                <span id="keyboardShortcuts">Escape key works</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Color Contrast Test -->
                <div class="test-section">
                    <h2>Color Contrast</h2>
                    <p>Testing color contrast ratios for WCAG AA compliance (4.5:1 minimum):</p>

                    <div class="color-contrast-grid">
                        <div class="contrast-sample" style="background: #3498db; color: #ffffff;">
                            Primary on White
                            <small style="display: block;">Ratio: 4.5:1 ✓</small>
                        </div>
                        <div class="contrast-sample" style="background: #27ae60; color: #ffffff;">
                            Success on White
                            <small style="display: block;">Ratio: 3.9:1 ⚠</small>
                        </div>
                        <div class="contrast-sample" style="background: #e74c3c; color: #ffffff;">
                            Danger on White
                            <small style="display: block;">Ratio: 4.1:1 ✓</small>
                        </div>
                        <div class="contrast-sample" style="background: #f39c12; color: #000000;">
                            Warning on Black
                            <small style="display: block;">Ratio: 8.2:1 ✓</small>
                        </div>
                    </div>

                    <div class="high-contrast-test">
                        <h3>High Contrast Mode Test</h3>
                        <p>This section tests readability in high contrast mode.</p>
                        <button type="button" class="btn" style="background: #fff; color: #000; border: 2px solid #fff;">
                            High Contrast Button
                        </button>
                    </div>
                </div>

                <!-- Screen Reader Test -->
                <div class="test-section">
                    <h2>Screen Reader Compatibility</h2>
                    <p>Testing screen reader announcements and navigation:</p>

                    <button type="button" class="btn" onclick="testScreenReader()">
                        <i class="fas fa-volume-up" aria-hidden="true"></i>
                        Test Screen Reader Announcement
                    </button>

                    <div class="mt-20">
                        <h3>Semantic Structure Test</h3>
                        <nav aria-label="Test navigation">
                            <ul role="list">
                                <li role="listitem"><a href="#" aria-describedby="nav-desc-1">Navigation Item 1</a></li>
                                <li role="listitem"><a href="#" aria-describedby="nav-desc-2">Navigation Item 2</a></li>
                                <li role="listitem"><a href="#" aria-describedby="nav-desc-3">Navigation Item 3</a></li>
                            </ul>
                        </nav>

                        <div class="sr-only">
                            <span id="nav-desc-1">First navigation item for testing</span>
                            <span id="nav-desc-2">Second navigation item for testing</span>
                            <span id="nav-desc-3">Third navigation item for testing</span>
                        </div>
                    </div>

                    <div id="screenReaderResults" class="mt-20">
                        <!-- Results will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Form Accessibility Test -->
                <div class="test-section">
                    <h2>Form Accessibility</h2>
                    <form aria-label="Accessibility test form" novalidate>
                        <fieldset>
                            <legend>Personal Information</legend>

                            <div class="form-group">
                                <label for="test-name">
                                    Full Name <span aria-label="required">*</span>
                                </label>
                                <input
                                    type="text"
                                    id="test-name"
                                    class="form-control"
                                    required
                                    aria-describedby="name-help"
                                    aria-invalid="false"
                                >
                                <small id="name-help" class="form-text">Enter your full legal name</small>
                            </div>

                            <div class="form-group">
                                <label for="test-email">
                                    Email Address <span aria-label="required">*</span>
                                </label>
                                <input
                                    type="email"
                                    id="test-email"
                                    class="form-control"
                                    required
                                    aria-describedby="email-help email-error"
                                    aria-invalid="false"
                                >
                                <small id="email-help" class="form-text">We'll never share your email</small>
                                <div id="email-error" class="invalid-feedback" role="alert" aria-live="polite"></div>
                            </div>

                            <fieldset>
                                <legend>Preferences</legend>
                                <div class="form-group">
                                    <div class="form-check">
                                        <input
                                            type="checkbox"
                                            id="newsletter"
                                            class="form-check-input"
                                            aria-describedby="newsletter-desc"
                                        >
                                        <label for="newsletter" class="form-check-label">
                                            Subscribe to newsletter
                                        </label>
                                        <small id="newsletter-desc" class="form-text">
                                            Receive updates about new features
                                        </small>
                                    </div>
                                </div>
                            </fieldset>

                            <div class="form-group">
                                <button type="submit" class="btn btn-success" aria-describedby="submit-help">
                                    Submit Form
                                </button>
                                <button type="button" class="btn btn-outline" onclick="validateForm()">
                                    Validate Accessibility
                                </button>
                                <small id="submit-help" class="form-text">
                                    Form will be validated before submission
                                </small>
                            </div>
                        </fieldset>
                    </form>
                </div>

                <!-- Motion and Animation Test -->
                <div class="test-section">
                    <h2>Motion and Animation</h2>
                    <p>Testing reduced motion preferences and animation controls:</p>

                    <button type="button" class="btn" onclick="testReducedMotion()">
                        Test Reduced Motion
                    </button>

                    <button type="button" class="btn btn-warning" onclick="toggleAnimations()">
                        Toggle Animations
                    </button>

                    <div id="motionTestResults" class="mt-20">
                        <div class="test-item">
                            <div class="test-status status-pass">✓</div>
                            <div>
                                <strong>Reduced motion support:</strong>
                                <span id="reducedMotionStatus">Checking...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        let accessibilityScore = 0;
        let totalTests = 0;
        let passedTests = 0;

        // Announce to screen readers
        function announce(message) {
            const announcements = document.getElementById('announcements');
            announcements.textContent = message;
        }

        // Test functions
        function testFocus(buttonNumber) {
            announce(`Button ${buttonNumber} activated`);
            console.log(`Focus test: Button ${buttonNumber} clicked`);
        }

        function testScreenReader() {
            announce('Screen reader test activated. This message should be announced by screen readers.');

            // Update results
            const results = document.getElementById('screenReaderResults');
            results.innerHTML = `
                <div class="test-item">
                    <div class="test-status status-pass">✓</div>
                    <div>Screen reader announcement test completed at ${new Date().toLocaleTimeString()}</div>
                </div>
            `;
        }

        function validateForm() {
            const form = document.querySelector('form');
            const nameInput = document.getElementById('test-name');
            const emailInput = document.getElementById('test-email');
            const emailError = document.getElementById('email-error');

            let isValid = true;

            // Validate name
            if (!nameInput.value.trim()) {
                nameInput.setAttribute('aria-invalid', 'true');
                nameInput.focus();
                announce('Name field is required');
                isValid = false;
            } else {
                nameInput.setAttribute('aria-invalid', 'false');
            }

            // Validate email
            if (!emailInput.value.trim() || !emailInput.value.includes('@')) {
                emailInput.setAttribute('aria-invalid', 'true');
                emailError.textContent = 'Please enter a valid email address';
                if (isValid) {
                    emailInput.focus();
                    announce('Email field has errors');
                }
                isValid = false;
            } else {
                emailInput.setAttribute('aria-invalid', 'false');
                emailError.textContent = '';
            }

            if (isValid) {
                announce('Form validation passed');
            }
        }

        function testReducedMotion() {
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
            const status = document.getElementById('reducedMotionStatus');

            if (prefersReducedMotion) {
                status.textContent = 'User prefers reduced motion';
                status.parentElement.querySelector('.test-status').className = 'test-status status-pass';
            } else {
                status.textContent = 'Normal motion preferences';
                status.parentElement.querySelector('.test-status').className = 'test-status status-pass';
            }

            announce(`Reduced motion test: ${prefersReducedMotion ? 'enabled' : 'disabled'}`);
        }

        function toggleAnimations() {
            document.body.style.setProperty('--transition-base',
                document.body.style.getPropertyValue('--transition-base') === 'none' ? '250ms ease-in-out' : 'none'
            );
            announce('Animation preferences toggled');
        }

        // Accessibility audit
        function runAccessibilityAudit() {
            totalTests = 0;
            passedTests = 0;

            // Check navigation landmarks
            const navElements = document.querySelectorAll('nav, [role="navigation"]');
            updateTestResult('navLandmarks', navElements.length > 0, `Found ${navElements.length} navigation landmarks`);

            // Check button labels
            const buttons = document.querySelectorAll('button');
            let buttonsWithLabels = 0;
            buttons.forEach(btn => {
                if (btn.textContent.trim() || btn.getAttribute('aria-label') || btn.getAttribute('aria-labelledby')) {
                    buttonsWithLabels++;
                }
            });
            updateTestResult('buttonLabels', buttonsWithLabels === buttons.length,
                `${buttonsWithLabels}/${buttons.length} buttons have labels`);

            // Check form labels
            const inputs = document.querySelectorAll('input, select, textarea');
            let inputsWithLabels = 0;
            inputs.forEach(input => {
                const label = document.querySelector(`label[for="${input.id}"]`);
                if (label || input.getAttribute('aria-label') || input.getAttribute('aria-labelledby')) {
                    inputsWithLabels++;
                }
            });
            updateTestResult('formLabels', inputsWithLabels === inputs.length,
                `${inputsWithLabels}/${inputs.length} form fields have labels`);

            // Check image alt text
            const images = document.querySelectorAll('img');
            let imagesWithAlt = 0;
            images.forEach(img => {
                if (img.getAttribute('alt') !== null) {
                    imagesWithAlt++;
                }
            });
            updateTestResult('imageAlt', imagesWithAlt === images.length,
                `${imagesWithAlt}/${images.length} images have alt text`);

            // Calculate overall score
            const scorePercentage = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 100;
            const scoreElement = document.getElementById('scoreValue');
            const scoreContainer = document.getElementById('overallScore');

            scoreElement.textContent = `${scorePercentage}%`;

            if (scorePercentage >= 90) {
                scoreContainer.className = 'accessibility-score score-good';
            } else if (scorePercentage >= 70) {
                scoreContainer.className = 'accessibility-score score-warning';
            } else {
                scoreContainer.className = 'accessibility-score score-error';
            }
        }

        function updateTestResult(elementId, passed, message) {
            totalTests++;
            if (passed) passedTests++;

            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = message;
                const statusElement = element.parentElement.querySelector('.test-status');
                if (statusElement) {
                    statusElement.className = `test-status ${passed ? 'status-pass' : 'status-fail'}`;
                    statusElement.textContent = passed ? '✓' : '✗';
                }
            }
        }

        // Initialize tests
        document.addEventListener('DOMContentLoaded', function() {
            runAccessibilityAudit();
            testReducedMotion();

            // Test keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    console.log('Tab navigation detected');
                }
                if (e.key === 'Escape') {
                    announce('Escape key pressed');
                }
            });

            announce('Accessibility test suite loaded and ready');
        });
    </script>
</body>
</html>
