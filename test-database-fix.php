<?php
/**
 * Test Database Fix
 * This script tests the database operations after fixing the parameter issue
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Database Fix - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔧 Database Fix Test</h1>
    <p>Testing the database operations after fixing the parameter mixing issue.</p>

    <?php
    try {
        require_once 'config/config.php';
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Test 1: Check if users table exists
        echo "<h2>Test 1: Check Users Table</h2>";
        $tables = $db->fetchAll("SHOW TABLES LIKE 'users'");
        if (empty($tables)) {
            echo "<div class='error'>❌ Users table doesn't exist. Please run setup.php first.</div>";
            echo "<p><a href='setup.php' class='btn'>Run Database Setup</a></p>";
        } else {
            echo "<div class='success'>✅ Users table exists</div>";
        }
        
        // Test 2: Try to create/update admin user
        echo "<h2>Test 2: Create/Update Admin User</h2>";
        
        $username = 'admin';
        $email = '<EMAIL>';
        $password = 'admin123';
        $fullName = 'Administrator';
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);
        
        // Check if admin user exists
        $existingUser = $db->fetchOne("SELECT id, username FROM users WHERE username = ?", [$username]);
        
        if ($existingUser) {
            echo "<div class='info'>Admin user exists, updating...</div>";
            
            // Test UPDATE operation
            $result = $db->update('users', [
                'email' => $email,
                'password_hash' => $passwordHash,
                'full_name' => $fullName,
                'role' => 'admin',
                'is_active' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ], 'username = ?', [$username]);
            
            echo "<div class='success'>✅ Admin user updated successfully</div>";
            
        } else {
            echo "<div class='info'>Admin user doesn't exist, creating...</div>";
            
            // Test INSERT operation
            $userId = $db->insert('users', [
                'username' => $username,
                'email' => $email,
                'password_hash' => $passwordHash,
                'full_name' => $fullName,
                'role' => 'admin',
                'is_active' => 1,
                'created_at' => date('Y-m-d H:i:s')
            ]);
            
            echo "<div class='success'>✅ Admin user created successfully (ID: $userId)</div>";
        }
        
        // Test 3: Verify the user can login
        echo "<h2>Test 3: Verify Login</h2>";
        
        $user = $db->fetchOne(
            "SELECT id, username, email, password_hash, full_name, role, is_active FROM users WHERE username = ? AND is_active = 1",
            [$username]
        );
        
        if ($user && password_verify($password, $user['password_hash'])) {
            echo "<div class='success'>✅ Login verification successful!</div>";
            echo "<div class='info'>Credentials that work:</div>";
            echo "<pre>Username: $username\nPassword: $password</pre>";
        } else {
            echo "<div class='error'>❌ Login verification failed</div>";
        }
        
        // Test 4: Show all users
        echo "<h2>Test 4: Current Users</h2>";
        $users = $db->fetchAll("SELECT id, username, email, full_name, role, is_active, created_at FROM users");
        
        if (empty($users)) {
            echo "<div class='error'>❌ No users found</div>";
        } else {
            echo "<div class='success'>✅ Found " . count($users) . " user(s):</div>";
            echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Role</th><th>Active</th></tr>";
            foreach ($users as $user) {
                $activeStatus = $user['is_active'] ? '✅ Yes' : '❌ No';
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['full_name']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td>$activeStatus</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Test 5: Test API authentication
        echo "<h2>Test 5: API Authentication Test</h2>";
        echo "<div class='info'>Testing the API endpoint directly...</div>";
        
        $testCredentials = [
            'username' => $username,
            'password' => $password
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://localhost/erdevwe/api/auth.php');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testCredentials));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<div class='info'><strong>HTTP Status Code:</strong> $httpCode</div>";
        
        if ($httpCode == 200) {
            $data = json_decode($response, true);
            if ($data && isset($data['success']) && $data['success']) {
                echo "<div class='success'>✅ API authentication successful!</div>";
                echo "<div class='success'>✅ Token generated successfully</div>";
                echo "<pre>API Response: " . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
            } else {
                echo "<div class='error'>❌ API authentication failed</div>";
                echo "<pre>Response: " . htmlspecialchars($response) . "</pre>";
            }
        } else {
            echo "<div class='error'>❌ API request failed (HTTP $httpCode)</div>";
            echo "<pre>Response: " . htmlspecialchars($response) . "</pre>";
        }
        
        echo "<h2>✅ Database Fix Test Complete</h2>";
        echo "<div class='success'>The database parameter mixing issue has been fixed!</div>";
        
        echo "<h3>Next Steps:</h3>";
        echo "<p>";
        echo "<a href='mobile-app/' class='btn'>🚀 Test Mobile App</a>";
        echo "<a href='admin/' class='btn'>🖥️ Admin Panel</a>";
        echo "<a href='debug-auth.php' class='btn'>🔍 Debug Tool</a>";
        echo "</p>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Error: " . $e->getMessage() . "</div>";
        echo "<div class='info'>Stack trace:</div>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    ?>

    <h2>What Was Fixed</h2>
    <div class="info">
        <p><strong>Problem:</strong> The database class was mixing named parameters (<code>:param</code>) and positional parameters (<code>?</code>) in the same query, which caused a PDO error.</p>
        
        <p><strong>Solution:</strong> Updated the database class to use only positional parameters (<code>?</code>) consistently in all methods:</p>
        <ul>
            <li><code>insert()</code> method - Now uses <code>?</code> placeholders</li>
            <li><code>update()</code> method - Now uses <code>?</code> placeholders</li>
            <li><code>delete()</code> method - Already used <code>?</code> placeholders</li>
        </ul>
        
        <p><strong>Result:</strong> All database operations should now work without parameter mixing errors.</p>
    </div>
</body>
</html>
