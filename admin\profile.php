<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    if ($action === 'update_profile') {
        $fullName = sanitize($_POST['full_name']);
        $email = sanitize($_POST['email']);

        // Validate input
        if (empty($fullName) || empty($email)) {
            $message = 'Please fill in all required fields';
            $messageType = 'error';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message = 'Please enter a valid email address';
            $messageType = 'error';
        } else {
            // Check if email is already taken by another user
            $existingUser = $db->fetchOne(
                "SELECT id FROM users WHERE email = ? AND id != ? AND is_active = 1",
                [$email, $user['id']]
            );

            if ($existingUser) {
                $message = 'This email address is already in use by another user';
                $messageType = 'error';
            } else {
                try {
                    $updateData = [
                        'full_name' => $fullName,
                        'email' => $email,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $db->update('users', $updateData, 'id = ?', [$user['id']]);

                    // Update session data
                    $user['full_name'] = $fullName;
                    $user['email'] = $email;

                    $message = 'Profile updated successfully!';
                    $messageType = 'success';

                } catch (Exception $e) {
                    $message = 'Error updating profile: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    } elseif ($action === 'change_password') {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Validate input
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            $message = 'Please fill in all password fields';
            $messageType = 'error';
        } elseif (strlen($newPassword) < 6) {
            $message = 'New password must be at least 6 characters long';
            $messageType = 'error';
        } elseif ($newPassword !== $confirmPassword) {
            $message = 'New passwords do not match';
            $messageType = 'error';
        } else {
            // Get current user with password hash
            $currentUser = $db->fetchOne(
                "SELECT password_hash FROM users WHERE id = ?",
                [$user['id']]
            );

            if (!password_verify($currentPassword, $currentUser['password_hash'])) {
                $message = 'Current password is incorrect';
                $messageType = 'error';
            } else {
                try {
                    $newPasswordHash = password_hash($newPassword, PASSWORD_DEFAULT);

                    $db->update('users',
                        ['password_hash' => $newPasswordHash, 'updated_at' => date('Y-m-d H:i:s')],
                        'id = ?',
                        [$user['id']]
                    );

                    $message = 'Password changed successfully!';
                    $messageType = 'success';

                } catch (Exception $e) {
                    $message = 'Error changing password: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <!-- Enhanced Profile Header -->
                <div class="profile-header">
                    <div class="profile-header-content">
                        <div class="profile-avatar-section">
                            <div class="profile-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="profile-info">
                                <h2 class="profile-name"><?= htmlspecialchars($user['full_name']) ?></h2>
                                <p class="profile-role"><?= ucfirst($user['role']) ?> Account</p>
                                <div class="profile-meta">
                                    <span class="meta-item">
                                        <i class="fas fa-envelope"></i>
                                        <?= htmlspecialchars($user['email']) ?>
                                    </span>
                                    <span class="meta-item">
                                        <i class="fas fa-user-tag"></i>
                                        @<?= htmlspecialchars($user['username']) ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="profile-status">
                            <div class="status-badge active">
                                <i class="fas fa-check-circle"></i>
                                <span>Active Account</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="profile-content">
                    <div class="profile-section">
                        <!-- Profile Information -->
                        <div class="form-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-user-edit"></i>
                                    Profile Information
                                </h3>
                                <p class="section-description">Update your personal information and contact details</p>
                            </div>
                            <div class="section-content">
                                <form method="POST" class="modern-profile-form">
                                    <input type="hidden" name="action" value="update_profile">

                                    <div class="form-grid">
                                        <div class="form-field">
                                            <label for="username" class="field-label">
                                                <i class="fas fa-at"></i>
                                                Username
                                            </label>
                                            <input type="text"
                                                   id="username"
                                                   class="form-input disabled"
                                                   value="<?= htmlspecialchars($user['username']) ?>"
                                                   disabled>
                                            <div class="field-help">Username cannot be changed for security reasons</div>
                                        </div>

                                        <div class="form-field">
                                            <label for="role" class="field-label">
                                                <i class="fas fa-user-shield"></i>
                                                Account Role
                                            </label>
                                            <input type="text"
                                                   id="role"
                                                   class="form-input disabled"
                                                   value="<?= ucfirst($user['role']) ?>"
                                                   disabled>
                                            <div class="field-help">Role can only be changed by an administrator</div>
                                        </div>

                                        <div class="form-field">
                                            <label for="full_name" class="field-label">
                                                <i class="fas fa-user"></i>
                                                Full Name *
                                            </label>
                                            <input type="text"
                                                   id="full_name"
                                                   name="full_name"
                                                   class="form-input"
                                                   value="<?= htmlspecialchars($user['full_name']) ?>"
                                                   placeholder="Enter your full name"
                                                   required>
                                            <div class="field-help">Your display name used throughout the system</div>
                                        </div>

                                        <div class="form-field">
                                            <label for="email" class="field-label">
                                                <i class="fas fa-envelope"></i>
                                                Email Address *
                                            </label>
                                            <input type="email"
                                                   id="email"
                                                   name="email"
                                                   class="form-input"
                                                   value="<?= htmlspecialchars($user['email']) ?>"
                                                   placeholder="Enter your email address"
                                                   required>
                                            <div class="field-help">Used for login and system notifications</div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-success btn-large">
                                            <i class="fas fa-save"></i>
                                            <span>Update Profile</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="profile-section">
                        <!-- Change Password -->
                        <div class="form-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-lock"></i>
                                    Security Settings
                                </h3>
                                <p class="section-description">Update your password to keep your account secure</p>
                            </div>
                            <div class="section-content">
                                <form method="POST" class="modern-password-form">
                                    <input type="hidden" name="action" value="change_password">

                                    <div class="form-grid">
                                        <div class="form-field full-width">
                                            <label for="current_password" class="field-label">
                                                <i class="fas fa-key"></i>
                                                Current Password *
                                            </label>
                                            <div class="password-input-group">
                                                <input type="password"
                                                       id="current_password"
                                                       name="current_password"
                                                       class="form-input password-input"
                                                       placeholder="Enter your current password"
                                                       required>
                                                <button type="button" class="password-toggle" data-target="current_password">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="field-help">Enter your current password to verify your identity</div>
                                        </div>

                                        <div class="form-field">
                                            <label for="new_password" class="field-label">
                                                <i class="fas fa-lock"></i>
                                                New Password *
                                            </label>
                                            <div class="password-input-group">
                                                <input type="password"
                                                       id="new_password"
                                                       name="new_password"
                                                       class="form-input password-input"
                                                       placeholder="Enter new password"
                                                       required
                                                       minlength="6">
                                                <button type="button" class="password-toggle" data-target="new_password">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="password-strength">
                                                <div class="strength-bar">
                                                    <div class="strength-fill"></div>
                                                </div>
                                                <span class="strength-text">Password strength</span>
                                            </div>
                                            <div class="field-help">Minimum 6 characters. Use a mix of letters, numbers, and symbols</div>
                                        </div>

                                        <div class="form-field">
                                            <label for="confirm_password" class="field-label">
                                                <i class="fas fa-check-double"></i>
                                                Confirm New Password *
                                            </label>
                                            <div class="password-input-group">
                                                <input type="password"
                                                       id="confirm_password"
                                                       name="confirm_password"
                                                       class="form-input password-input"
                                                       placeholder="Confirm new password"
                                                       required
                                                       minlength="6">
                                                <button type="button" class="password-toggle" data-target="confirm_password">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="password-match">
                                                <span class="match-indicator"></span>
                                            </div>
                                            <div class="field-help">Re-enter your new password to confirm</div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-warning btn-large">
                                            <i class="fas fa-shield-alt"></i>
                                            <span>Update Password</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="profile-section">
                        <!-- Account Information -->
                        <div class="form-section">
                            <div class="section-header">
                                <h3 class="section-title">
                                    <i class="fas fa-info-circle"></i>
                                    Account Information
                                </h3>
                                <p class="section-description">View your account details and activity history</p>
                            </div>
                            <div class="section-content">
                                <?php
                                // Get additional user info with timestamps
                                $userDetails = $db->fetchOne(
                                    "SELECT created_at, updated_at, last_login FROM users WHERE id = ?",
                                    [$user['id']]
                                );
                                ?>
                                <div class="account-info-grid">
                                    <div class="info-card">
                                        <div class="info-icon">
                                            <i class="fas fa-calendar-plus"></i>
                                        </div>
                                        <div class="info-content">
                                            <h4>Account Created</h4>
                                            <p><?= formatDate($userDetails['created_at'] ?? date('Y-m-d H:i:s')) ?></p>
                                        </div>
                                    </div>

                                    <div class="info-card">
                                        <div class="info-icon">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="info-content">
                                            <h4>Last Updated</h4>
                                            <p><?= $userDetails['updated_at'] ? formatDate($userDetails['updated_at']) : 'Never updated' ?></p>
                                        </div>
                                    </div>

                                    <div class="info-card">
                                        <div class="info-icon">
                                            <i class="fas fa-sign-in-alt"></i>
                                        </div>
                                        <div class="info-content">
                                            <h4>Last Login</h4>
                                            <p><?= $userDetails['last_login'] ? formatDate($userDetails['last_login']) : 'Never logged in' ?></p>
                                        </div>
                                    </div>

                                    <div class="info-card">
                                        <div class="info-icon">
                                            <i class="fas fa-shield-check"></i>
                                        </div>
                                        <div class="info-content">
                                            <h4>Account Status</h4>
                                            <p>
                                                <span class="status-badge active">
                                                    <i class="fas fa-check-circle"></i>
                                                    Active
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="account-summary">
                                    <div class="summary-header">
                                        <h4>
                                            <i class="fas fa-chart-line"></i>
                                            Account Summary
                                        </h4>
                                    </div>
                                    <div class="summary-content">
                                        <div class="summary-item">
                                            <span class="summary-label">Account Type:</span>
                                            <span class="summary-value"><?= ucfirst($user['role']) ?> Account</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Account ID:</span>
                                            <span class="summary-value">#<?= str_pad($user['id'], 6, '0', STR_PAD_LEFT) ?></span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Security Level:</span>
                                            <span class="summary-value">
                                                <span class="security-level high">
                                                    <i class="fas fa-shield-alt"></i>
                                                    High Security
                                                </span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password toggle functionality
            const passwordToggles = document.querySelectorAll('.password-toggle');
            passwordToggles.forEach(toggle => {
                toggle.addEventListener('click', function() {
                    const targetId = this.dataset.target;
                    const passwordInput = document.getElementById(targetId);
                    const icon = this.querySelector('i');

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            });

            // Password strength checker
            const newPassword = document.getElementById('new_password');
            const confirmPassword = document.getElementById('confirm_password');
            const strengthBar = document.querySelector('.strength-fill');
            const strengthText = document.querySelector('.strength-text');
            const matchIndicator = document.querySelector('.match-indicator');

            function checkPasswordStrength(password) {
                let strength = 0;
                let feedback = [];

                // Length check
                if (password.length >= 8) {
                    strength += 25;
                } else if (password.length >= 6) {
                    strength += 15;
                    feedback.push('Use at least 8 characters');
                } else {
                    feedback.push('Too short');
                }

                // Lowercase check
                if (/[a-z]/.test(password)) {
                    strength += 15;
                } else {
                    feedback.push('Add lowercase letters');
                }

                // Uppercase check
                if (/[A-Z]/.test(password)) {
                    strength += 15;
                } else {
                    feedback.push('Add uppercase letters');
                }

                // Number check
                if (/\d/.test(password)) {
                    strength += 15;
                } else {
                    feedback.push('Add numbers');
                }

                // Special character check
                if (/[^A-Za-z0-9]/.test(password)) {
                    strength += 30;
                } else {
                    feedback.push('Add special characters');
                }

                return { strength: Math.min(strength, 100), feedback };
            }

            function updatePasswordStrength() {
                if (!newPassword.value) {
                    strengthBar.style.width = '0%';
                    strengthBar.className = 'strength-fill';
                    strengthText.textContent = 'Password strength';
                    return;
                }

                const result = checkPasswordStrength(newPassword.value);
                const strength = result.strength;

                strengthBar.style.width = strength + '%';

                // Update strength bar color and text
                strengthBar.className = 'strength-fill';
                if (strength < 30) {
                    strengthBar.classList.add('weak');
                    strengthText.textContent = 'Weak password';
                } else if (strength < 60) {
                    strengthBar.classList.add('fair');
                    strengthText.textContent = 'Fair password';
                } else if (strength < 80) {
                    strengthBar.classList.add('good');
                    strengthText.textContent = 'Good password';
                } else {
                    strengthBar.classList.add('strong');
                    strengthText.textContent = 'Strong password';
                }
            }

            function updatePasswordMatch() {
                if (!confirmPassword.value) {
                    matchIndicator.textContent = '';
                    matchIndicator.className = 'match-indicator';
                    return;
                }

                if (newPassword.value === confirmPassword.value) {
                    matchIndicator.innerHTML = '<i class="fas fa-check"></i> Passwords match';
                    matchIndicator.className = 'match-indicator match';
                    confirmPassword.setCustomValidity('');
                } else {
                    matchIndicator.innerHTML = '<i class="fas fa-times"></i> Passwords do not match';
                    matchIndicator.className = 'match-indicator no-match';
                    confirmPassword.setCustomValidity('Passwords do not match');
                }
            }

            if (newPassword) {
                newPassword.addEventListener('input', function() {
                    updatePasswordStrength();
                    if (confirmPassword.value) {
                        updatePasswordMatch();
                    }
                });
            }

            if (confirmPassword) {
                confirmPassword.addEventListener('input', updatePasswordMatch);
            }

            // Enhanced form validation
            const profileForm = document.querySelector('.modern-profile-form');
            const passwordForm = document.querySelector('.modern-password-form');

            function validateForm(form) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('error');
                        isValid = false;
                    } else {
                        field.classList.remove('error');
                    }
                });

                return isValid;
            }

            if (profileForm) {
                profileForm.addEventListener('submit', function(e) {
                    if (!validateForm(this)) {
                        e.preventDefault();
                        const firstError = this.querySelector('.error');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            firstError.focus();
                        }
                    }
                });
            }

            if (passwordForm) {
                passwordForm.addEventListener('submit', function(e) {
                    if (!validateForm(this)) {
                        e.preventDefault();
                        const firstError = this.querySelector('.error');
                        if (firstError) {
                            firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            firstError.focus();
                        }
                    }
                });
            }

            // Real-time validation feedback
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.hasAttribute('required') && !this.value.trim()) {
                        this.classList.add('error');
                    } else {
                        this.classList.remove('error');
                    }
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('error') && this.value.trim()) {
                        this.classList.remove('error');
                    }
                });
            });
        });
    </script>
</body>
</html>
