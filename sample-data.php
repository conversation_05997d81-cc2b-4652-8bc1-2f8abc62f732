<?php
/**
 * Insert sample data for Flori Construction Ltd website
 * Run this script once to populate the database with sample content
 */

require_once 'config/config.php';

echo "Inserting sample data for Flori Construction Ltd...\n\n";

try {
    // Sample Services
    echo "Creating sample services...\n";
    
    $services = [
        [
            'title' => 'Civil Engineering',
            'slug' => 'civil-engineering',
            'short_description' => 'Comprehensive civil engineering solutions for residential and commercial projects, ensuring structural integrity and compliance with all regulations.',
            'description' => 'Our civil engineering services encompass the complete design, planning, and execution of construction projects. We provide structural analysis, foundation design, and ensure all work meets British Standards and building regulations. Our experienced team handles everything from initial site surveys to final inspections, delivering projects that stand the test of time.',
            'is_featured' => 1,
            'sort_order' => 1,
            'meta_title' => 'Civil Engineering Services London - Flori Construction Ltd',
            'meta_description' => 'Professional civil engineering services in London. Structural design, foundation work, and building compliance. Contact us for expert consultation.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'title' => 'Groundworks',
            'slug' => 'groundworks',
            'short_description' => 'Professional groundworks including excavation, foundations, drainage, and site preparation for all types of construction projects.',
            'description' => 'Our groundworks services form the foundation of every successful construction project. We handle site clearance, excavation, foundation laying, drainage systems, and utility connections. Using modern equipment and techniques, we ensure proper site preparation that meets all safety and quality standards.',
            'is_featured' => 1,
            'sort_order' => 2,
            'meta_title' => 'Groundworks Services London - Foundation & Excavation',
            'meta_description' => 'Expert groundworks services including excavation, foundations, and drainage. Professional site preparation in London and surrounding areas.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'title' => 'RC Frames',
            'slug' => 'rc-frames',
            'short_description' => 'Reinforced concrete frame construction for robust, durable structures that meet the highest engineering standards.',
            'description' => 'Reinforced concrete (RC) frames are the backbone of modern construction. Our team specializes in designing and constructing RC frame structures that provide exceptional strength and durability. We use high-quality materials and advanced construction techniques to deliver frames that exceed industry standards.',
            'is_featured' => 1,
            'sort_order' => 3,
            'meta_title' => 'RC Frame Construction London - Reinforced Concrete',
            'meta_description' => 'Professional RC frame construction services. Reinforced concrete structures built to the highest standards in London.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'title' => 'Basements',
            'slug' => 'basements',
            'short_description' => 'Basement construction and conversion services, from excavation to waterproofing and finishing.',
            'description' => 'Transform your property with professional basement construction or conversion. We handle the complete process including excavation, structural work, waterproofing, and finishing. Our basement solutions maximize your property\'s potential while ensuring compliance with building regulations.',
            'is_featured' => 1,
            'sort_order' => 4,
            'meta_title' => 'Basement Construction London - Conversion & Excavation',
            'meta_description' => 'Professional basement construction and conversion services in London. Complete excavation, waterproofing, and finishing solutions.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'title' => 'Hard Landscaping',
            'slug' => 'hard-landscaping',
            'short_description' => 'Professional hard landscaping services including driveways, patios, retaining walls, and outdoor construction.',
            'description' => 'Complete your project with our hard landscaping services. We create beautiful and functional outdoor spaces including driveways, patios, walkways, retaining walls, and garden features. Using quality materials and expert craftsmanship, we enhance your property\'s exterior appeal and value.',
            'is_featured' => 0,
            'sort_order' => 5,
            'meta_title' => 'Hard Landscaping London - Driveways, Patios & More',
            'meta_description' => 'Professional hard landscaping services in London. Driveways, patios, retaining walls, and outdoor construction by expert craftsmen.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ];
    
    foreach ($services as $service) {
        $db->insert('services', $service);
        echo "✓ Created service: {$service['title']}\n";
    }
    
    // Sample Projects
    echo "\nCreating sample projects...\n";
    
    $projects = [
        [
            'title' => 'Residential Extension - North London',
            'slug' => 'residential-extension-north-london',
            'description' => 'Complete two-story rear extension for a Victorian terraced house in North London. The project included structural alterations, new foundations, RC frame construction, and full interior finishing. The extension added 60 square meters of living space including a modern kitchen-dining area and master bedroom suite.',
            'location' => 'North London',
            'project_type' => 'completed',
            'start_date' => '2023-03-15',
            'completion_date' => '2023-08-30',
            'client_name' => 'Mr. & Mrs. Johnson',
            'is_featured' => 1,
            'sort_order' => 1,
            'meta_title' => 'Residential Extension Project North London - Case Study',
            'meta_description' => 'Successful residential extension project in North London. Two-story rear extension with modern design and quality construction.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'title' => 'Commercial Office Development',
            'slug' => 'commercial-office-development',
            'description' => 'New build commercial office development featuring modern RC frame construction, basement parking, and sustainable building technologies. The three-story building provides 1,200 square meters of premium office space with state-of-the-art facilities.',
            'location' => 'Central London',
            'project_type' => 'completed',
            'start_date' => '2022-09-01',
            'completion_date' => '2023-12-15',
            'client_name' => 'London Business Centre Ltd',
            'is_featured' => 1,
            'sort_order' => 2,
            'meta_title' => 'Commercial Office Development London - Construction Project',
            'meta_description' => 'Modern commercial office development in Central London. Three-story building with basement parking and premium facilities.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'title' => 'Luxury Basement Conversion',
            'slug' => 'luxury-basement-conversion',
            'description' => 'High-end basement conversion creating a luxury entertainment space with home cinema, wine cellar, and guest suite. The project involved complex excavation work, waterproofing, and premium finishing throughout.',
            'location' => 'Hampstead, London',
            'project_type' => 'ongoing',
            'start_date' => '2024-01-10',
            'completion_date' => null,
            'client_name' => 'Private Client',
            'is_featured' => 1,
            'sort_order' => 3,
            'meta_title' => 'Luxury Basement Conversion Hampstead - Ongoing Project',
            'meta_description' => 'Luxury basement conversion project in Hampstead. Creating premium entertainment space with cinema and wine cellar.',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ];
    
    foreach ($projects as $project) {
        $db->insert('projects', $project);
        echo "✓ Created project: {$project['title']}\n";
    }
    
    // Sample Content
    echo "\nCreating sample content...\n";
    
    $content = [
        [
            'section_key' => 'about_title',
            'title' => 'Our Story',
            'content' => 'Building Excellence Since 2009',
            'is_active' => 1,
            'sort_order' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'section_key' => 'about_intro',
            'title' => 'Company Introduction',
            'content' => 'Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.',
            'is_active' => 1,
            'sort_order' => 2,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'section_key' => 'about_description',
            'title' => 'About Description',
            'content' => 'Our team brings together many years of collective experience in the construction industry, embodying extensive knowledge and refined processes that ensure every project meets the highest standards of excellence.',
            'is_active' => 1,
            'sort_order' => 3,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'section_key' => 'about_mission',
            'title' => 'Our Mission',
            'content' => 'To deliver exceptional construction services that exceed client expectations while maintaining the highest standards of safety, quality, and professionalism in every project we undertake.',
            'is_active' => 1,
            'sort_order' => 4,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        [
            'section_key' => 'about_vision',
            'title' => 'Our Vision',
            'content' => 'To be London\'s leading construction company, recognized for innovation, reliability, and excellence in delivering transformative construction solutions that shape communities and enhance lives.',
            'is_active' => 1,
            'sort_order' => 5,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ];
    
    foreach ($content as $item) {
        $db->insert('content', $item);
        echo "✓ Created content: {$item['title']}\n";
    }
    
    // Sample Contact Inquiries
    echo "\nCreating sample contact inquiries...\n";
    
    $inquiries = [
        [
            'name' => 'Sarah Williams',
            'email' => '<EMAIL>',
            'phone' => '020 7123 4567',
            'subject' => 'Kitchen Extension Quote',
            'message' => 'Hi, I\'m interested in getting a quote for a single-story kitchen extension. The property is a 1930s semi-detached house in North London. Could you please provide an estimate?',
            'service_interest' => 'Residential Extension',
            'status' => 'new',
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
            'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ],
        [
            'name' => 'David Chen',
            'email' => '<EMAIL>',
            'phone' => '020 8987 6543',
            'subject' => 'Commercial Project Inquiry',
            'message' => 'We are planning a commercial office renovation and would like to discuss your services. The project involves structural modifications and modern fit-out work.',
            'service_interest' => 'Commercial Construction',
            'status' => 'replied',
            'ip_address' => '*************',
            'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'created_at' => date('Y-m-d H:i:s', strtotime('-5 days')),
            'updated_at' => date('Y-m-d H:i:s', strtotime('-4 days'))
        ]
    ];
    
    foreach ($inquiries as $inquiry) {
        $db->insert('contact_inquiries', $inquiry);
        echo "✓ Created inquiry from: {$inquiry['name']}\n";
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Sample data insertion complete!\n";
    echo "✓ Services: " . count($services) . " created\n";
    echo "✓ Projects: " . count($projects) . " created\n";
    echo "✓ Content: " . count($content) . " created\n";
    echo "✓ Inquiries: " . count($inquiries) . " created\n";
    echo "\n🎉 Your website now has sample content to work with!\n";
    echo "You can view the website and admin panel to see the sample data.\n";
    
} catch (Exception $e) {
    echo "❌ Error inserting sample data: " . $e->getMessage() . "\n";
    echo "Make sure the database is properly set up and tables exist.\n";
}
?>
