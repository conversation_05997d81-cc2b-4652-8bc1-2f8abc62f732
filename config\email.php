<?php
/**
 * Email Configuration and Functions
 * Flori Construction Ltd
 */

// Email sending function using PHP's mail() function
function sendEmail($to, $subject, $message, $fromEmail = null, $fromName = null) {
    $fromEmail = $fromEmail ?: SITE_EMAIL;
    $fromName = $fromName ?: SITE_NAME;
    
    // Email headers
    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: ' . $fromName . ' <' . $fromEmail . '>',
        'Reply-To: ' . $fromEmail,
        'X-Mailer: PHP/' . phpversion()
    ];
    
    $headerString = implode("\r\n", $headers);
    
    // Send email
    return mail($to, $subject, $message, $headerString);
}

// Send contact form notification
function sendContactNotification($contactData) {
    $subject = 'New Contact Form Submission - ' . SITE_NAME;
    
    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Contact Form Submission</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2c3e50; color: white; padding: 20px; text-align: center; }
            .content { background: #f8f9fa; padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #2c3e50; }
            .value { margin-top: 5px; }
            .footer { background: #e74c3c; color: white; padding: 15px; text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>New Contact Form Submission</h1>
                <p>You have received a new inquiry through your website</p>
            </div>
            
            <div class="content">
                <div class="field">
                    <div class="label">Name:</div>
                    <div class="value">' . htmlspecialchars($contactData['name']) . '</div>
                </div>
                
                <div class="field">
                    <div class="label">Email:</div>
                    <div class="value">' . htmlspecialchars($contactData['email']) . '</div>
                </div>
                
                <div class="field">
                    <div class="label">Phone:</div>
                    <div class="value">' . htmlspecialchars($contactData['phone'] ?: 'Not provided') . '</div>
                </div>
                
                <div class="field">
                    <div class="label">Subject:</div>
                    <div class="value">' . htmlspecialchars($contactData['subject'] ?: 'General Inquiry') . '</div>
                </div>
                
                ' . ($contactData['service_interest'] ? '
                <div class="field">
                    <div class="label">Service Interest:</div>
                    <div class="value">' . htmlspecialchars($contactData['service_interest']) . '</div>
                </div>
                ' : '') . '
                
                ' . ($contactData['project_reference'] ? '
                <div class="field">
                    <div class="label">Project Reference:</div>
                    <div class="value">' . htmlspecialchars($contactData['project_reference']) . '</div>
                </div>
                ' : '') . '
                
                <div class="field">
                    <div class="label">Message:</div>
                    <div class="value">' . nl2br(htmlspecialchars($contactData['message'])) . '</div>
                </div>
                
                <div class="field">
                    <div class="label">Submitted:</div>
                    <div class="value">' . date('F j, Y \a\t g:i A') . '</div>
                </div>
                
                <div class="field">
                    <div class="label">IP Address:</div>
                    <div class="value">' . htmlspecialchars($contactData['ip_address']) . '</div>
                </div>
            </div>
            
            <div class="footer">
                <p><strong>' . SITE_NAME . '</strong></p>
                <p>' . SITE_PHONE . ' | ' . SITE_EMAIL . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail(SITE_EMAIL, $subject, $message);
}

// Send auto-reply to contact form submitter
function sendContactAutoReply($contactData) {
    $subject = 'Thank you for contacting ' . SITE_NAME;
    
    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Thank you for your inquiry</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #e74c3c; color: white; padding: 20px; text-align: center; }
            .content { background: #f8f9fa; padding: 20px; }
            .footer { background: #2c3e50; color: white; padding: 15px; text-align: center; }
            .contact-info { margin-top: 20px; }
            .contact-item { margin-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Thank You for Your Inquiry</h1>
                <p>We have received your message and will respond within 24 hours</p>
            </div>
            
            <div class="content">
                <p>Dear ' . htmlspecialchars($contactData['name']) . ',</p>
                
                <p>Thank you for contacting <strong>' . SITE_NAME . '</strong>. We have received your inquiry and one of our team members will get back to you within 24 hours during business hours.</p>
                
                <p><strong>Your message summary:</strong></p>
                <div style="background: white; padding: 15px; border-left: 4px solid #e74c3c; margin: 15px 0;">
                    <strong>Subject:</strong> ' . htmlspecialchars($contactData['subject'] ?: 'General Inquiry') . '<br>
                    <strong>Message:</strong> ' . nl2br(htmlspecialchars(substr($contactData['message'], 0, 200))) . (strlen($contactData['message']) > 200 ? '...' : '') . '
                </div>
                
                <p>If you need immediate assistance, please don\'t hesitate to call us directly:</p>
                
                <div class="contact-info">
                    <div class="contact-item"><strong>Phone:</strong> ' . SITE_PHONE . '</div>
                    <div class="contact-item"><strong>Mobile:</strong> ' . SITE_MOBILE . '</div>
                    <div class="contact-item"><strong>Email:</strong> ' . SITE_EMAIL . '</div>
                    <div class="contact-item"><strong>Address:</strong> ' . SITE_ADDRESS . '</div>
                </div>
                
                <p><strong>Business Hours:</strong></p>
                <ul>
                    <li>Monday - Friday: 8:00 AM - 6:00 PM</li>
                    <li>Saturday: 9:00 AM - 4:00 PM</li>
                    <li>Sunday: Emergency Only</li>
                </ul>
                
                <p>Thank you for considering ' . SITE_NAME . ' for your construction needs.</p>
                
                <p>Best regards,<br>
                The ' . SITE_NAME . ' Team</p>
            </div>
            
            <div class="footer">
                <p><strong>' . SITE_NAME . '</strong></p>
                <p>' . SITE_PHONE . ' | ' . SITE_EMAIL . '</p>
                <p>' . SITE_ADDRESS . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($contactData['email'], $subject, $message);
}

// Test email function
function testEmail($testEmail) {
    $subject = 'Email Test - ' . SITE_NAME;
    
    $message = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Email Test</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { background: #f8f9fa; padding: 20px; }
            .footer { background: #2c3e50; color: white; padding: 15px; text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>✅ Email Test Successful</h1>
                <p>Your email configuration is working correctly</p>
            </div>
            
            <div class="content">
                <p>Congratulations! This test email confirms that your email system is properly configured and working.</p>
                
                <p><strong>Test Details:</strong></p>
                <ul>
                    <li>Sent from: ' . SITE_NAME . '</li>
                    <li>Sent to: ' . htmlspecialchars($testEmail) . '</li>
                    <li>Date/Time: ' . date('F j, Y \a\t g:i A T') . '</li>
                    <li>Server: ' . $_SERVER['SERVER_NAME'] . '</li>
                </ul>
                
                <p>Your contact forms and email notifications should now work properly.</p>
            </div>
            
            <div class="footer">
                <p><strong>' . SITE_NAME . '</strong></p>
                <p>' . SITE_PHONE . ' | ' . SITE_EMAIL . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return sendEmail($testEmail, $subject, $message);
}

// Get email settings
function getEmailSettings() {
    global $db;
    
    $settings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'email_settings'");
    
    if ($settings) {
        return json_decode($settings['setting_value'], true);
    }
    
    return [
        'smtp_enabled' => false,
        'smtp_host' => SMTP_HOST,
        'smtp_port' => SMTP_PORT,
        'smtp_username' => SMTP_USERNAME,
        'smtp_password' => SMTP_PASSWORD,
        'from_email' => SMTP_FROM_EMAIL,
        'from_name' => SMTP_FROM_NAME,
        'auto_reply_enabled' => true,
        'notification_email' => SITE_EMAIL
    ];
}

// Save email settings
function saveEmailSettings($settings) {
    global $db;
    
    $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'email_settings'");
    
    if ($existing) {
        return $db->update('site_settings', 
            ['setting_value' => json_encode($settings), 'updated_at' => date('Y-m-d H:i:s')], 
            'setting_key = ?', 
            ['email_settings']
        );
    } else {
        return $db->insert('site_settings', [
            'setting_key' => 'email_settings',
            'setting_value' => json_encode($settings),
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
?>
