/**
 * Sync Manager for Flori Construction Mobile App
 * Handles offline data synchronization and conflict resolution
 */

class SyncManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.syncQueue = [];
        this.lastSyncTime = localStorage.getItem('last_sync_time') || null;
        this.syncInProgress = false;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadSyncQueue();
        
        // Auto sync when coming online
        if (this.isOnline) {
            this.performSync();
        }
    }
    
    setupEventListeners() {
        // Online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.updateConnectionStatus();
            this.performSync();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.updateConnectionStatus();
        });
        
        // Periodic sync when online
        setInterval(() => {
            if (this.isOnline && !this.syncInProgress) {
                this.performSync();
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }
    
    updateConnectionStatus() {
        const statusIndicator = document.getElementById('connection-status');
        if (statusIndicator) {
            statusIndicator.className = `connection-status ${this.isOnline ? 'online' : 'offline'}`;
            statusIndicator.innerHTML = `
                <i class="fas fa-${this.isOnline ? 'wifi' : 'wifi-slash'}"></i>
                <span>${this.isOnline ? 'Online' : 'Offline'}</span>
            `;
        }
        
        // Show toast for connection changes
        if (this.isOnline) {
            window.floriAdmin?.showToast('Connection restored', 'success');
        } else {
            window.floriAdmin?.showToast('Working offline', 'warning');
        }
    }
    
    async performSync() {
        if (this.syncInProgress || !this.isOnline) {
            return;
        }
        
        this.syncInProgress = true;
        
        try {
            // Sync pending changes first
            await this.syncPendingChanges();
            
            // Then pull latest data
            await this.pullLatestData();
            
            // Update last sync time
            this.lastSyncTime = new Date().toISOString();
            localStorage.setItem('last_sync_time', this.lastSyncTime);
            
            // Clear sync queue
            this.syncQueue = [];
            this.saveSyncQueue();
            
            console.log('Sync completed successfully');
            
        } catch (error) {
            console.error('Sync failed:', error);
            window.floriAdmin?.showToast('Sync failed', 'error');
        } finally {
            this.syncInProgress = false;
        }
    }
    
    async syncPendingChanges() {
        if (this.syncQueue.length === 0) {
            return;
        }
        
        const results = [];
        
        for (const item of this.syncQueue) {
            try {
                const result = await this.syncItem(item);
                results.push({ item, result, success: true });
            } catch (error) {
                console.error('Failed to sync item:', item, error);
                results.push({ item, error, success: false });
            }
        }
        
        // Remove successfully synced items
        this.syncQueue = this.syncQueue.filter((item, index) => {
            return !results[index].success;
        });
        
        this.saveSyncQueue();
        
        return results;
    }
    
    async syncItem(item) {
        const { type, action, data, timestamp } = item;
        
        switch (type) {
            case 'project':
                return await this.syncProject(action, data);
            case 'media':
                return await this.syncMedia(action, data);
            case 'content':
                return await this.syncContent(action, data);
            default:
                throw new Error(`Unknown sync type: ${type}`);
        }
    }
    
    async syncProject(action, data) {
        const endpoint = 'mobile.php?action=project';
        
        switch (action) {
            case 'create':
                return await window.floriAdmin.apiRequest(endpoint, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
            case 'update':
                return await window.floriAdmin.apiRequest(endpoint, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            case 'delete':
                return await window.floriAdmin.apiRequest(`${endpoint}&id=${data.id}`, {
                    method: 'DELETE'
                });
            default:
                throw new Error(`Unknown project action: ${action}`);
        }
    }
    
    async syncMedia(action, data) {
        const endpoint = 'mobile.php?action=media';
        
        switch (action) {
            case 'upload':
                // For file uploads, we need to handle FormData
                const formData = new FormData();
                formData.append('file', data.file);
                formData.append('alt_text', data.alt_text || '');
                formData.append('caption', data.caption || '');
                
                return await fetch(`${window.floriAdmin.apiBase}/mobile.php?action=upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${window.floriAdmin.token}`
                    },
                    body: formData
                });
            case 'update':
                return await window.floriAdmin.apiRequest(endpoint, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            case 'delete':
                return await window.floriAdmin.apiRequest(`${endpoint}&id=${data.id}`, {
                    method: 'DELETE'
                });
            default:
                throw new Error(`Unknown media action: ${action}`);
        }
    }
    
    async syncContent(action, data) {
        const endpoint = 'mobile.php?action=content';
        
        switch (action) {
            case 'update':
                return await window.floriAdmin.apiRequest(endpoint, {
                    method: 'PUT',
                    body: JSON.stringify(data)
                });
            default:
                throw new Error(`Unknown content action: ${action}`);
        }
    }
    
    async pullLatestData() {
        try {
            // Get latest data from server
            const data = await window.floriAdmin.apiRequest('mobile.php?action=sync');
            
            if (data && data.success) {
                // Store data in offline storage
                await this.storeOfflineData(data.data);
                
                // Update UI if needed
                this.updateUIWithLatestData(data.data);
            }
        } catch (error) {
            console.error('Failed to pull latest data:', error);
            throw error;
        }
    }
    
    async storeOfflineData(data) {
        if (!('indexedDB' in window)) {
            console.warn('IndexedDB not supported');
            return;
        }
        
        try {
            const db = await this.openDatabase();
            
            // Store projects
            if (data.projects) {
                await this.storeInDB(db, 'projects', data.projects);
            }
            
            // Store media
            if (data.media) {
                await this.storeInDB(db, 'media', data.media);
            }
            
            // Store content
            if (data.content) {
                await this.storeInDB(db, 'content', data.content);
            }
            
        } catch (error) {
            console.error('Failed to store offline data:', error);
        }
    }
    
    async openDatabase() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('FloriAdminDB', 1);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                
                // Create object stores
                if (!db.objectStoreNames.contains('projects')) {
                    db.createObjectStore('projects', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('media')) {
                    db.createObjectStore('media', { keyPath: 'id' });
                }
                
                if (!db.objectStoreNames.contains('content')) {
                    db.createObjectStore('content', { keyPath: 'key' });
                }
                
                if (!db.objectStoreNames.contains('sync_queue')) {
                    db.createObjectStore('sync_queue', { keyPath: 'id', autoIncrement: true });
                }
            };
        });
    }
    
    async storeInDB(db, storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            
            transaction.oncomplete = () => resolve();
            transaction.onerror = () => reject(transaction.error);
            
            // Clear existing data
            store.clear();
            
            // Add new data
            if (Array.isArray(data)) {
                data.forEach(item => store.add(item));
            } else {
                store.add(data);
            }
        });
    }
    
    updateUIWithLatestData(data) {
        // Refresh current page if needed
        const currentPage = window.floriAdmin?.currentPage;
        
        if (currentPage === 'dashboard') {
            window.floriAdmin.loadDashboard();
        } else if (currentPage === 'projects' && window.ProjectsManager) {
            window.ProjectsManager.load();
        } else if (currentPage === 'media' && window.MediaManager) {
            window.MediaManager.load();
        } else if (currentPage === 'content' && window.ContentManager) {
            window.ContentManager.load();
        }
    }
    
    addToSyncQueue(type, action, data) {
        const item = {
            id: Date.now() + Math.random(),
            type,
            action,
            data,
            timestamp: new Date().toISOString()
        };
        
        this.syncQueue.push(item);
        this.saveSyncQueue();
        
        // Show offline indicator
        if (!this.isOnline) {
            window.floriAdmin?.showToast('Changes saved offline', 'info');
        }
        
        return item.id;
    }
    
    loadSyncQueue() {
        try {
            const stored = localStorage.getItem('sync_queue');
            this.syncQueue = stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('Failed to load sync queue:', error);
            this.syncQueue = [];
        }
    }
    
    saveSyncQueue() {
        try {
            localStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
        } catch (error) {
            console.error('Failed to save sync queue:', error);
        }
    }
    
    getSyncStatus() {
        return {
            isOnline: this.isOnline,
            syncInProgress: this.syncInProgress,
            pendingItems: this.syncQueue.length,
            lastSyncTime: this.lastSyncTime
        };
    }
    
    async getOfflineData(type) {
        if (!('indexedDB' in window)) {
            return null;
        }
        
        try {
            const db = await this.openDatabase();
            
            return new Promise((resolve, reject) => {
                const transaction = db.transaction([type], 'readonly');
                const store = transaction.objectStore(type);
                const request = store.getAll();
                
                request.onsuccess = () => resolve(request.result);
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error('Failed to get offline data:', error);
            return null;
        }
    }
}

// Initialize Sync Manager
window.SyncManager = new SyncManager();
