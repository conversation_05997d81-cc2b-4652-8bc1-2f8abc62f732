<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    try {
        requireCSRF();
    } catch (Exception $e) {
        $message = 'Security validation failed. Please try again.';
        $messageType = 'error';
    }

    $action = $_POST['action'] ?? '';

    if ($action === 'update_colors' && empty($message)) {
        $primaryColor = sanitize($_POST['primary_color']);
        $secondaryColor = sanitize($_POST['secondary_color']);
        $accentColor = sanitize($_POST['accent_color']);
        $textColor = sanitize($_POST['text_color']);
        $backgroundColor = sanitize($_POST['background_color']);
        $headerBgColor = sanitize($_POST['header_bg_color']);
        $headerTextColor = sanitize($_POST['header_text_color']);
        $buttonColor = sanitize($_POST['button_color']);
        $buttonHoverColor = sanitize($_POST['button_hover_color']);
        $footerBgColor = sanitize($_POST['footer_bg_color']);
        $footerTextColor = sanitize($_POST['footer_text_color']);
        $linkColor = sanitize($_POST['link_color']);
        $linkHoverColor = sanitize($_POST['link_hover_color']);

        try {
            // Update or insert branding settings
            $settings = [
                'primary_color' => $primaryColor,
                'secondary_color' => $secondaryColor,
                'accent_color' => $accentColor,
                'text_color' => $textColor,
                'background_color' => $backgroundColor,
                'header_bg_color' => $headerBgColor,
                'header_text_color' => $headerTextColor,
                'button_color' => $buttonColor,
                'button_hover_color' => $buttonHoverColor,
                'footer_bg_color' => $footerBgColor,
                'footer_text_color' => $footerTextColor,
                'link_color' => $linkColor,
                'link_hover_color' => $linkHoverColor,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Check if branding settings exist
            $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'branding_colors'");

            if ($existing) {
                $db->update('site_settings',
                    ['setting_value' => json_encode($settings), 'updated_at' => date('Y-m-d H:i:s')],
                    'setting_key = ?',
                    ['branding_colors']
                );
            } else {
                $db->insert('site_settings', [
                    'setting_key' => 'branding_colors',
                    'setting_value' => json_encode($settings),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $message = 'Branding colors updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating branding: ' . $e->getMessage();
            $messageType = 'error';
        }
    }

    if ($action === 'update_fonts' && empty($message)) {
        $headingFont = sanitize($_POST['heading_font']);
        $bodyFont = sanitize($_POST['body_font']);

        try {
            $fontSettings = [
                'heading_font' => $headingFont,
                'body_font' => $bodyFont,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'branding_fonts'");

            if ($existing) {
                $db->update('site_settings',
                    ['setting_value' => json_encode($fontSettings), 'updated_at' => date('Y-m-d H:i:s')],
                    'setting_key = ?',
                    ['branding_fonts']
                );
            } else {
                $db->insert('site_settings', [
                    'setting_key' => 'branding_fonts',
                    'setting_value' => json_encode($fontSettings),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $message = 'Font settings updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating fonts: ' . $e->getMessage();
            $messageType = 'error';
        }
    }

    if ($action === 'upload_logo') {

        if (!isset($_FILES['logo_file'])) {
            $message = 'No file was selected for upload';
            $messageType = 'error';
        } else {
            $file = $_FILES['logo_file'];

            // Check for upload errors
            if ($file['error'] !== UPLOAD_ERR_OK) {
                $uploadErrors = [
                    UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize directive',
                    UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE directive',
                    UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                    UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                    UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                    UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                    UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
                ];

                $errorMsg = isset($uploadErrors[$file['error']])
                    ? $uploadErrors[$file['error']]
                    : 'Unknown upload error (code: ' . $file['error'] . ')';

                $message = 'Upload failed: ' . $errorMsg;
                $messageType = 'error';
            } else {
                try {
                    // Additional validation
                    $fileInfo = pathinfo($file['name']);
                    $extension = strtolower($fileInfo['extension'] ?? '');

                    if (!in_array($extension, ALLOWED_IMAGE_TYPES)) {
                        throw new Exception('Invalid file type. Allowed types: ' . implode(', ', ALLOWED_IMAGE_TYPES));
                    }

                    if ($file['size'] > MAX_FILE_SIZE) {
                        throw new Exception('File too large. Maximum size: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB');
                    }

                    if ($file['size'] == 0) {
                        throw new Exception('File is empty');
                    }

                    // Verify it's actually an image (SVG files need special handling)
                    if ($extension === 'svg') {
                        // SVG validation is handled in uploadFile function
                        // Just check if it's a valid SVG by looking for SVG tag
                        $content = file_get_contents($file['tmp_name']);
                        if (!$content || !preg_match('/<svg[^>]*>/i', $content)) {
                            throw new Exception('File is not a valid SVG');
                        }
                    } else {
                        // Use getimagesize for raster images
                        $imageInfo = getimagesize($file['tmp_name']);
                        if (!$imageInfo) {
                            throw new Exception('File is not a valid image');
                        }
                    }

                    $uploadResult = uploadFile($file, 'general');

                    $logoSettings = [
                        'logo_path' => $uploadResult['file_path'],
                        'logo_filename' => $uploadResult['filename'],
                        'original_name' => $uploadResult['original_name'],
                        'file_size' => $uploadResult['file_size'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'branding_logo'");

                    if ($existing) {
                        $db->update('site_settings',
                            ['setting_value' => json_encode($logoSettings), 'updated_at' => date('Y-m-d H:i:s')],
                            'setting_key = ?',
                            ['branding_logo']
                        );
                    } else {
                        $db->insert('site_settings', [
                            'setting_key' => 'branding_logo',
                            'setting_value' => json_encode($logoSettings),
                            'created_at' => date('Y-m-d H:i:s')
                        ]);
                    }

                    $message = 'Logo uploaded successfully! File: ' . $uploadResult['original_name'];
                    $messageType = 'success';

                } catch (Exception $e) {
                    $message = 'Error uploading logo: ' . $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    }
}

// Get current branding settings
$colorSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_colors'");
$fontSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_fonts'");
$logoSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_logo'");

// Default color values
$defaultColors = [
    'primary_color' => '#e74c3c',
    'secondary_color' => '#2c3e50',
    'accent_color' => '#f39c12',
    'text_color' => '#333333',
    'background_color' => '#ffffff',
    'header_bg_color' => '#ffffff',
    'header_text_color' => '#1a1a1a',
    'button_color' => '#1a1a1a',
    'button_hover_color' => '#d4a574',
    'footer_bg_color' => '#0a0a0a',
    'footer_text_color' => '#ffffff',
    'link_color' => '#d4a574',
    'link_hover_color' => '#f4d03f'
];

// Parse colors from database with fallback to defaults
$colors = $defaultColors;
if ($colorSettings) {
    $parsedColors = json_decode($colorSettings['setting_value'], true);
    if (is_array($parsedColors)) {
        // Merge with defaults to ensure all keys exist
        $colors = array_merge($defaultColors, $parsedColors);
    }
}

// Default font values
$defaultFonts = [
    'heading_font' => 'Oswald',
    'body_font' => 'Roboto'
];

// Parse fonts from database with fallback to defaults
$fonts = $defaultFonts;
if ($fontSettings) {
    $parsedFonts = json_decode($fontSettings['setting_value'], true);
    if (is_array($parsedFonts)) {
        // Merge with defaults to ensure all keys exist
        $fonts = array_merge($defaultFonts, $parsedFonts);
    }
}

$logo = $logoSettings ? json_decode($logoSettings['setting_value'], true) : null;

// Helper function to safely get color values
function getColor($colors, $key, $default = '#000000') {
    return isset($colors[$key]) && !empty($colors[$key]) ? htmlspecialchars($colors[$key]) : $default;
}

// Helper function to safely get font values
function getFont($fonts, $key, $default = 'Arial') {
    return isset($fonts[$key]) && !empty($fonts[$key]) ? htmlspecialchars($fonts[$key]) : $default;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branding Settings - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Enhanced Branding Page Styles */
        .branding-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .branding-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .branding-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .branding-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .branding-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .branding-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .branding-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .branding-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .branding-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        .branding-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            margin: -20px -20px 40px -20px;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
            text-align: center;
        }

        .page-header p {
            text-align: center;
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .section-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .enhanced-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            border: 1px solid #e8ecf4;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .enhanced-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        }

        .enhanced-card-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px 30px;
            border-bottom: 1px solid #e8ecf4;
        }

        .enhanced-card-header h3 {
            margin: 0;
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .enhanced-card-header i {
            font-size: 1.2rem;
            color: #667eea;
        }

        .enhanced-card-content {
            padding: 30px;
        }

        .color-section {
            margin-bottom: 30px;
        }

        .color-input-group {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .color-input-group:hover {
            background: #f1f3f4;
            border-color: #667eea;
        }

        .color-input-group label {
            min-width: 120px;
            font-weight: 500;
            color: #495057;
            margin: 0;
        }

        .color-input-group small {
            font-size: 11px;
            color: #6c757d;
            margin-top: 5px;
            display: block;
            font-style: italic;
        }

        /* Color Scheme Tabs */
        .color-tabs {
            display: flex;
            gap: 2px;
            margin-bottom: 30px;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 4px;
            border: 1px solid #e9ecef;
        }

        .color-tab {
            flex: 1;
            padding: 12px 20px;
            background: transparent;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .color-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .color-tab:hover:not(.active) {
            color: #495057;
            background: rgba(255,255,255,0.5);
        }

        /* Color Tab Content */
        .color-tab-content {
            display: none;
        }

        .color-tab-content.active {
            display: block;
        }

        .color-tab-content h4 {
            margin-bottom: 25px;
            color: #2c3e50;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .color-tab-content h4 i {
            color: #667eea;
        }

        /* Color Grid Layout */
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        /* Color Actions */
        .color-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            align-items: center;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
            margin-top: 30px;
        }

        /* Color Presets and Palettes Styles */
        .color-presets-section {
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .color-presets-section h4 {
            margin-bottom: 25px;
            color: #2c3e50;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .preset-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .preset-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .preset-tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .preset-tab.active {
            background: white;
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .preset-tab:hover:not(.active) {
            background: rgba(255,255,255,0.7);
            color: #495057;
        }

        .preset-content {
            display: none;
            padding: 25px;
        }

        .preset-content.active {
            display: block;
        }

        /* Quick Presets Grid */
        .preset-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .preset-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .preset-item:hover {
            background: white;
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .preset-colors {
            display: flex;
            justify-content: center;
            gap: 5px;
            margin-bottom: 15px;
        }

        .preset-colors span {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .preset-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        /* Color Palettes Grid */
        .palette-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }

        .palette-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .palette-item:hover {
            background: white;
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .palette-colors {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .palette-colors span {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            border: 2px solid white;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .palette-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 16px;
            display: block;
            margin-bottom: 8px;
        }

        .palette-item small {
            color: #6c757d;
            font-size: 12px;
            font-style: italic;
        }

        /* Custom Palette Generator */
        .custom-palette-generator {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
        }

        .generator-controls {
            display: flex;
            gap: 20px;
            align-items: end;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .control-group label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .generated-palette {
            background: white;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .palette-preview {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .generated-color {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            border: 3px solid white;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .generated-color:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        /* Responsive Design for Color Scheme */
        @media (max-width: 768px) {
            .color-tabs {
                flex-direction: column;
                gap: 5px;
            }

            .color-tab {
                padding: 10px 15px;
                font-size: 13px;
            }

            .color-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .color-input-group {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .color-input-group label {
                min-width: auto;
                margin-bottom: 5px;
            }

            .color-input {
                width: 100%;
                height: 45px;
            }

            .color-preview {
                width: 100%;
                text-align: center;
            }

            .color-actions {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .color-actions .btn-enhanced,
            .color-actions .btn-outline-enhanced {
                width: 100%;
                justify-content: center;
            }

            .preset-tabs {
                flex-direction: column;
            }

            .preset-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .palette-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .generator-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }

            .palette-preview {
                flex-wrap: wrap;
                gap: 8px;
            }

            .generated-color {
                width: 40px;
                height: 40px;
            }
        }

        @media (max-width: 480px) {
            .enhanced-card {
                margin: 10px 0;
                padding: 15px;
            }

            .enhanced-card-header h3 {
                font-size: 1.1rem;
            }

            .color-tab-content h4 {
                font-size: 1rem;
                margin-bottom: 20px;
            }

            .color-input-group small {
                font-size: 10px;
            }
        }

        .color-input {
            width: 70px;
            height: 50px;
            border: 3px solid #fff;
            border-radius: 12px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .color-input:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .color-preview {
            flex: 1;
            height: 50px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 13px;
            font-weight: 500;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .font-section {
            margin-bottom: 25px;
        }

        .font-preview {
            padding: 25px;
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            margin-top: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            transition: all 0.3s ease;
        }

        .font-preview:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f1f3f4 0%, #ffffff 100%);
        }

        .font-preview h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }

        .font-preview p {
            margin: 0;
            color: #6c757d;
            line-height: 1.6;
        }

        .logo-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .logo-upload-area {
            border: 2px dashed #e9ecef;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            transition: all 0.3s ease;
        }

        .logo-upload-area:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f1f3f4 0%, #ffffff 100%);
        }

        .logo-preview-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .logo-preview {
            max-width: 200px;
            max-height: 120px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            background: white;
            padding: 15px;
        }

        .preview-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            overflow: hidden;
            margin-top: 40px;
        }

        .preview-header {
            padding: 25px 30px;
            background: rgba(255,255,255,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .preview-content {
            padding: 30px;
        }

        .preview-demo {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }

        .btn-enhanced {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-enhanced:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-outline-enhanced {
            background: transparent;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 10px 23px;
            color: #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-outline-enhanced:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }

        .form-control-enhanced {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control-enhanced:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .alert-enhanced {
            border: none;
            border-radius: 12px;
            padding: 20px 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .alert-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        @media (max-width: 768px) {
            .section-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .logo-section {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .enhanced-card-content {
                padding: 20px;
            }

            .page-header {
                padding: 30px 20px;
            }

            .page-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Enhanced Branding Header -->
                <div class="branding-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-palette"></i>
                                Branding Settings
                            </h1>
                            <p class="page-description">Customize your website's visual identity with colors, fonts, and logo</p>
                        </div>
                        <div class="header-actions">
                            <a href="#" class="btn btn-outline btn-lg" onclick="resetToDefaults()">
                                <i class="fas fa-undo"></i>
                                <span>Reset to Defaults</span>
                            </a>
                            <a href="#" class="btn btn-primary btn-lg" onclick="previewChanges()">
                                <i class="fas fa-eye"></i>
                                <span>Preview Changes</span>
                            </a>
                        </div>
                    </div>

                    <div class="branding-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= !empty($colors) ? count($colors) : 0 ?></span>
                            <span class="stat-label">Colors</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= !empty($fonts) ? count($fonts) : 0 ?></span>
                            <span class="stat-label">Fonts</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= !empty($logo) ? '1' : '0' ?></span>
                            <span class="stat-label">Logo</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">Live</span>
                            <span class="stat-label">Preview</span>
                        </div>
                    </div>
                </div>

                <div class="branding-container">

                    <!-- Alert Messages -->
                    <?php if ($message): ?>
                    <div class="alert-enhanced alert-<?= $messageType ?>">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                        <?= $message ?>
                    </div>
                    <?php endif; ?>

                    <!-- Main Content Grid -->
                    <div class="section-grid">
                        <!-- Color Settings -->
                        <div class="enhanced-card" style="grid-column: 1 / -1;">
                            <div class="enhanced-card-header">
                                <h3><i class="fas fa-palette"></i> Color Scheme Customization</h3>
                            </div>
                            <div class="enhanced-card-content">
                                <form method="POST" id="color-scheme-form">
                                    <?= getCSRFField() ?>
                                    <input type="hidden" name="action" value="update_colors">

                                    <!-- Color Presets Section -->
                                    <div class="color-presets-section">
                                        <h4><i class="fas fa-swatchbook"></i> Color Presets & Palettes</h4>
                                        <div class="preset-container">
                                            <div class="preset-tabs">
                                                <button type="button" class="preset-tab active" data-preset-tab="presets">Quick Presets</button>
                                                <button type="button" class="preset-tab" data-preset-tab="palettes">Color Palettes</button>
                                                <button type="button" class="preset-tab" data-preset-tab="custom">Custom Palette</button>
                                            </div>

                                            <!-- Quick Presets -->
                                            <div class="preset-content active" id="presets-content">
                                                <div class="preset-grid">
                                                    <div class="preset-item" data-preset="modern-blue">
                                                        <div class="preset-colors">
                                                            <span style="background: #3498db"></span>
                                                            <span style="background: #2c3e50"></span>
                                                            <span style="background: #e74c3c"></span>
                                                            <span style="background: #f39c12"></span>
                                                        </div>
                                                        <span class="preset-name">Modern Blue</span>
                                                    </div>
                                                    <div class="preset-item" data-preset="elegant-purple">
                                                        <div class="preset-colors">
                                                            <span style="background: #9b59b6"></span>
                                                            <span style="background: #34495e"></span>
                                                            <span style="background: #e67e22"></span>
                                                            <span style="background: #f1c40f"></span>
                                                        </div>
                                                        <span class="preset-name">Elegant Purple</span>
                                                    </div>
                                                    <div class="preset-item" data-preset="nature-green">
                                                        <div class="preset-colors">
                                                            <span style="background: #27ae60"></span>
                                                            <span style="background: #2c3e50"></span>
                                                            <span style="background: #e74c3c"></span>
                                                            <span style="background: #f39c12"></span>
                                                        </div>
                                                        <span class="preset-name">Nature Green</span>
                                                    </div>
                                                    <div class="preset-item" data-preset="warm-orange">
                                                        <div class="preset-colors">
                                                            <span style="background: #e67e22"></span>
                                                            <span style="background: #34495e"></span>
                                                            <span style="background: #c0392b"></span>
                                                            <span style="background: #f39c12"></span>
                                                        </div>
                                                        <span class="preset-name">Warm Orange</span>
                                                    </div>
                                                    <div class="preset-item" data-preset="professional-gray">
                                                        <div class="preset-colors">
                                                            <span style="background: #95a5a6"></span>
                                                            <span style="background: #2c3e50"></span>
                                                            <span style="background: #e74c3c"></span>
                                                            <span style="background: #f39c12"></span>
                                                        </div>
                                                        <span class="preset-name">Professional Gray</span>
                                                    </div>
                                                    <div class="preset-item" data-preset="ocean-teal">
                                                        <div class="preset-colors">
                                                            <span style="background: #1abc9c"></span>
                                                            <span style="background: #2c3e50"></span>
                                                            <span style="background: #e74c3c"></span>
                                                            <span style="background: #f39c12"></span>
                                                        </div>
                                                        <span class="preset-name">Ocean Teal</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Color Palettes -->
                                            <div class="preset-content" id="palettes-content">
                                                <div class="palette-grid">
                                                    <div class="palette-item" data-palette="construction">
                                                        <div class="palette-colors">
                                                            <span style="background: #d4a574"></span>
                                                            <span style="background: #1a1a1a"></span>
                                                            <span style="background: #ffffff"></span>
                                                            <span style="background: #f4f4f4"></span>
                                                            <span style="background: #333333"></span>
                                                        </div>
                                                        <span class="palette-name">Construction Pro</span>
                                                        <small>Perfect for construction businesses</small>
                                                    </div>
                                                    <div class="palette-item" data-palette="corporate">
                                                        <div class="palette-colors">
                                                            <span style="background: #2c5aa0"></span>
                                                            <span style="background: #ffffff"></span>
                                                            <span style="background: #f8f9fa"></span>
                                                            <span style="background: #343a40"></span>
                                                            <span style="background: #6c757d"></span>
                                                        </div>
                                                        <span class="palette-name">Corporate Blue</span>
                                                        <small>Professional corporate look</small>
                                                    </div>
                                                    <div class="palette-item" data-palette="creative">
                                                        <div class="palette-colors">
                                                            <span style="background: #ff6b6b"></span>
                                                            <span style="background: #4ecdc4"></span>
                                                            <span style="background: #45b7d1"></span>
                                                            <span style="background: #f9ca24"></span>
                                                            <span style="background: #6c5ce7"></span>
                                                        </div>
                                                        <span class="palette-name">Creative Vibrant</span>
                                                        <small>Bold and creative design</small>
                                                    </div>
                                                    <div class="palette-item" data-palette="minimal">
                                                        <div class="palette-colors">
                                                            <span style="background: #000000"></span>
                                                            <span style="background: #ffffff"></span>
                                                            <span style="background: #f5f5f5"></span>
                                                            <span style="background: #333333"></span>
                                                            <span style="background: #666666"></span>
                                                        </div>
                                                        <span class="palette-name">Minimal Black</span>
                                                        <small>Clean minimal design</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Custom Palette Generator -->
                                            <div class="preset-content" id="custom-content">
                                                <div class="custom-palette-generator">
                                                    <div class="generator-controls">
                                                        <div class="control-group">
                                                            <label>Base Color:</label>
                                                            <input type="color" id="base-color" value="#3498db" class="color-input">
                                                        </div>
                                                        <div class="control-group">
                                                            <label>Palette Type:</label>
                                                            <select id="palette-type" class="form-control-enhanced">
                                                                <option value="monochromatic">Monochromatic</option>
                                                                <option value="analogous">Analogous</option>
                                                                <option value="complementary">Complementary</option>
                                                                <option value="triadic">Triadic</option>
                                                                <option value="tetradic">Tetradic</option>
                                                            </select>
                                                        </div>
                                                        <button type="button" class="btn-enhanced" onclick="generateCustomPalette()">
                                                            <i class="fas fa-magic"></i> Generate Palette
                                                        </button>
                                                    </div>
                                                    <div class="generated-palette" id="generated-palette">
                                                        <div class="palette-preview">
                                                            <span class="generated-color" style="background: #3498db"></span>
                                                            <span class="generated-color" style="background: #2980b9"></span>
                                                            <span class="generated-color" style="background: #5dade2"></span>
                                                            <span class="generated-color" style="background: #85c1e9"></span>
                                                            <span class="generated-color" style="background: #aed6f1"></span>
                                                        </div>
                                                        <button type="button" class="btn-outline-enhanced" onclick="applyGeneratedPalette()">
                                                            <i class="fas fa-check"></i> Apply This Palette
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Color Scheme Tabs -->
                                    <div class="color-tabs">
                                        <button type="button" class="color-tab active" data-tab="basic">Basic Colors</button>
                                        <button type="button" class="color-tab" data-tab="header">Header & Navigation</button>
                                        <button type="button" class="color-tab" data-tab="content">Content & Buttons</button>
                                        <button type="button" class="color-tab" data-tab="footer">Footer & Links</button>
                                    </div>

                                    <!-- Basic Colors Tab -->
                                    <div class="color-tab-content active" id="basic-tab">
                                        <h4><i class="fas fa-swatchbook"></i> Primary Brand Colors</h4>
                                        <div class="color-grid">
                                            <div class="color-input-group">
                                                <label for="primary_color">Primary Color</label>
                                                <input type="color" id="primary_color" name="primary_color"
                                                       value="<?= getColor($colors, 'primary_color', '#e74c3c') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'primary_color', '#e74c3c') ?>">
                                                    <?= getColor($colors, 'primary_color', '#e74c3c') ?>
                                                </div>
                                                <small>Main brand color for highlights and CTAs</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="secondary_color">Secondary Color</label>
                                                <input type="color" id="secondary_color" name="secondary_color"
                                                       value="<?= getColor($colors, 'secondary_color', '#2c3e50') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'secondary_color', '#2c3e50') ?>">
                                                    <?= getColor($colors, 'secondary_color', '#2c3e50') ?>
                                                </div>
                                                <small>Supporting color for sections and elements</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="accent_color">Accent Color</label>
                                                <input type="color" id="accent_color" name="accent_color"
                                                       value="<?= getColor($colors, 'accent_color', '#f39c12') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'accent_color', '#f39c12') ?>">
                                                    <?= getColor($colors, 'accent_color', '#f39c12') ?>
                                                </div>
                                                <small>Accent color for special elements and highlights</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="text_color">Text Color</label>
                                                <input type="color" id="text_color" name="text_color"
                                                       value="<?= getColor($colors, 'text_color', '#333333') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'text_color', '#333333') ?>">
                                                    <?= getColor($colors, 'text_color', '#333333') ?>
                                                </div>
                                                <small>Main text color for content</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="background_color">Background Color</label>
                                                <input type="color" id="background_color" name="background_color"
                                                       value="<?= getColor($colors, 'background_color', '#ffffff') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'background_color', '#ffffff') ?>; color: #333;">
                                                    <?= getColor($colors, 'background_color', '#ffffff') ?>
                                                </div>
                                                <small>Main background color for the website</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Header Colors Tab -->
                                    <div class="color-tab-content" id="header-tab">
                                        <h4><i class="fas fa-window-maximize"></i> Header & Navigation Colors</h4>
                                        <div class="color-grid">
                                            <div class="color-input-group">
                                                <label for="header_bg_color">Header Background</label>
                                                <input type="color" id="header_bg_color" name="header_bg_color"
                                                       value="<?= getColor($colors, 'header_bg_color', '#ffffff') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'header_bg_color', '#ffffff') ?>; color: #333;">
                                                    <?= getColor($colors, 'header_bg_color', '#ffffff') ?>
                                                </div>
                                                <small>Background color for the header section</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="header_text_color">Header Text</label>
                                                <input type="color" id="header_text_color" name="header_text_color"
                                                       value="<?= getColor($colors, 'header_text_color', '#1a1a1a') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'header_text_color', '#1a1a1a') ?>">
                                                    <?= getColor($colors, 'header_text_color', '#1a1a1a') ?>
                                                </div>
                                                <small>Text color for navigation and header elements</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Content & Buttons Tab -->
                                    <div class="color-tab-content" id="content-tab">
                                        <h4><i class="fas fa-mouse-pointer"></i> Buttons & Interactive Elements</h4>
                                        <div class="color-grid">
                                            <div class="color-input-group">
                                                <label for="button_color">Button Color</label>
                                                <input type="color" id="button_color" name="button_color"
                                                       value="<?= getColor($colors, 'button_color', '#1a1a1a') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'button_color', '#1a1a1a') ?>">
                                                    <?= getColor($colors, 'button_color', '#1a1a1a') ?>
                                                </div>
                                                <small>Default button background color</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="button_hover_color">Button Hover</label>
                                                <input type="color" id="button_hover_color" name="button_hover_color"
                                                       value="<?= getColor($colors, 'button_hover_color', '#d4a574') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'button_hover_color', '#d4a574') ?>">
                                                    <?= getColor($colors, 'button_hover_color', '#d4a574') ?>
                                                </div>
                                                <small>Button color when hovered</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Footer & Links Tab -->
                                    <div class="color-tab-content" id="footer-tab">
                                        <h4><i class="fas fa-link"></i> Footer & Link Colors</h4>
                                        <div class="color-grid">
                                            <div class="color-input-group">
                                                <label for="footer_bg_color">Footer Background</label>
                                                <input type="color" id="footer_bg_color" name="footer_bg_color"
                                                       value="<?= getColor($colors, 'footer_bg_color', '#0a0a0a') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'footer_bg_color', '#0a0a0a') ?>">
                                                    <?= getColor($colors, 'footer_bg_color', '#0a0a0a') ?>
                                                </div>
                                                <small>Background color for the footer section</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="footer_text_color">Footer Text</label>
                                                <input type="color" id="footer_text_color" name="footer_text_color"
                                                       value="<?= getColor($colors, 'footer_text_color', '#ffffff') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'footer_text_color', '#ffffff') ?>">
                                                    <?= getColor($colors, 'footer_text_color', '#ffffff') ?>
                                                </div>
                                                <small>Text color for footer content</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="link_color">Link Color</label>
                                                <input type="color" id="link_color" name="link_color"
                                                       value="<?= getColor($colors, 'link_color', '#d4a574') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'link_color', '#d4a574') ?>">
                                                    <?= getColor($colors, 'link_color', '#d4a574') ?>
                                                </div>
                                                <small>Default link color</small>
                                            </div>

                                            <div class="color-input-group">
                                                <label for="link_hover_color">Link Hover</label>
                                                <input type="color" id="link_hover_color" name="link_hover_color"
                                                       value="<?= getColor($colors, 'link_hover_color', '#f4d03f') ?>" class="color-input">
                                                <div class="color-preview" style="background-color: <?= getColor($colors, 'link_hover_color', '#f4d03f') ?>">
                                                    <?= getColor($colors, 'link_hover_color', '#f4d03f') ?>
                                                </div>
                                                <small>Link color when hovered</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="color-actions">
                                        <button type="button" class="btn-outline-enhanced" onclick="resetColors()">
                                            <i class="fas fa-undo"></i> Reset to Defaults
                                        </button>
                                        <button type="button" class="btn-outline-enhanced" onclick="previewColors()">
                                            <i class="fas fa-eye"></i> Live Preview
                                        </button>
                                        <button type="submit" class="btn-enhanced">
                                            <i class="fas fa-save"></i> Save Color Scheme
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Font Settings -->
                        <div class="enhanced-card">
                            <div class="enhanced-card-header">
                                <h3><i class="fas fa-font"></i> Typography</h3>
                            </div>
                            <div class="enhanced-card-content">
                                <form method="POST">
                                    <?= getCSRFField() ?>
                                    <input type="hidden" name="action" value="update_fonts">

                                    <div class="font-section">
                                        <div class="form-group">
                                            <label for="heading_font">Heading Font</label>
                                            <select id="heading_font" name="heading_font" class="form-control-enhanced">
                                                <option value="Oswald" <?= getFont($fonts, 'heading_font', 'Oswald') === 'Oswald' ? 'selected' : '' ?>>Oswald</option>
                                                <option value="Roboto" <?= getFont($fonts, 'heading_font', 'Oswald') === 'Roboto' ? 'selected' : '' ?>>Roboto</option>
                                                <option value="Open Sans" <?= getFont($fonts, 'heading_font', 'Oswald') === 'Open Sans' ? 'selected' : '' ?>>Open Sans</option>
                                                <option value="Lato" <?= getFont($fonts, 'heading_font', 'Oswald') === 'Lato' ? 'selected' : '' ?>>Lato</option>
                                                <option value="Montserrat" <?= getFont($fonts, 'heading_font', 'Oswald') === 'Montserrat' ? 'selected' : '' ?>>Montserrat</option>
                                                <option value="Poppins" <?= getFont($fonts, 'heading_font', 'Oswald') === 'Poppins' ? 'selected' : '' ?>>Poppins</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="body_font">Body Font</label>
                                            <select id="body_font" name="body_font" class="form-control-enhanced">
                                                <option value="Roboto" <?= getFont($fonts, 'body_font', 'Roboto') === 'Roboto' ? 'selected' : '' ?>>Roboto</option>
                                                <option value="Open Sans" <?= getFont($fonts, 'body_font', 'Roboto') === 'Open Sans' ? 'selected' : '' ?>>Open Sans</option>
                                                <option value="Lato" <?= getFont($fonts, 'body_font', 'Roboto') === 'Lato' ? 'selected' : '' ?>>Lato</option>
                                                <option value="Source Sans Pro" <?= getFont($fonts, 'body_font', 'Roboto') === 'Source Sans Pro' ? 'selected' : '' ?>>Source Sans Pro</option>
                                                <option value="Nunito" <?= getFont($fonts, 'body_font', 'Roboto') === 'Nunito' ? 'selected' : '' ?>>Nunito</option>
                                            </select>
                                        </div>

                                        <div class="font-preview">
                                            <h3 style="font-family: <?= getFont($fonts, 'heading_font', 'Oswald') ?>, sans-serif;">
                                                Heading Preview (<?= getFont($fonts, 'heading_font', 'Oswald') ?>)
                                            </h3>
                                            <p style="font-family: <?= getFont($fonts, 'body_font', 'Roboto') ?>, sans-serif;">
                                                This is how your body text will look with the <?= getFont($fonts, 'body_font', 'Roboto') ?> font family. It provides a clear example of readability and style.
                                            </p>
                                        </div>
                                    </div>

                                    <button type="submit" class="btn-enhanced">
                                        <i class="fas fa-save"></i> Update Fonts
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Logo Settings -->
                    <div class="enhanced-card" style="grid-column: 1 / -1;">
                        <div class="enhanced-card-header">
                            <h3><i class="fas fa-image"></i> Logo Management</h3>
                        </div>
                        <div class="enhanced-card-content">
                            <div class="logo-section">
                                <div class="logo-upload-area">
                                    <form method="POST" enctype="multipart/form-data" id="logo-upload-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="upload_logo">
                                        <input type="hidden" name="MAX_FILE_SIZE" value="<?= MAX_FILE_SIZE ?>">

                                        <div class="form-group">
                                            <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: #667eea; margin-bottom: 20px;"></i>
                                            <h4 style="margin-bottom: 15px; color: #2c3e50;">Upload New Logo</h4>
                                            <input type="file" id="logo_file" name="logo_file"
                                                   accept=".jpg,.jpeg,.png,.gif,.webp,.svg"
                                                   class="form-control-enhanced" required>
                                            <div style="margin-top: 15px; color: #6c757d; font-size: 0.9rem;">
                                                <p><strong>Supported formats:</strong> JPG, PNG, GIF, WebP, SVG</p>
                                                <p><strong>Maximum size:</strong> <?= (MAX_FILE_SIZE / 1024 / 1024) ?>MB</p>
                                                <p><strong>Recommended:</strong> PNG format with transparent background, max 200x100px</p>
                                            </div>
                                            <div id="file-preview" style="margin-top: 20px; display: none;">
                                                <img id="preview-image" style="max-width: 200px; max-height: 100px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                                            </div>
                                        </div>

                                        <button type="submit" class="btn-enhanced" id="upload-btn" style="margin-top: 20px;">
                                            <i class="fas fa-upload"></i> Upload Logo
                                        </button>
                                    </form>
                                </div>

                                <div class="logo-preview-container">
                                    <h4 style="margin-bottom: 20px; color: #2c3e50;">Current Logo</h4>
                                    <?php if ($logo && isset($logo['logo_path'])): ?>
                                        <img src="<?= UPLOAD_URL . '/' . $logo['logo_path'] ?>" alt="Current Logo" class="logo-preview"
                                             onerror="this.src='<?= ASSETS_URL ?>/images/logo.png'; this.alt='Logo not found';">
                                        <div style="margin-top: 15px; color: #6c757d; font-size: 0.9rem;">
                                            <p><strong>File:</strong> <?= htmlspecialchars($logo['original_name'] ?? $logo['logo_filename'] ?? 'Unknown') ?></p>
                                            <?php if (isset($logo['file_size'])): ?>
                                            <p><strong>Size:</strong> <?= round($logo['file_size'] / 1024, 2) ?>KB</p>
                                            <?php endif; ?>
                                            <p><strong>Path:</strong> <?= htmlspecialchars($logo['logo_path']) ?></p>
                                        </div>
                                    <?php else: ?>
                                        <img src="<?= ASSETS_URL ?>/images/logo.png" alt="Default Logo" class="logo-preview">
                                        <div style="margin-top: 15px; color: #6c757d; font-size: 0.9rem;">
                                            <p>Using default logo - no custom logo uploaded</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="preview-section" style="grid-column: 1 / -1;">
                        <div class="preview-header">
                            <h3><i class="fas fa-eye"></i> Live Preview</h3>
                            <p style="margin: 0; opacity: 0.9;">See how your branding choices will look on your website</p>
                        </div>
                        <div class="preview-content">
                            <div class="preview-demo" style="background: <?= getColor($colors, 'background_color', '#ffffff') ?>;
                                        color: <?= getColor($colors, 'text_color', '#333333') ?>;">
                                <h2 style="font-family: <?= getFont($fonts, 'heading_font', 'Oswald') ?>, sans-serif;
                                           color: <?= getColor($colors, 'primary_color', '#e74c3c') ?>; margin-bottom: 20px; font-size: 2rem;">
                                    Welcome to Flori Construction Ltd
                                </h2>
                                <p style="font-family: <?= getFont($fonts, 'body_font', 'Roboto') ?>, sans-serif; margin-bottom: 25px; font-size: 1.1rem; line-height: 1.6;">
                                    This is a preview of how your website will look with the current branding settings. Your chosen colors and fonts create the visual identity that visitors will experience.
                                </p>
                                <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                                    <button style="background: <?= getColor($colors, 'primary_color', '#e74c3c') ?>;
                                                   color: white; border: none; padding: 12px 25px;
                                                   border-radius: 8px; font-family: <?= getFont($fonts, 'body_font', 'Roboto') ?>, sans-serif;
                                                   font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                        Primary Button
                                    </button>
                                    <button style="background: <?= getColor($colors, 'secondary_color', '#2c3e50') ?>;
                                                   color: white; border: none; padding: 12px 25px;
                                                   border-radius: 8px; font-family: <?= getFont($fonts, 'body_font', 'Roboto') ?>, sans-serif;
                                                   font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                        Secondary Button
                                    </button>
                                    <button style="background: <?= getColor($colors, 'accent_color', '#f39c12') ?>;
                                                   color: white; border: none; padding: 12px 25px;
                                                   border-radius: 8px; font-family: <?= getFont($fonts, 'body_font', 'Roboto') ?>, sans-serif;
                                                   font-weight: 500; cursor: pointer; transition: all 0.3s ease;">
                                        Accent Button
                                    </button>
                                </div>
                            </div>

                            <div style="margin-top: 30px; text-align: center;">
                                <a href="../index.php" target="_blank" class="btn-outline-enhanced">
                                    <i class="fas fa-external-link-alt"></i> View Live Website
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle all branding forms with loading states
            const brandingForms = document.querySelectorAll('form[method="POST"]');
            brandingForms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn && !submitBtn.disabled) {
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
                        submitBtn.disabled = true;

                        // Re-enable after timeout in case of error
                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }, 10000);
                    }
                });
            });

            const logoForm = document.getElementById('logo-upload-form');
            const logoFileInput = document.getElementById('logo_file');
            const uploadBtn = document.getElementById('upload-btn');
            const filePreview = document.getElementById('file-preview');
            const previewImage = document.getElementById('preview-image');

            // File input change handler
            logoFileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];

                if (file) {
                    // Validate file type
                    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
                    if (!allowedTypes.includes(file.type)) {
                        alert('Invalid file type. Please select a JPG, PNG, GIF, WebP, or SVG image.');
                        this.value = '';
                        filePreview.style.display = 'none';
                        return;
                    }

                    // Validate file size (<?= MAX_FILE_SIZE ?> bytes)
                    if (file.size > <?= MAX_FILE_SIZE ?>) {
                        alert('File too large. Maximum size is <?= (MAX_FILE_SIZE / 1024 / 1024) ?>MB.');
                        this.value = '';
                        filePreview.style.display = 'none';
                        return;
                    }

                    // Show preview
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        previewImage.src = e.target.result;
                        filePreview.style.display = 'block';
                    };
                    reader.readAsDataURL(file);
                } else {
                    filePreview.style.display = 'none';
                }
            });

            // Form submit handler for logo form (validation only)
            logoForm.addEventListener('submit', function(e) {
                const file = logoFileInput.files[0];

                if (!file) {
                    e.preventDefault();
                    alert('Please select a logo file to upload.');
                    return;
                }

                // Loading state is handled by the general form handler above
            });

            // Preset Tab Functionality
            document.querySelectorAll('.preset-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all preset tabs and content
                    document.querySelectorAll('.preset-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.preset-content').forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show corresponding content
                    const tabId = this.getAttribute('data-preset-tab') + '-content';
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Color Scheme Tab Functionality
            document.querySelectorAll('.color-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs and content
                    document.querySelectorAll('.color-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.color-tab-content').forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show corresponding content
                    const tabId = this.getAttribute('data-tab') + '-tab';
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Preset Item Click Handlers
            document.querySelectorAll('.preset-item').forEach(item => {
                item.addEventListener('click', function() {
                    const presetName = this.getAttribute('data-preset');
                    applyPreset(presetName);
                });
            });

            // Palette Item Click Handlers
            document.querySelectorAll('.palette-item').forEach(item => {
                item.addEventListener('click', function() {
                    const paletteName = this.getAttribute('data-palette');
                    applyPalette(paletteName);
                });
            });

            // Color input change handler
            document.querySelectorAll('.color-input').forEach(input => {
                input.addEventListener('input', function() {
                    const preview = this.parentElement.querySelector('.color-preview');
                    if (preview) {
                        preview.style.backgroundColor = this.value;
                        preview.textContent = this.value;

                        // Adjust text color for better contrast
                        const rgb = hexToRgb(this.value);
                        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
                        preview.style.color = brightness > 128 ? '#000' : '#fff';
                    }
                });
            });

            // Hex to RGB conversion function
            function hexToRgb(hex) {
                const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
                return result ? {
                    r: parseInt(result[1], 16),
                    g: parseInt(result[2], 16),
                    b: parseInt(result[3], 16)
                } : null;
            }
        });

        // Reset Colors Function (Global scope for onclick)
        function resetColors() {
            if (confirm('Are you sure you want to reset all colors to default values?')) {
                const defaultColors = {
                    'primary_color': '#e74c3c',
                    'secondary_color': '#2c3e50',
                    'accent_color': '#f39c12',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#ffffff',
                    'header_text_color': '#1a1a1a',
                    'button_color': '#1a1a1a',
                    'button_hover_color': '#d4a574',
                    'footer_bg_color': '#0a0a0a',
                    'footer_text_color': '#ffffff',
                    'link_color': '#d4a574',
                    'link_hover_color': '#f4d03f'
                };

                Object.keys(defaultColors).forEach(colorKey => {
                    const input = document.getElementById(colorKey);
                    if (input) {
                        const preview = input.parentElement.querySelector('.color-preview');

                        input.value = defaultColors[colorKey];
                        if (preview) {
                            preview.style.backgroundColor = defaultColors[colorKey];
                            preview.textContent = defaultColors[colorKey];

                            // Adjust text color for better contrast
                            const rgb = hexToRgb(defaultColors[colorKey]);
                            const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
                            preview.style.color = brightness > 128 ? '#000' : '#fff';
                        }
                    }
                });
            }
        }

        // Live Preview Function (Global scope for onclick)
        function previewColors() {
            const colors = {};
            document.querySelectorAll('.color-input').forEach(input => {
                colors[input.name] = input.value;
            });

            // Create preview window
            const previewWindow = window.open('<?= BASE_URL ?>', 'preview', 'width=1200,height=800,scrollbars=yes');

            if (previewWindow) {
                previewWindow.addEventListener('load', function() {
                    // Inject custom styles for preview
                    const style = previewWindow.document.createElement('style');
                    style.textContent = `
                        :root {
                            --primary-color: ${colors.primary_color} !important;
                            --secondary-color: ${colors.secondary_color} !important;
                            --accent-color: ${colors.accent_color} !important;
                        }

                        body { background-color: ${colors.background_color} !important; color: ${colors.text_color} !important; }
                        header { background-color: ${colors.header_bg_color} !important; color: ${colors.header_text_color} !important; }
                        .header { background-color: ${colors.header_bg_color} !important; color: ${colors.header_text_color} !important; }
                        nav a { color: ${colors.header_text_color} !important; }
                        .btn, button { background-color: ${colors.button_color} !important; }
                        .btn:hover, button:hover { background-color: ${colors.button_hover_color} !important; }
                        footer { background-color: ${colors.footer_bg_color} !important; color: ${colors.footer_text_color} !important; }
                        .footer { background-color: ${colors.footer_bg_color} !important; color: ${colors.footer_text_color} !important; }
                        a { color: ${colors.link_color} !important; }
                        a:hover { color: ${colors.link_hover_color} !important; }
                    `;
                    previewWindow.document.head.appendChild(style);
                });
            } else {
                alert('Please allow popups to use the preview feature.');
            }
        }

        // Apply Preset Function
        function applyPreset(presetName) {
            const presets = {
                'modern-blue': {
                    'primary_color': '#3498db',
                    'secondary_color': '#2c3e50',
                    'accent_color': '#e74c3c',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#3498db',
                    'header_text_color': '#ffffff',
                    'button_color': '#3498db',
                    'button_hover_color': '#2980b9',
                    'footer_bg_color': '#2c3e50',
                    'footer_text_color': '#ffffff',
                    'link_color': '#3498db',
                    'link_hover_color': '#2980b9'
                },
                'elegant-purple': {
                    'primary_color': '#9b59b6',
                    'secondary_color': '#34495e',
                    'accent_color': '#e67e22',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#9b59b6',
                    'header_text_color': '#ffffff',
                    'button_color': '#9b59b6',
                    'button_hover_color': '#8e44ad',
                    'footer_bg_color': '#34495e',
                    'footer_text_color': '#ffffff',
                    'link_color': '#9b59b6',
                    'link_hover_color': '#8e44ad'
                },
                'nature-green': {
                    'primary_color': '#27ae60',
                    'secondary_color': '#2c3e50',
                    'accent_color': '#e74c3c',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#27ae60',
                    'header_text_color': '#ffffff',
                    'button_color': '#27ae60',
                    'button_hover_color': '#229954',
                    'footer_bg_color': '#2c3e50',
                    'footer_text_color': '#ffffff',
                    'link_color': '#27ae60',
                    'link_hover_color': '#229954'
                },
                'warm-orange': {
                    'primary_color': '#e67e22',
                    'secondary_color': '#34495e',
                    'accent_color': '#c0392b',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#e67e22',
                    'header_text_color': '#ffffff',
                    'button_color': '#e67e22',
                    'button_hover_color': '#d35400',
                    'footer_bg_color': '#34495e',
                    'footer_text_color': '#ffffff',
                    'link_color': '#e67e22',
                    'link_hover_color': '#d35400'
                },
                'professional-gray': {
                    'primary_color': '#95a5a6',
                    'secondary_color': '#2c3e50',
                    'accent_color': '#e74c3c',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#95a5a6',
                    'header_text_color': '#ffffff',
                    'button_color': '#95a5a6',
                    'button_hover_color': '#7f8c8d',
                    'footer_bg_color': '#2c3e50',
                    'footer_text_color': '#ffffff',
                    'link_color': '#95a5a6',
                    'link_hover_color': '#7f8c8d'
                },
                'ocean-teal': {
                    'primary_color': '#1abc9c',
                    'secondary_color': '#2c3e50',
                    'accent_color': '#e74c3c',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#1abc9c',
                    'header_text_color': '#ffffff',
                    'button_color': '#1abc9c',
                    'button_hover_color': '#16a085',
                    'footer_bg_color': '#2c3e50',
                    'footer_text_color': '#ffffff',
                    'link_color': '#1abc9c',
                    'link_hover_color': '#16a085'
                }
            };

            if (presets[presetName]) {
                if (confirm(`Apply the "${presetName.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}" color preset?`)) {
                    applyColorScheme(presets[presetName]);
                }
            }
        }

        // Apply Palette Function
        function applyPalette(paletteName) {
            const palettes = {
                'construction': {
                    'primary_color': '#d4a574',
                    'secondary_color': '#1a1a1a',
                    'accent_color': '#f4f4f4',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#1a1a1a',
                    'header_text_color': '#d4a574',
                    'button_color': '#d4a574',
                    'button_hover_color': '#c19660',
                    'footer_bg_color': '#1a1a1a',
                    'footer_text_color': '#d4a574',
                    'link_color': '#d4a574',
                    'link_hover_color': '#c19660'
                },
                'corporate': {
                    'primary_color': '#2c5aa0',
                    'secondary_color': '#343a40',
                    'accent_color': '#6c757d',
                    'text_color': '#343a40',
                    'background_color': '#ffffff',
                    'header_bg_color': '#2c5aa0',
                    'header_text_color': '#ffffff',
                    'button_color': '#2c5aa0',
                    'button_hover_color': '#1e3f73',
                    'footer_bg_color': '#343a40',
                    'footer_text_color': '#ffffff',
                    'link_color': '#2c5aa0',
                    'link_hover_color': '#1e3f73'
                },
                'creative': {
                    'primary_color': '#ff6b6b',
                    'secondary_color': '#4ecdc4',
                    'accent_color': '#45b7d1',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#ff6b6b',
                    'header_text_color': '#ffffff',
                    'button_color': '#4ecdc4',
                    'button_hover_color': '#45b7d1',
                    'footer_bg_color': '#6c5ce7',
                    'footer_text_color': '#ffffff',
                    'link_color': '#ff6b6b',
                    'link_hover_color': '#f9ca24'
                },
                'minimal': {
                    'primary_color': '#000000',
                    'secondary_color': '#333333',
                    'accent_color': '#666666',
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': '#ffffff',
                    'header_text_color': '#000000',
                    'button_color': '#000000',
                    'button_hover_color': '#333333',
                    'footer_bg_color': '#f5f5f5',
                    'footer_text_color': '#333333',
                    'link_color': '#000000',
                    'link_hover_color': '#666666'
                }
            };

            if (palettes[paletteName]) {
                if (confirm(`Apply the "${paletteName.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}" color palette?`)) {
                    applyColorScheme(palettes[paletteName]);
                }
            }
        }

        // Apply Color Scheme Function
        function applyColorScheme(colors) {
            Object.keys(colors).forEach(colorKey => {
                const input = document.getElementById(colorKey);
                if (input) {
                    const preview = input.parentElement.querySelector('.color-preview');

                    input.value = colors[colorKey];
                    if (preview) {
                        preview.style.backgroundColor = colors[colorKey];
                        preview.textContent = colors[colorKey];

                        // Adjust text color for better contrast
                        const rgb = hexToRgb(colors[colorKey]);
                        const brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
                        preview.style.color = brightness > 128 ? '#000' : '#fff';
                    }
                }
            });
        }

        // Generate Custom Palette Function
        function generateCustomPalette() {
            const baseColor = document.getElementById('base-color').value;
            const paletteType = document.getElementById('palette-type').value;

            const colors = generatePaletteColors(baseColor, paletteType);
            const palettePreview = document.querySelector('.palette-preview');

            // Update the generated palette display
            palettePreview.innerHTML = '';
            colors.forEach(color => {
                const colorSpan = document.createElement('span');
                colorSpan.className = 'generated-color';
                colorSpan.style.backgroundColor = color;
                colorSpan.title = color;
                palettePreview.appendChild(colorSpan);
            });

            // Store colors for applying
            window.generatedColors = colors;
        }

        // Apply Generated Palette Function
        function applyGeneratedPalette() {
            if (window.generatedColors && window.generatedColors.length >= 5) {
                const colors = window.generatedColors;
                const colorScheme = {
                    'primary_color': colors[0],
                    'secondary_color': colors[1],
                    'accent_color': colors[2],
                    'text_color': '#333333',
                    'background_color': '#ffffff',
                    'header_bg_color': colors[0],
                    'header_text_color': '#ffffff',
                    'button_color': colors[0],
                    'button_hover_color': colors[1],
                    'footer_bg_color': colors[1],
                    'footer_text_color': '#ffffff',
                    'link_color': colors[2],
                    'link_hover_color': colors[3]
                };

                if (confirm('Apply this generated color palette?')) {
                    applyColorScheme(colorScheme);
                }
            }
        }

        // Generate Palette Colors Function
        function generatePaletteColors(baseColor, type) {
            const hsl = hexToHsl(baseColor);
            let colors = [];

            switch (type) {
                case 'monochromatic':
                    colors = [
                        baseColor,
                        hslToHex(hsl.h, hsl.s, Math.max(0, hsl.l - 0.2)),
                        hslToHex(hsl.h, hsl.s, Math.min(1, hsl.l + 0.2)),
                        hslToHex(hsl.h, Math.max(0, hsl.s - 0.3), hsl.l),
                        hslToHex(hsl.h, Math.min(1, hsl.s + 0.2), Math.min(1, hsl.l + 0.3))
                    ];
                    break;
                case 'analogous':
                    colors = [
                        baseColor,
                        hslToHex((hsl.h + 30) % 360, hsl.s, hsl.l),
                        hslToHex((hsl.h - 30 + 360) % 360, hsl.s, hsl.l),
                        hslToHex((hsl.h + 60) % 360, hsl.s, Math.max(0, hsl.l - 0.2)),
                        hslToHex((hsl.h - 60 + 360) % 360, hsl.s, Math.min(1, hsl.l + 0.2))
                    ];
                    break;
                case 'complementary':
                    colors = [
                        baseColor,
                        hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l),
                        hslToHex(hsl.h, hsl.s, Math.max(0, hsl.l - 0.3)),
                        hslToHex((hsl.h + 180) % 360, hsl.s, Math.max(0, hsl.l - 0.3)),
                        hslToHex(hsl.h, Math.max(0, hsl.s - 0.4), Math.min(1, hsl.l + 0.4))
                    ];
                    break;
                case 'triadic':
                    colors = [
                        baseColor,
                        hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l),
                        hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l),
                        hslToHex(hsl.h, hsl.s, Math.max(0, hsl.l - 0.2)),
                        hslToHex((hsl.h + 120) % 360, hsl.s, Math.max(0, hsl.l - 0.2))
                    ];
                    break;
                case 'tetradic':
                    colors = [
                        baseColor,
                        hslToHex((hsl.h + 90) % 360, hsl.s, hsl.l),
                        hslToHex((hsl.h + 180) % 360, hsl.s, hsl.l),
                        hslToHex((hsl.h + 270) % 360, hsl.s, hsl.l),
                        hslToHex(hsl.h, Math.max(0, hsl.s - 0.3), Math.min(1, hsl.l + 0.3))
                    ];
                    break;
                default:
                    colors = [baseColor, baseColor, baseColor, baseColor, baseColor];
            }

            return colors;
        }

        // Color conversion functions
        function hexToHsl(hex) {
            const rgb = hexToRgb(hex);
            const r = rgb.r / 255;
            const g = rgb.g / 255;
            const b = rgb.b / 255;

            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h, s, l = (max + min) / 2;

            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                switch (max) {
                    case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                    case g: h = (b - r) / d + 2; break;
                    case b: h = (r - g) / d + 4; break;
                }
                h /= 6;
            }

            return { h: h * 360, s: s, l: l };
        }

        function hslToHex(h, s, l) {
            h = h / 360;
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };

            let r, g, b;
            if (s === 0) {
                r = g = b = l;
            } else {
                const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
                const p = 2 * l - q;
                r = hue2rgb(p, q, h + 1/3);
                g = hue2rgb(p, q, h);
                b = hue2rgb(p, q, h - 1/3);
            }

            const toHex = (c) => {
                const hex = Math.round(c * 255).toString(16);
                return hex.length === 1 ? '0' + hex : hex;
            };

            return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
        }

        // Hex to RGB conversion function (Global scope)
        function hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result ? {
                r: parseInt(result[1], 16),
                g: parseInt(result[2], 16),
                b: parseInt(result[3], 16)
            } : null;
        }
    </script>
</body>
</html>
