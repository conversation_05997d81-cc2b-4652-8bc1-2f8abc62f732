<?php
/**
 * Main Configuration File for Flori Construction Ltd
 * Website and Mobile App Backend
 */

// Start session
session_start();

// Error reporting (set to 0 in production)
// For production, uncomment these lines:
// error_reporting(0);
// ini_set('display_errors', 0);
// For development:
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Europe/London');

// Site Configuration
define('SITE_NAME', 'Flori Construction Ltd');
define('SITE_URL', 'http://localhost/erdevwe');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '0208 914 7883');
define('SITE_MOBILE', '078 8292 3621');
define('SITE_ADDRESS', '662 High Road North Finchley, London N12 0NL');

// Paths
define('ROOT_PATH', dirname(__DIR__));
define('UPLOAD_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// URLs
define('BASE_URL', SITE_URL);
define('UPLOAD_URL', BASE_URL . '/uploads');
define('ASSETS_URL', BASE_URL . '/assets');
define('API_URL', BASE_URL . '/api');

// Upload settings
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']);
define('ALLOWED_VIDEO_TYPES', ['mp4', 'webm', 'ogg']);

// Security - CHANGE THESE FOR PRODUCTION!
define('JWT_SECRET', 'your-secret-key-change-this-in-production');
define('PASSWORD_SALT', 'flori-construction-salt-2024');
// Recommended: Use environment variables or generate strong random keys

// Pagination
define('ITEMS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Email Configuration (for contact forms)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'your-app-password');
define('SMTP_FROM_EMAIL', SITE_EMAIL);
define('SMTP_FROM_NAME', SITE_NAME);

// Social Media Links
define('FACEBOOK_URL', 'https://www.facebook.com/FloriConstructionLtd');
define('INSTAGRAM_URL', 'https://www.instagram.com/flori_construction_ltd/');
define('YOUTUBE_URL', 'https://www.youtube.com/@floriconstructionltd7045');
define('LINKEDIN_URL', 'https://www.linkedin.com/in/floriconstructionltd/');

// Include database configuration
require_once ROOT_PATH . '/config/database.php';

// Helper functions
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function generateSlug($text) {
    $text = strtolower($text);
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    $text = preg_replace('/[\s-]+/', '-', $text);
    return trim($text, '-');
}

function formatDate($date, $format = 'F j, Y') {
    return date($format, strtotime($date));
}

function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * CSRF Protection Functions
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

function getCSRFField() {
    return '<input type="hidden" name="csrf_token" value="' . generateCSRFToken() . '">';
}

function requireCSRF() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        if (!validateCSRFToken($token)) {
            die('CSRF token validation failed. Please refresh the page and try again.');
        }
    }
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . '/admin/login.php');
        exit;
    }
}

function getCurrentUser() {
    global $db;
    if (!isLoggedIn()) {
        return null;
    }

    return $db->fetchOne(
        "SELECT id, username, email, full_name, role FROM users WHERE id = ? AND is_active = 1",
        [$_SESSION['user_id']]
    );
}

function redirect($url) {
    header("Location: $url");
    exit;
}

function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

function formatFileSize($bytes) {
    if ($bytes === 0) return '0 Bytes';
    $k = 1024;
    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}

function validateSVGFile($filePath) {
    // Read the SVG content
    $content = file_get_contents($filePath);
    if ($content === false) {
        throw new Exception('Cannot read SVG file');
    }

    // Check for potentially dangerous elements/attributes
    $dangerousPatterns = [
        '/<script[^>]*>.*?<\/script>/is',
        '/<iframe[^>]*>.*?<\/iframe>/is',
        '/<object[^>]*>.*?<\/object>/is',
        '/<embed[^>]*>.*?<\/embed>/is',
        '/on\w+\s*=/i', // onclick, onload, etc.
        '/javascript:/i',
        '/data:text\/html/i',
        '/<foreignObject[^>]*>.*?<\/foreignObject>/is'
    ];

    foreach ($dangerousPatterns as $pattern) {
        if (preg_match($pattern, $content)) {
            throw new Exception('SVG file contains potentially dangerous content');
        }
    }

    // Validate that it's actually an SVG
    if (!preg_match('/<svg[^>]*>/i', $content)) {
        throw new Exception('File is not a valid SVG');
    }

    return true;
}

function uploadFile($file, $directory = 'general') {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        throw new Exception('No file uploaded');
    }

    $uploadDir = UPLOAD_PATH . '/' . $directory;
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $fileInfo = pathinfo($file['name']);
    $extension = strtolower($fileInfo['extension']);
    $filename = uniqid() . '_' . time() . '.' . $extension;
    $filepath = $uploadDir . '/' . $filename;

    // Validate file type
    if (!in_array($extension, array_merge(ALLOWED_IMAGE_TYPES, ALLOWED_VIDEO_TYPES))) {
        throw new Exception('Invalid file type');
    }

    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        throw new Exception('File too large');
    }

    // Special validation for SVG files
    if ($extension === 'svg') {
        validateSVGFile($file['tmp_name']);
    }

    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Failed to upload file');
    }

    return [
        'filename' => $filename,
        'original_name' => $file['name'],
        'file_path' => $directory . '/' . $filename,
        'file_size' => $file['size'],
        'mime_type' => $file['type']
    ];
}

// Auto-create upload directories
$uploadDirs = ['general', 'projects', 'services', 'gallery'];
foreach ($uploadDirs as $dir) {
    $path = UPLOAD_PATH . '/' . $dir;
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
}
?>
