/* Flori Construction Admin App Styles */

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f5f5f5;
    color: #333;
    overflow-x: hidden;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Screen Management */
.screen {
    min-height: 100vh;
}

/* Login Screen */
.login-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 40px 20px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 100vh;
}

.logo {
    text-align: center;
    margin-bottom: 30px;
}

.logo img {
    height: 60px;
    width: auto;
}

.login-form h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #e74c3c;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.btn-ghost {
    background: transparent;
    color: #666;
    border: 1px solid #ddd;
}

.btn-ghost:hover {
    background: #f8f9fa;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Main App Layout */
#main-app {
    display: grid;
    grid-template-areas:
        "header header"
        "sidebar content";
    grid-template-columns: 250px 1fr;
    grid-template-rows: 60px 1fr;
    height: 100vh;
}

/* Header */
.app-header {
    grid-area: header;
    background: white;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
}

#page-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* Sidebar */
.sidebar {
    grid-area: sidebar;
    background: #2c3e50;
    color: white;
    overflow-y: auto;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #34495e;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sidebar-header img {
    height: 30px;
    width: auto;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    gap: 12px;
}

.menu-item:hover,
.menu-item.active {
    background: #34495e;
    color: white;
}

.menu-item i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    grid-area: content;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

/* Pages */
.page {
    display: none;
}

.page.active {
    display: block;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h2 {
    color: #2c3e50;
    font-size: 24px;
}

/* Dashboard */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
}

.stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.stat-info h3 {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 14px;
}

/* Filters */
.filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filters select,
.filters input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
}

/* Grids */
.projects-grid,
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.project-card,
.media-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.project-card:hover,
.media-card:hover {
    transform: translateY(-2px);
}

.project-image,
.media-image {
    height: 150px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.project-content,
.media-content {
    padding: 15px;
}

.project-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #2c3e50;
}

.project-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.project-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.project-type.completed {
    background: #d4edda;
    color: #155724;
}

.project-type.ongoing {
    background: #fff3cd;
    color: #856404;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.pagination button {
    padding: 8px 12px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination button:hover,
.pagination button.active {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 20px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1001;
}

.toast {
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-left: 4px solid #e74c3c;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left-color: #28a745;
}

.toast.error {
    border-left-color: #dc3545;
}

.toast.warning {
    border-left-color: #ffc107;
}

/* Upload Options */
.upload-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.upload-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.upload-option:hover {
    border-color: #e74c3c;
    background: #fff;
    transform: translateY(-2px);
}

.upload-option i {
    font-size: 24px;
}

/* Camera Modal */
.camera-modal {
    max-width: 600px;
    width: 95vw;
}

.camera-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin: 20px 0;
}

#camera-video {
    width: 100%;
    height: auto;
    display: block;
}

#camera-canvas {
    position: absolute;
    top: 0;
    left: 0;
}

.camera-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 20px 0;
}

.camera-capture {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 20px;
}

/* Media Viewer */
.media-viewer {
    max-width: 800px;
    width: 95vw;
}

.media-preview {
    text-align: center;
    margin: 20px 0;
}

.media-preview img,
.media-preview video {
    max-width: 100%;
    max-height: 60vh;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.media-details {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 20px 0;
}

.media-details p {
    margin: 8px 0;
    font-size: 14px;
}

/* Upload Area Enhancements */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #e74c3c;
    background: #fff;
}

.upload-content i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.upload-content h3 {
    color: #666;
    margin-bottom: 10px;
}

.upload-content p {
    color: #999;
    font-size: 14px;
    margin-bottom: 20px;
}

/* Upload Queue */
.upload-queue {
    margin-top: 20px;
}

.upload-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: white;
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 10px;
}

.upload-item .file-preview {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-item .file-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-item .file-preview i {
    font-size: 24px;
    color: #666;
}

.upload-item .file-info {
    flex: 1;
}

.upload-item .file-info h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #333;
}

.upload-item .file-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.upload-progress {
    width: 100%;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
    margin-top: 8px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    transition: width 0.3s ease;
}

.upload-item.upload-success .progress-bar {
    background: #4CAF50;
}

.upload-item.upload-error .progress-bar {
    background: #f44336;
}

/* Media Grid Enhancements */
.media-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.media-item:hover {
    transform: translateY(-2px);
}

.media-preview {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.media-preview img,
.media-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-item:hover .media-overlay {
    opacity: 1;
}

.media-actions {
    display: flex;
    gap: 10px;
}

.media-actions .btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.media-actions .btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.media-info {
    padding: 15px;
}

.media-info h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.alt-text {
    font-size: 12px;
    color: #999;
    font-style: italic;
    margin: 0;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #333;
}

.empty-state p {
    margin-bottom: 30px;
    color: #666;
}

/* Error Messages */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px 15px;
    border-radius: 6px;
    margin-top: 15px;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    #main-app {
        grid-template-areas:
            "header"
            "content";
        grid-template-columns: 1fr;
        grid-template-rows: 60px 1fr;
    }

    .sidebar {
        position: fixed;
        top: 60px;
        left: -250px;
        width: 250px;
        height: calc(100vh - 60px);
        z-index: 999;
        transition: left 0.3s ease;
    }

    .sidebar.active {
        left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .projects-grid,
    .media-grid {
        grid-template-columns: 1fr;
    }

    .filters {
        flex-direction: column;
    }

    .page-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
}

/* Connection Status Indicator */
.connection-status {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.connection-status.online {
    background: #d4edda;
    color: #155724;
}

.connection-status.offline {
    background: #f8d7da;
    color: #721c24;
}

.connection-status i {
    font-size: 10px;
}

/* Offline Mode Styles */
body.offline-mode {
    filter: grayscale(20%);
}

body.offline-mode .btn[data-requires-online]:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

/* Offline Banner */
.offline-banner {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 10px 20px;
    background: #fff3cd;
    border-bottom: 1px solid #ffeaa7;
    color: #856404;
}

.offline-banner.success {
    background: #d4edda;
    border-bottom-color: #c3e6cb;
    color: #155724;
}

.offline-banner.error {
    background: #f8d7da;
    border-bottom-color: #f5c6cb;
    color: #721c24;
}

.banner-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    gap: 10px;
}

.banner-close {
    background: none;
    border: none;
    cursor: pointer;
    opacity: 0.7;
    padding: 2px;
}

.banner-close:hover {
    opacity: 1;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 20px;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #e74c3c;
    background: #fef5f5;
}

.upload-content i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.upload-content h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.upload-content p {
    color: #666;
    margin-bottom: 20px;
}

/* Upload Queue */
.upload-queue {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.upload-queue h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.upload-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 6px;
    margin-bottom: 10px;
}

.upload-item .file-preview {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-item .file-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.upload-item .file-info {
    flex: 1;
}

.upload-item .file-info h4 {
    font-size: 14px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.upload-item .file-info p {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.upload-progress {
    width: 100%;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: #e74c3c;
    width: 0%;
    transition: width 0.3s ease;
}

.upload-actions {
    display: flex;
    gap: 5px;
}

/* Media Grid Enhancements */
.media-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.media-item:hover {
    transform: translateY(-2px);
}

.media-preview {
    position: relative;
    height: 150px;
    overflow: hidden;
}

.media-preview img,
.media-preview video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.media-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.media-item:hover .media-overlay {
    opacity: 1;
}

.media-actions {
    display: flex;
    gap: 10px;
}

.media-info {
    padding: 15px;
}

.media-info h4 {
    font-size: 14px;
    margin-bottom: 8px;
    color: #2c3e50;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.alt-text {
    font-size: 12px;
    color: #888;
    font-style: italic;
}

/* Project Card Enhancements */
.project-card {
    position: relative;
}

.project-image {
    position: relative;
    height: 180px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.project-placeholder {
    color: #ddd;
    font-size: 48px;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #f39c12;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.project-content h3 {
    font-size: 16px;
    margin-bottom: 8px;
    color: #2c3e50;
}

.project-location {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.project-description {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 15px;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.project-date {
    font-size: 11px;
    color: #999;
}

.project-actions {
    display: flex;
    gap: 5px;
    padding: 0 15px 15px;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

/* Content Management */
.content-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.content-tab {
    padding: 10px 20px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    color: #666;
    font-weight: 500;
    transition: all 0.3s ease;
}

.content-tab.active,
.content-tab:hover {
    color: #e74c3c;
    border-bottom-color: #e74c3c;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.content-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.content-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: translateY(-2px);
}

.card-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-bottom: 15px;
}

.card-content h4 {
    color: #2c3e50;
    margin-bottom: 8px;
}

.card-content p {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.card-actions {
    text-align: right;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state i {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #2c3e50;
    margin-bottom: 10px;
}

.empty-state p {
    margin-bottom: 20px;
}

/* Form Enhancements */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 3px;
    position: relative;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark {
    background: #e74c3c;
    border-color: #e74c3c;
}

.checkbox-label input[type="checkbox"]:checked+.checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
}

/* Modal Enhancements */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-header h2 {
    color: #2c3e50;
    font-size: 20px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 5px;
}

.modal-close:hover {
    color: #333;
}

.modal-form {
    max-height: 60vh;
    overflow-y: auto;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Install Banner */
.install-banner {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border-left: 4px solid #e74c3c;
}

.install-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.install-content span {
    flex: 1;
    font-weight: 500;
    color: #2c3e50;
}

/* Activity List */
.activity-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.activity-content {
    flex: 1;
}

.activity-content h4 {
    color: #2c3e50;
    margin-bottom: 4px;
    font-size: 14px;
}

.activity-content p {
    color: #666;
    font-size: 12px;
    margin-bottom: 4px;
}

.activity-time {
    font-size: 11px;
    color: #999;
}

/* Responsive Enhancements */
@media (max-width: 480px) {
    .form-row {
        grid-template-columns: 1fr;
    }

    .install-content {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .content-tabs {
        flex-wrap: wrap;
    }

    .content-tab {
        flex: 1;
        min-width: 120px;
    }
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 1002;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.connection-status.offline {
    background: #f44336;
}

.connection-status.fade-out {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
}

/* In-App Notifications */
.in-app-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 350px;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.in-app-notification.show {
    transform: translateX(0);
}

.in-app-notification.success {
    border-left: 4px solid #4CAF50;
}

.in-app-notification.error {
    border-left: 4px solid #f44336;
}

.in-app-notification.warning {
    border-left: 4px solid #ff9800;
}

.in-app-notification.info {
    border-left: 4px solid #2196F3;
}

.notification-content {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    gap: 12px;
}

.notification-icon {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-text {
    flex: 1;
}

.notification-text h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.notification-text p {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background: #f0f0f0;
}

/* Notification Settings */
.setting-group {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.setting-group:last-child {
    border-bottom: none;
}

.setting-group h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    margin-bottom: 8px;
}

.toggle-switch input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: #ccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked+.toggle-slider {
    background: #e74c3c;
}

.toggle-switch input[type="checkbox"]:checked+.toggle-slider::before {
    transform: translateX(20px);
}

.toggle-label {
    font-size: 14px;
    color: #333;
}

.checkbox-setting {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    cursor: pointer;
}

.checkbox-setting input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #e74c3c;
}

.checkbox-setting span {
    font-size: 14px;
    color: #333;
}

.setting-description {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.notification-status {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

.time-range {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.time-range label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
}

.time-range input[type="time"] {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

/* Offline Mode Styles */
body.offline-mode {
    filter: grayscale(20%);
}

body.offline-mode .btn[data-requires-online]:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}