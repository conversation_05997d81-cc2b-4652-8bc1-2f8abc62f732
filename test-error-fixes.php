<?php
/**
 * Test script to verify all error fixes
 * This script tests the fixes for:
 * 1. Analytics database column issues
 * 2. SEO array access errors
 * 3. htmlspecialchars deprecation warnings
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Error Fixes Test - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: orange; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        h2 { border-bottom: 2px solid #007bff; padding-bottom: 10px; }
    </style>
</head>
<body>";

echo "<h1>Error Fixes Verification Test</h1>";
echo "<p>Testing all the fixes applied to resolve PHP/MySQL errors...</p>";

$db = new Database();
$allTestsPassed = true;

// Test 1: Analytics Database Queries
echo "<h2>Test 1: Analytics Database Queries</h2>";
try {
    // Test projects analytics with corrected column names
    $projectsTotal = $db->fetchOne("SELECT COUNT(*) as count FROM projects")['count'];
    $projectsActive = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE project_type = 'ongoing'")['count'];
    $projectsCompleted = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE project_type = 'completed'")['count'];

    echo "<div class='success'>✓ Projects analytics queries working correctly</div>";
    echo "<div class='info'>Total Projects: $projectsTotal | Active: $projectsActive | Completed: $projectsCompleted</div>";

    // Test inquiries analytics with correct status values
    $inquiriesTotal = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries")['count'];
    $inquiriesPending = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'];
    $inquiriesResponded = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'contacted'")['count'];

    echo "<div class='success'>✓ Inquiries analytics queries working correctly</div>";
    echo "<div class='info'>Total Inquiries: $inquiriesTotal | Pending: $inquiriesPending | Responded: $inquiriesResponded</div>";

    // Test recent projects query with alias
    $recentProjects = $db->fetchAll("SELECT id, title, project_type as status, created_at FROM projects ORDER BY created_at DESC LIMIT 3");
    echo "<div class='success'>✓ Recent projects query with alias working correctly</div>";
    echo "<div class='info'>Found " . count($recentProjects) . " recent projects</div>";

    // Test the fixed top projects query (without the problematic JOIN)
    $topProjects = $db->fetchAll("SELECT p.id, p.title, p.project_type as status, p.created_at FROM projects p WHERE p.is_active = 1 ORDER BY p.created_at DESC LIMIT 3");
    echo "<div class='success'>✓ Top projects query (without media JOIN) working correctly</div>";
    echo "<div class='info'>Found " . count($topProjects) . " top projects</div>";

    // Test the fixed service stats query (without the problematic JOIN)
    $serviceStats = $db->fetchAll("SELECT s.id, s.title, s.is_featured, s.sort_order FROM services s WHERE s.is_active = 1 ORDER BY s.sort_order ASC LIMIT 3");
    echo "<div class='success'>✓ Service stats query (without project JOIN) working correctly</div>";
    echo "<div class='info'>Found " . count($serviceStats) . " services</div>";

} catch (Exception $e) {
    echo "<div class='error'>✗ Analytics database queries failed: " . $e->getMessage() . "</div>";
    $allTestsPassed = false;
}

// Test 2: SEO Page Array Access
echo "<h2>Test 2: SEO Page Array Access</h2>";
try {
    // Simulate the SEO page logic
    $pages = [
        ['id' => 1, 'name' => 'Homepage', 'url' => '/'],
        ['id' => 2, 'name' => 'About', 'url' => '/about'],
        ['id' => 3, 'name' => 'Services', 'url' => '/services']
    ];

    // Test valid page ID
    $pageId = 1;
    $filteredPages = array_filter($pages, function($p) use ($pageId) { return $p['id'] == $pageId; });
    $currentPage = reset($filteredPages);

    if ($currentPage !== false && is_array($currentPage) && isset($currentPage['id'])) {
        echo "<div class='success'>✓ Valid page array access working correctly</div>";
        echo "<div class='info'>Page: {$currentPage['name']} ({$currentPage['url']})</div>";
    } else {
        throw new Exception("Valid page array access failed");
    }

    // Test invalid page ID
    $pageId = 999;
    $filteredPages = array_filter($pages, function($p) use ($pageId) { return $p['id'] == $pageId; });
    $currentPage = reset($filteredPages);

    if ($currentPage === false || !is_array($currentPage) || !isset($currentPage['id'])) {
        echo "<div class='success'>✓ Invalid page ID handling working correctly</div>";
    } else {
        throw new Exception("Invalid page ID should return false");
    }

} catch (Exception $e) {
    echo "<div class='error'>✗ SEO page array access failed: " . $e->getMessage() . "</div>";
    $allTestsPassed = false;
}

// Test 3: htmlspecialchars with null values
echo "<h2>Test 3: htmlspecialchars Null Value Handling</h2>";
try {
    // Test the fixed htmlspecialchars calls
    $testData = [
        'name' => 'John Doe',
        'email' => '<EMAIL>',
        'phone' => null,
        'notes' => null,
        'message' => 'Test message'
    ];

    // Test each field with the new syntax
    $name = htmlspecialchars($testData['name'] ?? '', ENT_QUOTES, 'UTF-8');
    $email = htmlspecialchars($testData['email'] ?? '', ENT_QUOTES, 'UTF-8');
    $phone = htmlspecialchars($testData['phone'] ?? '', ENT_QUOTES, 'UTF-8');
    $notes = htmlspecialchars($testData['notes'] ?? '', ENT_QUOTES, 'UTF-8');
    $message = htmlspecialchars($testData['message'] ?? '', ENT_QUOTES, 'UTF-8');

    echo "<div class='success'>✓ htmlspecialchars with null values working correctly</div>";
    echo "<div class='info'>All fields processed without deprecation warnings</div>";
    echo "<pre>Name: '$name'\nEmail: '$email'\nPhone: '$phone'\nNotes: '$notes'\nMessage: '$message'</pre>";

} catch (Exception $e) {
    echo "<div class='error'>✗ htmlspecialchars null handling failed: " . $e->getMessage() . "</div>";
    $allTestsPassed = false;
}

// Test 4: Database Schema Verification
echo "<h2>Test 4: Database Schema Verification</h2>";
try {
    // Check projects table structure
    $projectsColumns = $db->fetchAll("DESCRIBE projects");
    $hasProjectType = false;
    foreach ($projectsColumns as $column) {
        if ($column['Field'] === 'project_type') {
            $hasProjectType = true;
            break;
        }
    }

    if ($hasProjectType) {
        echo "<div class='success'>✓ Projects table has 'project_type' column</div>";
    } else {
        echo "<div class='error'>✗ Projects table missing 'project_type' column</div>";
        $allTestsPassed = false;
    }

    // Check contact_inquiries table structure
    $inquiriesColumns = $db->fetchAll("DESCRIBE contact_inquiries");
    $hasStatus = false;
    foreach ($inquiriesColumns as $column) {
        if ($column['Field'] === 'status') {
            $hasStatus = true;
            break;
        }
    }

    if ($hasStatus) {
        echo "<div class='success'>✓ Contact_inquiries table has 'status' column</div>";
    } else {
        echo "<div class='error'>✗ Contact_inquiries table missing 'status' column</div>";
        $allTestsPassed = false;
    }

} catch (Exception $e) {
    echo "<div class='error'>✗ Database schema verification failed: " . $e->getMessage() . "</div>";
    $allTestsPassed = false;
}

// Final Results
echo "<h2>Test Results Summary</h2>";
if ($allTestsPassed) {
    echo "<div class='success'>";
    echo "<h3>🎉 All Tests Passed!</h3>";
    echo "<p>All identified errors have been successfully fixed:</p>";
    echo "<ul>";
    echo "<li>✓ Analytics database queries use correct column names</li>";
    echo "<li>✓ SEO page array access is properly validated</li>";
    echo "<li>✓ htmlspecialchars calls handle null values correctly</li>";
    echo "<li>✓ Database schema is properly structured</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='error'>";
    echo "<h3>❌ Some Tests Failed</h3>";
    echo "<p>Please review the failed tests above and address any remaining issues.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<div class='info'>";
echo "<p><strong>Recommended actions:</strong></p>";
echo "<ul>";
echo "<li>Visit <a href='admin/analytics.php'>Analytics Dashboard</a> to verify it loads without errors</li>";
echo "<li>Visit <a href='admin/seo.php'>SEO Management</a> to test page selection</li>";
echo "<li>Visit <a href='admin/inquiries.php'>Inquiries Management</a> to verify no deprecation warnings</li>";
echo "<li>Check Apache error logs for any remaining issues</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
