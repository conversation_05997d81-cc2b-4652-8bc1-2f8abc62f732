/**
 * Projects Manager for Flori Construction Mobile App
 * Handles project listing, creation, editing, and management
 */

class ProjectsManager {
    constructor() {
        this.projects = [];
        this.currentPage = 1;
        this.totalPages = 1;
        this.filters = {
            type: '',
            search: '',
            featured: ''
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Add project button
        const addProjectBtn = document.getElementById('add-project-btn');
        addProjectBtn?.addEventListener('click', () => {
            this.showAddProjectModal();
        });
        
        // Project type filter
        const typeFilter = document.getElementById('project-type-filter');
        typeFilter?.addEventListener('change', (e) => {
            this.filters.type = e.target.value;
            this.currentPage = 1;
            this.load();
        });
        
        // Search input
        const searchInput = document.getElementById('project-search');
        let searchTimeout;
        searchInput?.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.filters.search = e.target.value;
                this.currentPage = 1;
                this.load();
            }, 500);
        });
    }
    
    async load() {
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: 20,
                ...this.filters
            });
            
            const data = await window.floriAdmin.apiRequest(`mobile.php?action=projects&${params}`);
            
            if (data && data.success) {
                this.projects = data.data.projects;
                this.updatePagination(data.data.pagination);
                this.renderProjects();
            } else {
                window.floriAdmin.showToast('Failed to load projects', 'error');
            }
        } catch (error) {
            console.error('Failed to load projects:', error);
            window.floriAdmin.showToast('Failed to load projects', 'error');
        }
    }
    
    renderProjects() {
        const container = document.getElementById('projects-list');
        if (!container) return;
        
        if (this.projects.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-building"></i>
                    <h3>No projects found</h3>
                    <p>Start by adding your first project</p>
                    <button class="btn btn-primary" onclick="window.ProjectsManager.showAddProjectModal()">
                        <i class="fas fa-plus"></i> Add Project
                    </button>
                </div>
            `;
            return;
        }
        
        const html = this.projects.map(project => `
            <div class="project-card" data-id="${project.id}">
                <div class="project-image">
                    ${project.featured_image ? 
                        `<img src="../${project.featured_image}" alt="${window.floriAdmin.escapeHtml(project.title)}" loading="lazy">` :
                        `<div class="project-placeholder"><i class="fas fa-building"></i></div>`
                    }
                    ${project.is_featured ? '<span class="featured-badge">Featured</span>' : ''}
                </div>
                
                <div class="project-content">
                    <h3>${window.floriAdmin.escapeHtml(project.title)}</h3>
                    <p class="project-location">
                        <i class="fas fa-map-marker-alt"></i>
                        ${window.floriAdmin.escapeHtml(project.location || 'No location')}
                    </p>
                    <p class="project-description">${window.floriAdmin.escapeHtml(project.short_description || project.description || '').substring(0, 100)}...</p>
                    
                    <div class="project-meta">
                        <span class="project-type ${project.project_type}">${project.project_type}</span>
                        <span class="project-date">${window.floriAdmin.formatDate(project.created_at)}</span>
                    </div>
                </div>
                
                <div class="project-actions">
                    <button class="btn btn-sm btn-ghost" onclick="window.ProjectsManager.editProject(${project.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-ghost" onclick="window.ProjectsManager.viewProject(${project.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="window.ProjectsManager.deleteProject(${project.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
    }
    
    updatePagination(pagination) {
        this.totalPages = pagination.pages;
        const container = document.getElementById('projects-pagination');
        if (!container) return;
        
        if (pagination.pages <= 1) {
            container.innerHTML = '';
            return;
        }
        
        let html = '<div class="pagination-controls">';
        
        // Previous button
        if (pagination.page > 1) {
            html += `<button class="btn btn-sm" onclick="window.ProjectsManager.goToPage(${pagination.page - 1})">
                <i class="fas fa-chevron-left"></i> Previous
            </button>`;
        }
        
        // Page numbers
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const active = i === pagination.page ? 'active' : '';
            html += `<button class="btn btn-sm ${active}" onclick="window.ProjectsManager.goToPage(${i})">${i}</button>`;
        }
        
        // Next button
        if (pagination.page < pagination.pages) {
            html += `<button class="btn btn-sm" onclick="window.ProjectsManager.goToPage(${pagination.page + 1})">
                Next <i class="fas fa-chevron-right"></i>
            </button>`;
        }
        
        html += '</div>';
        container.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.load();
    }
    
    showAddProjectModal() {
        const modalContent = `
            <div class="modal-header">
                <h2>Add New Project</h2>
                <button class="modal-close" onclick="window.floriAdmin.closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="add-project-form" class="modal-form">
                <div class="form-group">
                    <label for="project-title">Project Title *</label>
                    <input type="text" id="project-title" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="project-location">Location</label>
                    <input type="text" id="project-location" name="location">
                </div>
                
                <div class="form-group">
                    <label for="project-type">Project Type</label>
                    <select id="project-type" name="project_type">
                        <option value="completed">Completed</option>
                        <option value="ongoing">Ongoing</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="project-description">Description</label>
                    <textarea id="project-description" name="description" rows="4"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="project-short-description">Short Description</label>
                    <textarea id="project-short-description" name="short_description" rows="2"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="client-name">Client Name</label>
                    <input type="text" id="client-name" name="client_name">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="start-date">Start Date</label>
                        <input type="date" id="start-date" name="start_date">
                    </div>
                    <div class="form-group">
                        <label for="end-date">End Date</label>
                        <input type="date" id="end-date" name="end_date">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="project-value">Project Value</label>
                    <input type="number" id="project-value" name="project_value" step="0.01">
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="is-featured" name="is_featured">
                        <span class="checkmark"></span>
                        Featured Project
                    </label>
                </div>
                
                <div class="modal-actions">
                    <button type="button" class="btn btn-ghost" onclick="window.floriAdmin.closeModal()">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Project
                    </button>
                </div>
            </form>
        `;
        
        window.floriAdmin.showModal(modalContent);
        
        // Setup form submission
        document.getElementById('add-project-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveProject();
        });
    }
    
    async saveProject(projectId = null) {
        const form = document.getElementById('add-project-form');
        const formData = new FormData(form);
        
        const projectData = {
            title: formData.get('title'),
            location: formData.get('location'),
            project_type: formData.get('project_type'),
            description: formData.get('description'),
            short_description: formData.get('short_description'),
            client_name: formData.get('client_name'),
            start_date: formData.get('start_date') || null,
            end_date: formData.get('end_date') || null,
            project_value: formData.get('project_value') || null,
            is_featured: formData.get('is_featured') ? 1 : 0
        };
        
        if (projectId) {
            projectData.id = projectId;
        }
        
        try {
            const action = projectId ? 'PUT' : 'POST';
            const endpoint = `mobile.php?action=project`;
            
            const data = await window.floriAdmin.apiRequest(endpoint, {
                method: action,
                body: JSON.stringify(projectData)
            });
            
            if (data && data.success) {
                window.floriAdmin.closeModal();
                window.floriAdmin.showToast(
                    projectId ? 'Project updated successfully' : 'Project created successfully',
                    'success'
                );
                this.load();
            } else {
                window.floriAdmin.showToast(data?.error || 'Failed to save project', 'error');
            }
        } catch (error) {
            console.error('Failed to save project:', error);
            window.floriAdmin.showToast('Failed to save project', 'error');
        }
    }
    
    async editProject(projectId) {
        try {
            const data = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`);
            
            if (data && data.success) {
                this.showEditProjectModal(data.data.project);
            } else {
                window.floriAdmin.showToast('Failed to load project details', 'error');
            }
        } catch (error) {
            console.error('Failed to load project:', error);
            window.floriAdmin.showToast('Failed to load project details', 'error');
        }
    }
    
    showEditProjectModal(project) {
        // Similar to showAddProjectModal but with pre-filled data
        // Implementation continues in next part due to length limit
    }
    
    async deleteProject(projectId) {
        if (!confirm('Are you sure you want to delete this project?')) {
            return;
        }
        
        try {
            const data = await window.floriAdmin.apiRequest(`mobile.php?action=project&id=${projectId}`, {
                method: 'DELETE'
            });
            
            if (data && data.success) {
                window.floriAdmin.showToast('Project deleted successfully', 'success');
                this.load();
            } else {
                window.floriAdmin.showToast('Failed to delete project', 'error');
            }
        } catch (error) {
            console.error('Failed to delete project:', error);
            window.floriAdmin.showToast('Failed to delete project', 'error');
        }
    }
    
    async viewProject(projectId) {
        // Navigate to project detail view
        // This could open a modal or navigate to a detail page
        console.log('View project:', projectId);
    }
}

// Initialize Projects Manager
window.ProjectsManager = new ProjectsManager();
