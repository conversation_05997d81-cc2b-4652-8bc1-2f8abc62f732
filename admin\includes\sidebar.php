<?php
/**
 * Admin Sidebar Navigation
 * Flori Construction Ltd
 * Enhanced with better spacing and responsive design
 */

// Get current page for active menu highlighting
$currentPage = basename($_SERVER['PHP_SELF']);
$user = getCurrentUser();

// Define menu sections with grouped items for better organization
$menuSections = [
    'main' => [
        'title' => 'Main',
        'items' => [
            [
                'file' => 'index.php',
                'icon' => 'fas fa-tachometer-alt',
                'title' => 'Dashboard',
                'roles' => ['admin', 'editor']
            ]
        ]
    ],
    'content' => [
        'title' => 'Content Management',
        'items' => [
            [
                'file' => 'projects.php',
                'icon' => 'fas fa-building',
                'title' => 'Projects',
                'roles' => ['admin', 'editor']
            ],
            [
                'file' => 'services.php',
                'icon' => 'fas fa-tools',
                'title' => 'Services',
                'roles' => ['admin', 'editor']
            ],
            [
                'file' => 'media.php',
                'icon' => 'fas fa-images',
                'title' => 'Media',
                'roles' => ['admin', 'editor']
            ],
            [
                'file' => 'content.php',
                'icon' => 'fas fa-edit',
                'title' => 'Content',
                'roles' => ['admin', 'editor']
            ],
            [
                'file' => 'testimonials.php',
                'icon' => 'fas fa-star',
                'title' => 'Testimonials',
                'roles' => ['admin', 'editor']
            ]
        ]
    ],
    'communication' => [
        'title' => 'Communication',
        'items' => [
            [
                'file' => 'inquiries.php',
                'icon' => 'fas fa-envelope',
                'title' => 'Inquiries',
                'roles' => ['admin', 'editor']
            ]
        ]
    ],
    'analytics' => [
        'title' => 'Analytics & SEO',
        'items' => [
            [
                'file' => 'analytics.php',
                'icon' => 'fas fa-chart-line',
                'title' => 'Analytics',
                'roles' => ['admin', 'editor']
            ],
            [
                'file' => 'seo.php',
                'icon' => 'fas fa-search',
                'title' => 'SEO',
                'roles' => ['admin', 'editor']
            ]
        ]
    ],
    'admin' => [
        'title' => 'Administration',
        'items' => [
            [
                'file' => 'branding.php',
                'icon' => 'fas fa-palette',
                'title' => 'Branding',
                'roles' => ['admin']
            ],
            [
                'file' => 'email-test.php',
                'icon' => 'fas fa-envelope-open-text',
                'title' => 'Email Test',
                'roles' => ['admin']
            ]
        ]
    ]
];

// Filter menu sections and items based on user role
$allowedMenuSections = [];
foreach ($menuSections as $sectionKey => $section) {
    $allowedItems = array_filter($section['items'], function($item) use ($user) {
        return in_array($user['role'], $item['roles']);
    });

    if (!empty($allowedItems)) {
        $allowedMenuSections[$sectionKey] = [
            'title' => $section['title'],
            'items' => $allowedItems
        ];
    }
}
?>

<!-- Modern Sidebar - Hirelly Style -->
<nav class="admin-sidebar" role="navigation" aria-label="Admin Navigation">
    <!-- Sidebar Brand -->
    <div class="sidebar-brand">
        <div class="brand-logo">
            <i class="fas fa-hammer"></i>
        </div>
        <span class="brand-text">Flori Construction</span>
    </div>

    <!-- Sidebar Menu -->
    <div class="sidebar-menu-container">
        <ul class="sidebar-menu" role="menubar">
            <?php foreach ($allowedMenuSections as $sectionKey => $section): ?>
                <?php foreach ($section['items'] as $item): ?>
                    <li class="sidebar-menu-item<?= $currentPage === $item['file'] ? ' active' : '' ?>" role="none">
                        <a href="<?= $item['file'] ?>"
                           class="sidebar-menu-link"
                           role="menuitem"
                           aria-current="<?= $currentPage === $item['file'] ? 'page' : 'false' ?>"
                           title="<?= htmlspecialchars($item['title']) ?>">
                            <span class="menu-icon">
                                <i class="<?= $item['icon'] ?>" aria-hidden="true"></i>
                            </span>
                            <span class="menu-text"><?= htmlspecialchars($item['title']) ?></span>
                        </a>
                    </li>
                <?php endforeach; ?>
            <?php endforeach; ?>
        </ul>
    </div>


</nav>
