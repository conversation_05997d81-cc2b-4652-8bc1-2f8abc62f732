<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator - Flori Construction</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .icon-item canvas {
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 10px;
        }
        .icon-item h4 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        .icon-item p {
            margin: 0;
            font-size: 12px;
            color: #666;
        }
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        .btn {
            background: linear-gradient(45deg, #e74c3c, #f39c12);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
        }
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .instructions h3 {
            color: #1976d2;
            margin-top: 0;
        }
        .instructions ol {
            margin: 15px 0;
        }
        .instructions li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ Flori Construction PWA Icons</h1>
        
        <div class="controls">
            <button class="btn" onclick="generateIcons()">Generate Icons</button>
            <button class="btn" onclick="downloadAll()">Download All</button>
        </div>
        
        <div class="icon-preview" id="iconPreview">
            <!-- Icons will be generated here -->
        </div>
        
        <div class="instructions">
            <h3>📋 Instructions</h3>
            <ol>
                <li>Click "Generate Icons" to create all required PWA icons</li>
                <li>Click "Download All" to download a ZIP file with all icons</li>
                <li>Extract the ZIP file to the <code>mobile-app/icons/</code> directory</li>
                <li>The icons are automatically optimized for different devices and purposes</li>
                <li>Icons include the Flori Construction logo with proper sizing and padding</li>
            </ol>
            
            <p><strong>Note:</strong> These icons are generated using the company colors (#e74c3c, #f39c12) and include the "FC" monogram for Flori Construction.</p>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script>
        const iconSizes = [
            { size: 72, name: 'icon-72x72.png', purpose: 'notification' },
            { size: 96, name: 'icon-96x96.png', purpose: 'shortcut' },
            { size: 128, name: 'icon-128x128.png', purpose: 'app' },
            { size: 144, name: 'icon-144x144.png', purpose: 'app' },
            { size: 152, name: 'icon-152x152.png', purpose: 'apple-touch' },
            { size: 192, name: 'icon-192x192.png', purpose: 'app' },
            { size: 384, name: 'icon-384x384.png', purpose: 'app' },
            { size: 512, name: 'icon-512x512.png', purpose: 'app' }
        ];
        
        const shortcutIcons = [
            { size: 96, name: 'shortcut-add.png', icon: '➕', purpose: 'Add Project' },
            { size: 96, name: 'shortcut-upload.png', icon: '📤', purpose: 'Upload Media' }
        ];
        
        let generatedIcons = {};
        
        function generateIcons() {
            const container = document.getElementById('iconPreview');
            container.innerHTML = '';
            generatedIcons = {};
            
            // Generate main app icons
            iconSizes.forEach(iconConfig => {
                const canvas = createIcon(iconConfig.size, 'FC', iconConfig.purpose);
                generatedIcons[iconConfig.name] = canvas;
                
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <canvas width="${iconConfig.size}" height="${iconConfig.size}"></canvas>
                    <h4>${iconConfig.size}x${iconConfig.size}</h4>
                    <p>${iconConfig.purpose}</p>
                `;
                
                const displayCanvas = iconItem.querySelector('canvas');
                const ctx = displayCanvas.getContext('2d');
                ctx.drawImage(canvas, 0, 0);
                
                container.appendChild(iconItem);
            });
            
            // Generate shortcut icons
            shortcutIcons.forEach(iconConfig => {
                const canvas = createShortcutIcon(iconConfig.size, iconConfig.icon, iconConfig.purpose);
                generatedIcons[iconConfig.name] = canvas;
                
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                iconItem.innerHTML = `
                    <canvas width="${iconConfig.size}" height="${iconConfig.size}"></canvas>
                    <h4>${iconConfig.name}</h4>
                    <p>${iconConfig.purpose}</p>
                `;
                
                const displayCanvas = iconItem.querySelector('canvas');
                const ctx = displayCanvas.getContext('2d');
                ctx.drawImage(canvas, 0, 0);
                
                container.appendChild(iconItem);
            });
        }
        
        function createIcon(size, text, purpose) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create gradient background
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#e74c3c');
            gradient.addColorStop(1, '#f39c12');
            
            // Draw background
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Add subtle shadow/depth
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, size * 0.8, size, size * 0.2);
            
            // Draw text
            ctx.fillStyle = 'white';
            ctx.font = `bold ${size * 0.4}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // Add text shadow
            ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
            ctx.shadowBlur = size * 0.02;
            ctx.shadowOffsetX = size * 0.01;
            ctx.shadowOffsetY = size * 0.01;
            
            ctx.fillText(text, size / 2, size / 2);
            
            // Add construction-themed icon for larger sizes
            if (size >= 192) {
                ctx.shadowColor = 'transparent';
                ctx.font = `${size * 0.15}px Arial, sans-serif`;
                ctx.fillText('🏗️', size / 2, size * 0.75);
            }
            
            return canvas;
        }
        
        function createShortcutIcon(size, emoji, purpose) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            
            // Create circular background
            const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            gradient.addColorStop(0, '#f39c12');
            gradient.addColorStop(1, '#e74c3c');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add border
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // Draw emoji/icon
            ctx.font = `${size * 0.5}px Arial, sans-serif`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'white';
            
            ctx.fillText(emoji, size / 2, size / 2);
            
            return canvas;
        }
        
        async function downloadAll() {
            if (Object.keys(generatedIcons).length === 0) {
                alert('Please generate icons first!');
                return;
            }
            
            const zip = new JSZip();
            
            // Add all icons to zip
            for (const [filename, canvas] of Object.entries(generatedIcons)) {
                const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
                zip.file(filename, blob);
            }
            
            // Add README
            const readme = `# Flori Construction PWA Icons

These icons were generated for the Flori Construction mobile app (PWA).

## Files included:
- icon-72x72.png through icon-512x512.png (main app icons)
- shortcut-add.png (add project shortcut)
- shortcut-upload.png (upload media shortcut)

## Installation:
1. Copy all PNG files to the mobile-app/icons/ directory
2. The manifest.json file already references these icons
3. Clear browser cache and reinstall the PWA to see new icons

## Colors used:
- Primary: #e74c3c (red)
- Secondary: #f39c12 (orange)
- Text: white with shadow

Generated on: ${new Date().toLocaleString()}
`;
            
            zip.file('README.md', readme);
            
            // Generate and download zip
            const content = await zip.generateAsync({ type: 'blob' });
            const url = URL.createObjectURL(content);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'flori-construction-pwa-icons.zip';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        // Generate icons on page load
        window.addEventListener('load', generateIcons);
    </script>
</body>
</html>
