<?php
/**
 * Responsive Design Test Page
 * Tests admin dashboard layout across different screen sizes
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Design Test - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 0.5rem;
        }
        
        .screen-size-indicator {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            z-index: 10001;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .responsive-test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .test-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }
        
        .breakpoint-info {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            border-left: 4px solid #3498db;
        }
        
        @media (max-width: 1024px) {
            .screen-size-indicator { background: #f39c12; }
        }
        
        @media (max-width: 768px) {
            .screen-size-indicator { background: #e74c3c; }
        }
        
        @media (max-width: 480px) {
            .screen-size-indicator { background: #9b59b6; }
        }
    </style>
</head>
<body>
    <div class="screen-size-indicator" id="screenIndicator">
        Desktop (>1024px)
    </div>

    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <div class="test-section">
                    <h2 class="test-title">
                        <i class="fas fa-mobile-alt"></i>
                        Responsive Design Test
                    </h2>
                    
                    <div class="breakpoint-info">
                        <h3>Current Breakpoints:</h3>
                        <ul>
                            <li><strong>Desktop:</strong> > 1024px (Blue indicator)</li>
                            <li><strong>Tablet:</strong> ≤ 1024px (Orange indicator)</li>
                            <li><strong>Mobile:</strong> ≤ 768px (Red indicator)</li>
                            <li><strong>Small Mobile:</strong> ≤ 480px (Purple indicator)</li>
                        </ul>
                    </div>
                </div>

                <!-- Statistics Grid Test -->
                <div class="test-section">
                    <h3 class="test-title">Statistics Grid Responsiveness</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3>150</h3>
                                <p>Total Users</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon completed">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3>45</h3>
                                <p>Completed</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon ongoing">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3>12</h3>
                                <p>In Progress</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon media">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="stat-info">
                                <h3>89</h3>
                                <p>Media Files</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid Test -->
                <div class="test-section">
                    <h3 class="test-title">Dashboard Cards Layout</h3>
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h2>Recent Activity</h2>
                                <a href="#" class="btn btn-sm">View All</a>
                            </div>
                            <div class="card-content">
                                <p>This card tests the responsive behavior of dashboard components.</p>
                                <div class="responsive-test-grid">
                                    <div class="test-card">Item 1</div>
                                    <div class="test-card">Item 2</div>
                                    <div class="test-card">Item 3</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <div class="card-header">
                                <h2>System Status</h2>
                                <span class="badge badge-success">Online</span>
                            </div>
                            <div class="card-content">
                                <p>Testing card responsiveness and content flow.</p>
                                <div class="responsive-test-grid">
                                    <div class="test-card">Status A</div>
                                    <div class="test-card">Status B</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Test -->
                <div class="test-section">
                    <h3 class="test-title">Quick Actions Grid</h3>
                    <div class="actions-grid">
                        <a href="#" class="action-card">
                            <i class="fas fa-plus"></i>
                            <span>Add New</span>
                        </a>
                        <a href="#" class="action-card">
                            <i class="fas fa-edit"></i>
                            <span>Edit Content</span>
                        </a>
                        <a href="#" class="action-card">
                            <i class="fas fa-upload"></i>
                            <span>Upload Files</span>
                        </a>
                        <a href="#" class="action-card">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                        <a href="#" class="action-card">
                            <i class="fas fa-chart-bar"></i>
                            <span>Analytics</span>
                        </a>
                        <a href="#" class="action-card">
                            <i class="fas fa-users"></i>
                            <span>Users</span>
                        </a>
                    </div>
                </div>

                <!-- Form Elements Test -->
                <div class="test-section">
                    <h3 class="test-title">Form Elements Responsiveness</h3>
                    <form>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="test-input">Test Input</label>
                                    <input type="text" id="test-input" class="form-control" placeholder="Enter text">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="test-select">Test Select</label>
                                    <select id="test-select" class="form-control">
                                        <option>Option 1</option>
                                        <option>Option 2</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="test-textarea">Test Textarea</label>
                            <textarea id="test-textarea" class="form-control" rows="4" placeholder="Enter description"></textarea>
                        </div>
                        
                        <div class="d-flex gap-10">
                            <button type="submit" class="btn btn-success">Save Changes</button>
                            <button type="button" class="btn btn-outline">Cancel</button>
                        </div>
                    </form>
                </div>

                <!-- Navigation Test -->
                <div class="test-section">
                    <h3 class="test-title">Navigation Test</h3>
                    <p>Test the sidebar toggle functionality on mobile devices:</p>
                    <button type="button" class="btn" onclick="testSidebarToggle()">
                        <i class="fas fa-bars"></i> Toggle Sidebar
                    </button>
                    
                    <div class="mt-20">
                        <h4>Breadcrumb Test:</h4>
                        <nav class="breadcrumb">
                            <a href="#" class="breadcrumb-item">
                                <i class="fas fa-home"></i>
                                Dashboard
                            </a>
                            <span class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                            <a href="#" class="breadcrumb-item">Tests</a>
                            <span class="breadcrumb-separator">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                            <span class="breadcrumb-item current">Responsive</span>
                        </nav>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        // Screen size indicator
        function updateScreenIndicator() {
            const indicator = document.getElementById('screenIndicator');
            const width = window.innerWidth;
            
            if (width <= 480) {
                indicator.textContent = `Small Mobile (${width}px)`;
            } else if (width <= 768) {
                indicator.textContent = `Mobile (${width}px)`;
            } else if (width <= 1024) {
                indicator.textContent = `Tablet (${width}px)`;
            } else {
                indicator.textContent = `Desktop (${width}px)`;
            }
        }
        
        // Test sidebar toggle
        function testSidebarToggle() {
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            if (sidebarToggle) {
                sidebarToggle.click();
            }
        }
        
        // Update indicator on load and resize
        updateScreenIndicator();
        window.addEventListener('resize', updateScreenIndicator);
        
        // Test alert system
        setTimeout(() => {
            if (window.AdminJS && window.AdminJS.showAlert) {
                window.AdminJS.showAlert('info', 'Responsive design test loaded successfully!');
            }
        }, 1000);
    </script>
</body>
</html>
