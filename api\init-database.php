<?php
/**
 * Database Initialization Script
 * Ensures all required tables exist and creates missing ones
 */

require_once '../config/config.php';

header('Content-Type: application/json');

// Only allow admin access
$user = authenticateRequest();
if (!$user || $user['role'] !== 'admin') {
    jsonResponse(['error' => 'Admin access required'], 403);
}

try {
    global $db;
    
    $results = [];
    
    // Check and create missing tables
    $tables = [
        'users' => createUsersTable(),
        'api_tokens' => createApiTokensTable(),
        'projects' => createProjectsTable(),
        'media' => createMediaTable(),
        'content' => createContentTable(),
        'services' => createServicesTable(),
        'testimonials' => createTestimonialsTable(),
        'contact_inquiries' => createInquiriesTable(),
        'push_subscriptions' => createPushSubscriptionsTable(),
        'notification_history' => createNotificationHistoryTable()
    ];
    
    foreach ($tables as $tableName => $createFunction) {
        try {
            // Check if table exists
            $exists = $db->fetchOne("SHOW TABLES LIKE ?", [$tableName]);
            
            if (!$exists) {
                $createFunction();
                $results[$tableName] = 'Created';
            } else {
                $results[$tableName] = 'Exists';
            }
        } catch (Exception $e) {
            $results[$tableName] = 'Error: ' . $e->getMessage();
        }
    }
    
    // Add missing columns if needed
    $columnUpdates = addMissingColumns();
    
    jsonResponse([
        'success' => true,
        'tables' => $results,
        'column_updates' => $columnUpdates,
        'message' => 'Database initialization completed'
    ]);
    
} catch (Exception $e) {
    jsonResponse(['error' => 'Database initialization failed: ' . $e->getMessage()], 500);
}

function createUsersTable() {
    global $db;
    $sql = "CREATE TABLE `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL,
        `email` varchar(100) NOT NULL,
        `password_hash` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `role` enum('admin','editor','user') DEFAULT 'user',
        `is_active` tinyint(1) DEFAULT 1,
        `last_login` datetime DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `username` (`username`),
        UNIQUE KEY `email` (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createApiTokensTable() {
    global $db;
    $sql = "CREATE TABLE `api_tokens` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `token` varchar(255) NOT NULL,
        `expires_at` datetime NOT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `token` (`token`),
        KEY `user_id` (`user_id`),
        KEY `expires_at` (`expires_at`),
        FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createProjectsTable() {
    global $db;
    $sql = "CREATE TABLE `projects` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `slug` varchar(255) NOT NULL,
        `description` text,
        `short_description` varchar(500) DEFAULT NULL,
        `client_name` varchar(100) DEFAULT NULL,
        `location` varchar(255) DEFAULT NULL,
        `project_type` enum('completed','ongoing','upcoming') DEFAULT 'completed',
        `start_date` date DEFAULT NULL,
        `end_date` date DEFAULT NULL,
        `project_value` decimal(10,2) DEFAULT NULL,
        `featured_image` varchar(255) DEFAULT NULL,
        `gallery` json DEFAULT NULL,
        `is_featured` tinyint(1) DEFAULT 0,
        `is_active` tinyint(1) DEFAULT 1,
        `sort_order` int(11) DEFAULT 0,
        `meta_title` varchar(255) DEFAULT NULL,
        `meta_description` varchar(500) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`),
        KEY `project_type` (`project_type`),
        KEY `is_featured` (`is_featured`),
        KEY `is_active` (`is_active`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createMediaTable() {
    global $db;
    $sql = "CREATE TABLE `media` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `filename` varchar(255) NOT NULL,
        `original_name` varchar(255) NOT NULL,
        `file_path` varchar(500) NOT NULL,
        `file_type` varchar(100) NOT NULL,
        `file_size` bigint(20) NOT NULL,
        `mime_type` varchar(100) NOT NULL,
        `alt_text` varchar(255) DEFAULT NULL,
        `caption` text DEFAULT NULL,
        `uploaded_by` int(11) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `file_type` (`file_type`),
        KEY `uploaded_by` (`uploaded_by`),
        KEY `is_active` (`is_active`),
        KEY `created_at` (`created_at`),
        FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createContentTable() {
    global $db;
    $sql = "CREATE TABLE `content` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `slug` varchar(255) NOT NULL,
        `content` longtext,
        `content_type` enum('page','section','widget') DEFAULT 'page',
        `is_active` tinyint(1) DEFAULT 1,
        `sort_order` int(11) DEFAULT 0,
        `meta_title` varchar(255) DEFAULT NULL,
        `meta_description` varchar(500) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`),
        KEY `content_type` (`content_type`),
        KEY `is_active` (`is_active`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createServicesTable() {
    global $db;
    $sql = "CREATE TABLE `services` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `title` varchar(255) NOT NULL,
        `slug` varchar(255) NOT NULL,
        `description` text,
        `short_description` varchar(500) DEFAULT NULL,
        `icon` varchar(100) DEFAULT NULL,
        `featured_image` varchar(255) DEFAULT NULL,
        `is_active` tinyint(1) DEFAULT 1,
        `sort_order` int(11) DEFAULT 0,
        `meta_title` varchar(255) DEFAULT NULL,
        `meta_description` varchar(500) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `slug` (`slug`),
        KEY `is_active` (`is_active`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createTestimonialsTable() {
    global $db;
    $sql = "CREATE TABLE `testimonials` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `client_name` varchar(100) NOT NULL,
        `client_title` varchar(100) DEFAULT NULL,
        `client_company` varchar(100) DEFAULT NULL,
        `testimonial` text NOT NULL,
        `rating` tinyint(1) DEFAULT 5,
        `client_image` varchar(255) DEFAULT NULL,
        `is_approved` tinyint(1) DEFAULT 0,
        `sort_order` int(11) DEFAULT 0,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `is_approved` (`is_approved`),
        KEY `rating` (`rating`),
        KEY `sort_order` (`sort_order`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createInquiriesTable() {
    global $db;
    $sql = "CREATE TABLE `inquiries` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(100) NOT NULL,
        `email` varchar(100) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `subject` varchar(255) DEFAULT NULL,
        `message` text NOT NULL,
        `inquiry_type` enum('general','quote','support') DEFAULT 'general',
        `status` enum('new','read','replied','closed') DEFAULT 'new',
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `status` (`status`),
        KEY `inquiry_type` (`inquiry_type`),
        KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createPushSubscriptionsTable() {
    global $db;
    $sql = "CREATE TABLE `push_subscriptions` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `endpoint` text NOT NULL,
        `p256dh_key` varchar(255) NOT NULL,
        `auth_key` varchar(255) NOT NULL,
        `user_agent` text DEFAULT NULL,
        `device_type` enum('mobile','tablet','desktop') DEFAULT 'mobile',
        `is_active` tinyint(1) DEFAULT 1,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `is_active` (`is_active`),
        FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function createNotificationHistoryTable() {
    global $db;
    $sql = "CREATE TABLE `notification_history` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `title` varchar(255) NOT NULL,
        `body` text NOT NULL,
        `data` json DEFAULT NULL,
        `sent_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `read_at` timestamp NULL DEFAULT NULL,
        `sent_by` int(11) DEFAULT NULL,
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `sent_at` (`sent_at`),
        KEY `sent_by` (`sent_by`),
        FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
        FOREIGN KEY (`sent_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->query($sql);
}

function addMissingColumns() {
    global $db;
    $updates = [];
    
    try {
        // Add updated_at to media table if missing
        $columns = $db->fetchAll("SHOW COLUMNS FROM media LIKE 'updated_at'");
        if (empty($columns)) {
            $db->query("ALTER TABLE media ADD COLUMN updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            $updates[] = 'Added updated_at to media table';
        }
        
        // Add device_type to push_subscriptions if missing
        $columns = $db->fetchAll("SHOW COLUMNS FROM push_subscriptions LIKE 'device_type'");
        if (empty($columns)) {
            $db->query("ALTER TABLE push_subscriptions ADD COLUMN device_type enum('mobile','tablet','desktop') DEFAULT 'mobile'");
            $updates[] = 'Added device_type to push_subscriptions table';
        }
        
        // Add user_agent to push_subscriptions if missing
        $columns = $db->fetchAll("SHOW COLUMNS FROM push_subscriptions LIKE 'user_agent'");
        if (empty($columns)) {
            $db->query("ALTER TABLE push_subscriptions ADD COLUMN user_agent text DEFAULT NULL");
            $updates[] = 'Added user_agent to push_subscriptions table';
        }
        
    } catch (Exception $e) {
        $updates[] = 'Error adding columns: ' . $e->getMessage();
    }
    
    return $updates;
}

// Include authentication function
function authenticateRequest() {
    global $db;

    $token = getBearerToken();

    if (!$token) {
        return false;
    }

    $hashedToken = hash('sha256', $token);

    // Get token and user info
    $result = $db->fetchOne(
        "SELECT u.id, u.username, u.email, u.full_name, u.role, u.is_active, t.expires_at
         FROM users u
         JOIN api_tokens t ON u.id = t.user_id
         WHERE t.token = ? AND t.is_active = 1 AND u.is_active = 1",
        [$hashedToken]
    );

    if (!$result) {
        return false;
    }

    // Check if token is expired
    if (strtotime($result['expires_at']) < time()) {
        // Deactivate expired token
        $db->update('api_tokens',
            ['is_active' => 0],
            'token = ?',
            [$hashedToken]
        );
        return false;
    }

    return $result;
}

function getBearerToken() {
    $headers = getAuthorizationHeader();

    if (!empty($headers)) {
        if (preg_match('/Bearer\s(\S+)/', $headers, $matches)) {
            return $matches[1];
        }
    }

    return null;
}

function getAuthorizationHeader() {
    $headers = null;

    if (isset($_SERVER['Authorization'])) {
        $headers = trim($_SERVER["Authorization"]);
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $headers = trim($_SERVER["HTTP_AUTHORIZATION"]);
    } else if (function_exists('apache_request_headers')) {
        $requestHeaders = apache_request_headers();
        $requestHeaders = array_combine(array_map('ucwords', array_keys($requestHeaders)), array_values($requestHeaders));

        if (isset($requestHeaders['Authorization'])) {
            $headers = trim($requestHeaders['Authorization']);
        }
    }

    return $headers;
}
?>
