<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="100" viewBox="0 0 200 100" xmlns="http://www.w3.org/2000/svg">
  <!-- Simple test logo for Flori Construction -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#e74c3c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f39c12;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background rectangle -->
  <rect width="200" height="100" fill="url(#grad1)" rx="10" ry="10"/>
  
  <!-- Company name -->
  <text x="100" y="35" font-family="Arial, sans-serif" font-size="18" font-weight="bold" 
        text-anchor="middle" fill="white">FLORI</text>
  <text x="100" y="55" font-family="Arial, sans-serif" font-size="12" 
        text-anchor="middle" fill="white">CONSTRUCTION</text>
  
  <!-- Simple building icon -->
  <g transform="translate(85, 65)">
    <rect x="0" y="10" width="30" height="20" fill="white" opacity="0.8"/>
    <polygon points="0,10 15,0 30,10" fill="white" opacity="0.8"/>
    <rect x="5" y="15" width="4" height="8" fill="#2c3e50"/>
    <rect x="21" y="15" width="4" height="8" fill="#2c3e50"/>
    <rect x="12" y="20" width="6" height="10" fill="#2c3e50"/>
  </g>
</svg>
