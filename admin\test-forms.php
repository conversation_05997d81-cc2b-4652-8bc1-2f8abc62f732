<?php
/**
 * Form Submission and Loading States Test Page
 * Tests form functionality, validation, and loading states
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();

// Handle AJAX form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    // Simulate processing time
    sleep(2);
    
    $response = [
        'success' => true,
        'message' => 'Form submitted successfully!',
        'data' => $_POST
    ];
    
    // Simulate validation errors for testing
    if (isset($_POST['test_error'])) {
        $response = [
            'success' => false,
            'message' => 'Validation error: This is a test error.',
            'errors' => [
                'email' => 'Email format is invalid',
                'name' => 'Name is required'
            ]
        ];
    }
    
    echo json_encode($response);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Form Testing - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .form-test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .loading-demo {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .loading-demo:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .loading-demo.loading {
            background: #95a5a6;
        }
        
        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .validation-message {
            font-size: 14px;
            margin-top: 0.5rem;
            padding: 0.5rem;
            border-radius: 4px;
        }
        
        .validation-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .validation-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <div class="test-section">
                    <h1>
                        <i class="fas fa-clipboard-check"></i>
                        Form Submission & Loading States Test
                    </h1>
                    <p>Test various form submission scenarios, validation, and loading states.</p>
                </div>

                <!-- Basic Form Test -->
                <div class="test-section">
                    <h2>Basic Form Submission Test</h2>
                    <form id="basicForm" class="test-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="basic-name">Full Name</label>
                                    <input type="text" id="basic-name" name="name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="basic-email">Email</label>
                                    <input type="email" id="basic-email" name="email" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="basic-message">Message</label>
                            <textarea id="basic-message" name="message" class="form-control" rows="4"></textarea>
                        </div>
                        
                        <div class="d-flex gap-10">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane"></i>
                                Submit Form
                            </button>
                            <button type="button" class="btn btn-outline" onclick="resetForm('basicForm')">
                                Reset
                            </button>
                        </div>
                    </form>
                    
                    <div class="form-test-result" id="basicFormResult">
                        Form submission results will appear here...
                    </div>
                </div>

                <!-- AJAX Form Test -->
                <div class="test-section">
                    <h2>AJAX Form Submission Test</h2>
                    <form id="ajaxForm" class="test-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ajax-name">Full Name</label>
                                    <input type="text" id="ajax-name" name="name" class="form-control" required>
                                    <div class="validation-message" id="ajax-name-validation"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="ajax-email">Email</label>
                                    <input type="email" id="ajax-email" name="email" class="form-control" required>
                                    <div class="validation-message" id="ajax-email-validation"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="ajax-phone">Phone Number</label>
                            <input type="tel" id="ajax-phone" name="phone" class="form-control">
                        </div>
                        
                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" id="test-error" name="test_error" class="form-check-input">
                                <label for="test-error" class="form-check-label">
                                    Simulate validation error
                                </label>
                            </div>
                        </div>
                        
                        <div class="progress-bar" id="ajaxProgress" style="display: none;">
                            <div class="progress-fill" id="ajaxProgressFill"></div>
                        </div>
                        
                        <div class="d-flex gap-10">
                            <button type="submit" class="btn btn-success" id="ajaxSubmitBtn">
                                <i class="fas fa-cloud-upload-alt"></i>
                                Submit via AJAX
                            </button>
                            <button type="button" class="btn btn-outline" onclick="resetForm('ajaxForm')">
                                Reset
                            </button>
                        </div>
                    </form>
                    
                    <div class="form-test-result" id="ajaxFormResult">
                        AJAX form submission results will appear here...
                    </div>
                </div>

                <!-- Loading States Demo -->
                <div class="test-section">
                    <h2>Loading States Demo</h2>
                    <p>Test different loading states and button behaviors:</p>
                    
                    <div class="d-flex gap-10 mb-20" style="flex-wrap: wrap;">
                        <button class="loading-demo" onclick="simulateLoading(this, 2000)">
                            <i class="fas fa-save"></i>
                            Save Changes
                        </button>
                        
                        <button class="loading-demo" onclick="simulateLoading(this, 3000)">
                            <i class="fas fa-upload"></i>
                            Upload File
                        </button>
                        
                        <button class="loading-demo" onclick="simulateLoading(this, 1500)">
                            <i class="fas fa-sync"></i>
                            Refresh Data
                        </button>
                        
                        <button class="loading-demo" onclick="simulateLoading(this, 4000)">
                            <i class="fas fa-download"></i>
                            Generate Report
                        </button>
                    </div>
                    
                    <div class="form-test-result" id="loadingResults">
                        Loading state test results will appear here...
                    </div>
                </div>

                <!-- File Upload Test -->
                <div class="test-section">
                    <h2>File Upload Test</h2>
                    <form id="fileUploadForm" enctype="multipart/form-data">
                        <div class="file-upload">
                            <input type="file" id="test-file" name="file" multiple accept="image/*,.pdf,.doc,.docx">
                            <label for="test-file" class="file-upload-label">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <div>Click to select files or drag and drop</div>
                                <small>Supports images, PDF, and documents</small>
                            </label>
                        </div>
                        
                        <div id="fileList" class="mt-20"></div>
                        
                        <div class="progress-bar" id="uploadProgress" style="display: none;">
                            <div class="progress-fill" id="uploadProgressFill"></div>
                        </div>
                        
                        <button type="submit" class="btn btn-success mt-20" id="uploadBtn" disabled>
                            <i class="fas fa-upload"></i>
                            Upload Files
                        </button>
                    </form>
                    
                    <div class="form-test-result" id="fileUploadResult">
                        File upload results will appear here...
                    </div>
                </div>

                <!-- Real-time Validation Test -->
                <div class="test-section">
                    <h2>Real-time Validation Test</h2>
                    <form id="validationForm">
                        <div class="form-group">
                            <label for="username">Username (min 3 characters)</label>
                            <input type="text" id="username" name="username" class="form-control" minlength="3">
                            <div class="validation-message" id="username-validation"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">Password (min 8 characters, must include number)</label>
                            <input type="password" id="password" name="password" class="form-control" minlength="8">
                            <div class="validation-message" id="password-validation"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm-password">Confirm Password</label>
                            <input type="password" id="confirm-password" name="confirm_password" class="form-control">
                            <div class="validation-message" id="confirm-password-validation"></div>
                        </div>
                        
                        <button type="submit" class="btn btn-success" id="validationSubmitBtn" disabled>
                            <i class="fas fa-check"></i>
                            Submit (Validation Required)
                        </button>
                    </form>
                    
                    <div class="form-test-result" id="validationResult">
                        Real-time validation results will appear here...
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        // Form submission handlers
        document.addEventListener('DOMContentLoaded', function() {
            initFormTests();
        });
        
        function initFormTests() {
            // Basic form submission
            document.getElementById('basicForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleBasicFormSubmit(this);
            });
            
            // AJAX form submission
            document.getElementById('ajaxForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleAjaxFormSubmit(this);
            });
            
            // File upload form
            document.getElementById('fileUploadForm').addEventListener('submit', function(e) {
                e.preventDefault();
                handleFileUpload(this);
            });
            
            // Real-time validation
            initRealTimeValidation();
            
            // File input change
            document.getElementById('test-file').addEventListener('change', handleFileSelection);
        }
        
        function handleBasicFormSubmit(form) {
            const formData = new FormData(form);
            const result = document.getElementById('basicFormResult');
            
            result.innerHTML = 'Processing form submission...\n';
            
            // Simulate form processing
            setTimeout(() => {
                result.innerHTML += 'Form Data:\n';
                for (let [key, value] of formData.entries()) {
                    result.innerHTML += `${key}: ${value}\n`;
                }
                result.innerHTML += '\nForm submitted successfully!';
                
                if (window.AdminJS && window.AdminJS.showAlert) {
                    window.AdminJS.showAlert('success', 'Basic form submitted successfully!');
                }
            }, 1000);
        }
        
        function handleAjaxFormSubmit(form) {
            const formData = new FormData(form);
            const result = document.getElementById('ajaxFormResult');
            const submitBtn = document.getElementById('ajaxSubmitBtn');
            const progress = document.getElementById('ajaxProgress');
            const progressFill = document.getElementById('ajaxProgressFill');
            
            // Add AJAX flag
            formData.append('ajax', '1');
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="spinner"></div> Submitting...';
            progress.style.display = 'block';
            
            // Animate progress
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += 10;
                progressFill.style.width = progressValue + '%';
                if (progressValue >= 90) {
                    clearInterval(progressInterval);
                }
            }, 200);
            
            // Submit form
            fetch(window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                
                setTimeout(() => {
                    progress.style.display = 'none';
                    progressFill.style.width = '0%';
                    
                    result.innerHTML = JSON.stringify(data, null, 2);
                    
                    if (data.success) {
                        if (window.AdminJS && window.AdminJS.showAlert) {
                            window.AdminJS.showAlert('success', data.message);
                        }
                        clearValidationMessages();
                    } else {
                        if (window.AdminJS && window.AdminJS.showAlert) {
                            window.AdminJS.showAlert('error', data.message);
                        }
                        showValidationErrors(data.errors);
                    }
                    
                    // Reset button
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> Submit via AJAX';
                }, 500);
            })
            .catch(error => {
                console.error('Error:', error);
                result.innerHTML = 'Error: ' + error.message;
                
                if (window.AdminJS && window.AdminJS.showAlert) {
                    window.AdminJS.showAlert('error', 'Form submission failed');
                }
                
                // Reset button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-cloud-upload-alt"></i> Submit via AJAX';
                progress.style.display = 'none';
            });
        }
        
        function simulateLoading(button, duration) {
            const originalContent = button.innerHTML;
            const results = document.getElementById('loadingResults');
            
            button.disabled = true;
            button.classList.add('loading');
            button.innerHTML = '<div class="spinner"></div> Loading...';
            
            results.innerHTML += `Started loading: ${originalContent} (${duration}ms)\n`;
            
            setTimeout(() => {
                button.disabled = false;
                button.classList.remove('loading');
                button.innerHTML = originalContent;
                
                results.innerHTML += `Completed: ${originalContent}\n`;
                
                if (window.AdminJS && window.AdminJS.showAlert) {
                    window.AdminJS.showAlert('success', 'Operation completed successfully!');
                }
            }, duration);
        }
        
        function handleFileSelection(e) {
            const files = e.target.files;
            const fileList = document.getElementById('fileList');
            const uploadBtn = document.getElementById('uploadBtn');
            
            if (files.length > 0) {
                fileList.innerHTML = '<h4>Selected Files:</h4>';
                for (let file of files) {
                    fileList.innerHTML += `
                        <div style="padding: 0.5rem; background: #f8f9fa; margin: 0.25rem 0; border-radius: 4px;">
                            <strong>${file.name}</strong> (${formatFileSize(file.size)})
                        </div>
                    `;
                }
                uploadBtn.disabled = false;
            } else {
                fileList.innerHTML = '';
                uploadBtn.disabled = true;
            }
        }
        
        function handleFileUpload(form) {
            const result = document.getElementById('fileUploadResult');
            const progress = document.getElementById('uploadProgress');
            const progressFill = document.getElementById('uploadProgressFill');
            const uploadBtn = document.getElementById('uploadBtn');
            
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<div class="spinner"></div> Uploading...';
            progress.style.display = 'block';
            
            // Simulate upload progress
            let progressValue = 0;
            const uploadInterval = setInterval(() => {
                progressValue += Math.random() * 15;
                if (progressValue > 100) progressValue = 100;
                progressFill.style.width = progressValue + '%';
                
                if (progressValue >= 100) {
                    clearInterval(uploadInterval);
                    
                    setTimeout(() => {
                        progress.style.display = 'none';
                        progressFill.style.width = '0%';
                        uploadBtn.disabled = false;
                        uploadBtn.innerHTML = '<i class="fas fa-upload"></i> Upload Files';
                        
                        result.innerHTML = 'Files uploaded successfully!\n';
                        result.innerHTML += `Upload completed at: ${new Date().toLocaleTimeString()}`;
                        
                        if (window.AdminJS && window.AdminJS.showAlert) {
                            window.AdminJS.showAlert('success', 'Files uploaded successfully!');
                        }
                    }, 500);
                }
            }, 200);
        }
        
        function initRealTimeValidation() {
            const username = document.getElementById('username');
            const password = document.getElementById('password');
            const confirmPassword = document.getElementById('confirm-password');
            const submitBtn = document.getElementById('validationSubmitBtn');
            
            username.addEventListener('input', validateUsername);
            password.addEventListener('input', validatePassword);
            confirmPassword.addEventListener('input', validateConfirmPassword);
            
            function validateUsername() {
                const value = username.value;
                const validation = document.getElementById('username-validation');
                
                if (value.length === 0) {
                    validation.innerHTML = '';
                    validation.className = 'validation-message';
                } else if (value.length < 3) {
                    validation.innerHTML = 'Username must be at least 3 characters';
                    validation.className = 'validation-message validation-error';
                } else {
                    validation.innerHTML = 'Username looks good!';
                    validation.className = 'validation-message validation-success';
                }
                
                checkFormValidity();
            }
            
            function validatePassword() {
                const value = password.value;
                const validation = document.getElementById('password-validation');
                
                if (value.length === 0) {
                    validation.innerHTML = '';
                    validation.className = 'validation-message';
                } else if (value.length < 8) {
                    validation.innerHTML = 'Password must be at least 8 characters';
                    validation.className = 'validation-message validation-error';
                } else if (!/\d/.test(value)) {
                    validation.innerHTML = 'Password must include at least one number';
                    validation.className = 'validation-message validation-error';
                } else {
                    validation.innerHTML = 'Password is strong!';
                    validation.className = 'validation-message validation-success';
                }
                
                // Re-validate confirm password if it has a value
                if (confirmPassword.value) {
                    validateConfirmPassword();
                }
                
                checkFormValidity();
            }
            
            function validateConfirmPassword() {
                const value = confirmPassword.value;
                const validation = document.getElementById('confirm-password-validation');
                
                if (value.length === 0) {
                    validation.innerHTML = '';
                    validation.className = 'validation-message';
                } else if (value !== password.value) {
                    validation.innerHTML = 'Passwords do not match';
                    validation.className = 'validation-message validation-error';
                } else {
                    validation.innerHTML = 'Passwords match!';
                    validation.className = 'validation-message validation-success';
                }
                
                checkFormValidity();
            }
            
            function checkFormValidity() {
                const isValid = username.value.length >= 3 && 
                               password.value.length >= 8 && 
                               /\d/.test(password.value) && 
                               confirmPassword.value === password.value;
                
                submitBtn.disabled = !isValid;
                
                const result = document.getElementById('validationResult');
                result.innerHTML = `Form validity: ${isValid ? 'VALID' : 'INVALID'}\n`;
                result.innerHTML += `Last check: ${new Date().toLocaleTimeString()}`;
            }
        }
        
        function showValidationErrors(errors) {
            if (!errors) return;
            
            for (let field in errors) {
                const validation = document.getElementById(`ajax-${field}-validation`);
                if (validation) {
                    validation.innerHTML = errors[field];
                    validation.className = 'validation-message validation-error';
                }
            }
        }
        
        function clearValidationMessages() {
            const validations = document.querySelectorAll('#ajaxForm .validation-message');
            validations.forEach(validation => {
                validation.innerHTML = '';
                validation.className = 'validation-message';
            });
        }
        
        function resetForm(formId) {
            const form = document.getElementById(formId);
            form.reset();
            
            // Clear validation messages
            const validations = form.querySelectorAll('.validation-message');
            validations.forEach(validation => {
                validation.innerHTML = '';
                validation.className = 'validation-message';
            });
            
            // Reset file list if it's the file upload form
            if (formId === 'fileUploadForm') {
                document.getElementById('fileList').innerHTML = '';
                document.getElementById('uploadBtn').disabled = true;
            }
            
            if (window.AdminJS && window.AdminJS.showAlert) {
                window.AdminJS.showAlert('info', 'Form has been reset');
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
