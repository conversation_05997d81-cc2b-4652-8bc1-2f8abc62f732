<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flori Construction - Admin App</title>

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#e74c3c">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Flori Admin">

    <!-- Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Icons -->
    <link rel="icon" type="image/png" sizes="192x192" href="icons/icon-192x192.png">
    <link rel="apple-touch-icon" href="icons/icon-192x192.png">

    <!-- CSS -->
    <link rel="stylesheet" href="css/app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>Loading Flori Admin...</p>
    </div>

    <!-- Login Screen -->
    <div id="login-screen" class="screen">
        <div class="login-container">
            <div class="logo">
                <img src="../assets/images/logo.png" alt="Flori Construction">
            </div>
            <h1>Admin Login</h1>

            <form id="login-form" class="login-form">
                <div class="form-group">
                    <label for="username">Username or Email</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            </form>

            <div id="login-error" class="error-message" style="display: none;"></div>
        </div>
    </div>

    <!-- Main App -->
    <div id="main-app" class="screen" style="display: none;">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <button id="menu-toggle" class="menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="page-title">Dashboard</h1>
            </div>
            <div class="header-right">
                <button id="logout-btn" class="btn btn-ghost">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </header>

        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <img src="../assets/images/logo.png" alt="Flori Construction">
                <span>Admin Panel</span>
            </div>

            <ul class="sidebar-menu">
                <li>
                    <a href="#dashboard" class="menu-item active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="#projects" class="menu-item" data-page="projects">
                        <i class="fas fa-building"></i>
                        <span>Projects</span>
                    </a>
                </li>
                <li>
                    <a href="#media" class="menu-item" data-page="media">
                        <i class="fas fa-images"></i>
                        <span>Media</span>
                    </a>
                </li>
                <li>
                    <a href="#content" class="menu-item" data-page="content">
                        <i class="fas fa-edit"></i>
                        <span>Content</span>
                    </a>
                </li>
                <li>
                    <a href="#settings" class="menu-item" data-page="settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Page -->
            <div id="dashboard-page" class="page active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-projects">0</h3>
                            <p>Total Projects</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="completed-projects">0</h3>
                            <p>Completed</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="ongoing-projects">0</h3>
                            <p>Ongoing</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-media">0</h3>
                            <p>Media Files</p>
                        </div>
                    </div>
                </div>

                <div class="recent-activity">
                    <h2>Recent Activity</h2>
                    <div id="recent-projects" class="activity-list">
                        <!-- Recent projects will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Projects Page -->
            <div id="projects-page" class="page">
                <div class="page-header">
                    <h2>Projects</h2>
                    <button id="add-project-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Project
                    </button>
                </div>

                <div class="filters">
                    <select id="project-type-filter">
                        <option value="">All Projects</option>
                        <option value="completed">Completed</option>
                        <option value="ongoing">Ongoing</option>
                    </select>

                    <input type="search" id="project-search" placeholder="Search projects...">
                </div>

                <div id="projects-list" class="projects-grid">
                    <!-- Projects will be loaded here -->
                </div>

                <div id="projects-pagination" class="pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>

            <!-- Media Page -->
            <div id="media-page" class="page">
                <div class="page-header">
                    <h2>Media</h2>
                    <button id="upload-media-btn" class="btn btn-primary">
                        <i class="fas fa-upload"></i> Upload Media
                    </button>
                </div>

                <div class="filters">
                    <select id="media-type-filter">
                        <option value="">All Media</option>
                        <option value="image">Images</option>
                        <option value="video">Videos</option>
                    </select>
                </div>

                <div id="media-grid" class="media-grid">
                    <!-- Media files will be loaded here -->
                </div>

                <div id="media-pagination" class="pagination">
                    <!-- Pagination will be loaded here -->
                </div>
            </div>

            <!-- Content Page -->
            <div id="content-page" class="page">
                <div class="page-header">
                    <h2>Content Management</h2>
                </div>

                <div id="content-sections" class="content-sections">
                    <!-- Content sections will be loaded here -->
                </div>
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page">
                <div class="page-header">
                    <h2>Settings</h2>
                </div>

                <div class="settings-sections">
                    <div class="settings-section">
                        <h3>Profile</h3>
                        <form id="profile-form">
                            <div class="form-group">
                                <label for="full-name">Full Name</label>
                                <input type="text" id="full-name" name="full_name">
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" name="email">
                            </div>

                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </form>
                    </div>

                    <div class="settings-section">
                        <h3>Change Password</h3>
                        <form id="password-form">
                            <div class="form-group">
                                <label for="current-password">Current Password</label>
                                <input type="password" id="current-password" name="current_password">
                            </div>

                            <div class="form-group">
                                <label for="new-password">New Password</label>
                                <input type="password" id="new-password" name="new_password">
                            </div>

                            <div class="form-group">
                                <label for="confirm-password">Confirm Password</label>
                                <input type="password" id="confirm-password" name="confirm_password">
                            </div>

                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="modal-overlay" class="modal-overlay">
        <div id="modal-content" class="modal-content">
            <!-- Modal content will be dynamically loaded -->
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- File Upload Input (Hidden) -->
    <input type="file" id="file-input" multiple accept="image/*,video/*" style="display: none;">

    <!-- JavaScript -->
    <script src="js/app.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/notifications.js"></script>
    <script src="js/projects.js"></script>
    <script src="js/media.js"></script>
    <script src="js/content.js"></script>
    <script src="js/sync.js"></script>
    <script src="js/offline.js"></script>
</body>

</html>