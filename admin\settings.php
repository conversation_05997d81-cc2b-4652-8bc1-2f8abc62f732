<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    $action = $_POST['action'] ?? '';

    if ($action === 'update_site_info') {
        $siteName = sanitize($_POST['site_name']);
        $siteDescription = sanitize($_POST['site_description']);
        $siteKeywords = sanitize($_POST['site_keywords']);
        $companyAddress = sanitize($_POST['company_address']);
        $companyPhone = sanitize($_POST['company_phone']);
        $companyMobile = sanitize($_POST['company_mobile']);
        $companyEmail = sanitize($_POST['company_email']);

        try {
            $siteInfo = [
                'site_name' => $siteName,
                'site_description' => $siteDescription,
                'site_keywords' => $siteKeywords,
                'company_address' => $companyAddress,
                'company_phone' => $companyPhone,
                'company_mobile' => $companyMobile,
                'company_email' => $companyEmail,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'site_info'");

            if ($existing) {
                $db->update('site_settings',
                    ['setting_value' => json_encode($siteInfo), 'updated_at' => date('Y-m-d H:i:s')],
                    'setting_key = ?',
                    ['site_info']
                );
            } else {
                $db->insert('site_settings', [
                    'setting_key' => 'site_info',
                    'setting_value' => json_encode($siteInfo),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $message = 'Site information updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating site information: ' . $e->getMessage();
            $messageType = 'error';
        }
    }

    if ($action === 'update_email_settings') {
        $smtpEnabled = isset($_POST['smtp_enabled']) ? 1 : 0;
        $smtpHost = sanitize($_POST['smtp_host']);
        $smtpPort = (int)$_POST['smtp_port'];
        $smtpUsername = sanitize($_POST['smtp_username']);
        $smtpPassword = $_POST['smtp_password']; // Don't sanitize password
        $fromEmail = sanitize($_POST['from_email']);
        $fromName = sanitize($_POST['from_name']);
        $autoReplyEnabled = isset($_POST['auto_reply_enabled']) ? 1 : 0;
        $notificationEmail = sanitize($_POST['notification_email']);

        try {
            $emailSettings = [
                'smtp_enabled' => $smtpEnabled,
                'smtp_host' => $smtpHost,
                'smtp_port' => $smtpPort,
                'smtp_username' => $smtpUsername,
                'smtp_password' => $smtpPassword,
                'from_email' => $fromEmail,
                'from_name' => $fromName,
                'auto_reply_enabled' => $autoReplyEnabled,
                'notification_email' => $notificationEmail,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'email_settings'");

            if ($existing) {
                $db->update('site_settings',
                    ['setting_value' => json_encode($emailSettings), 'updated_at' => date('Y-m-d H:i:s')],
                    'setting_key = ?',
                    ['email_settings']
                );
            } else {
                $db->insert('site_settings', [
                    'setting_key' => 'email_settings',
                    'setting_value' => json_encode($emailSettings),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $message = 'Email settings updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating email settings: ' . $e->getMessage();
            $messageType = 'error';
        }
    }

    if ($action === 'update_maintenance') {
        $maintenanceMode = isset($_POST['maintenance_mode']) ? 1 : 0;
        $maintenanceMessage = sanitize($_POST['maintenance_message']);

        try {
            $maintenanceSettings = [
                'maintenance_mode' => $maintenanceMode,
                'maintenance_message' => $maintenanceMessage,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'maintenance_settings'");

            if ($existing) {
                $db->update('site_settings',
                    ['setting_value' => json_encode($maintenanceSettings), 'updated_at' => date('Y-m-d H:i:s')],
                    'setting_key = ?',
                    ['maintenance_settings']
                );
            } else {
                $db->insert('site_settings', [
                    'setting_key' => 'maintenance_settings',
                    'setting_value' => json_encode($maintenanceSettings),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            }

            $message = 'Maintenance settings updated successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error updating maintenance settings: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get current settings
$siteInfo = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'site_info'");
$emailSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'email_settings'");
$maintenanceSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'maintenance_settings'");

$siteData = $siteInfo ? json_decode($siteInfo['setting_value'], true) : [
    'site_name' => SITE_NAME,
    'site_description' => 'Professional construction services in London',
    'site_keywords' => 'construction, london, civil engineering, groundworks, basements',
    'company_address' => SITE_ADDRESS,
    'company_phone' => SITE_PHONE,
    'company_mobile' => SITE_MOBILE,
    'company_email' => SITE_EMAIL
];

$emailData = $emailSettings ? json_decode($emailSettings['setting_value'], true) : [
    'smtp_enabled' => false,
    'smtp_host' => 'smtp.gmail.com',
    'smtp_port' => 587,
    'smtp_username' => '',
    'smtp_password' => '',
    'from_email' => SITE_EMAIL,
    'from_name' => SITE_NAME,
    'auto_reply_enabled' => true,
    'notification_email' => SITE_EMAIL
];

$maintenanceData = $maintenanceSettings ? json_decode($maintenanceSettings['setting_value'], true) : [
    'maintenance_mode' => false,
    'maintenance_message' => 'We are currently performing scheduled maintenance. Please check back soon.'
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Site Settings - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* Enhanced Settings Page Styles */
        .settings-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .settings-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .settings-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .settings-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .settings-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .settings-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .settings-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .settings-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .settings-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        .enhanced-settings-section {
            margin-bottom: var(--spacing-2xl);
        }

        .enhanced-settings-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            overflow: hidden;
            position: relative;
        }

        .enhanced-settings-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .enhanced-settings-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .enhanced-settings-card:hover::before {
            opacity: 1;
        }

        .enhanced-card-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--gray-200);
        }

        .enhanced-card-header h3 {
            margin: 0;
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .enhanced-card-content {
            padding: var(--spacing-xl);
        }

        .enhanced-form-group {
            margin-bottom: var(--spacing-xl);
        }

        .enhanced-form-group label {
            display: block;
            font-weight: var(--font-weight-medium);
            color: var(--gray-700);
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-base);
        }

        .enhanced-form-control {
            width: 100%;
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            transition: all var(--transition-base);
            background: var(--gray-50);
            font-size: var(--font-size-base);
        }

        .enhanced-form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: var(--white);
            outline: none;
        }

        .enhanced-form-control textarea {
            min-height: 100px;
            resize: vertical;
        }

        .enhanced-toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .enhanced-toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .enhanced-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gray-300);
            transition: var(--transition-base);
            border-radius: 34px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .enhanced-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background: var(--white);
            transition: var(--transition-base);
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        input:checked + .enhanced-slider {
            background: var(--primary-color);
        }

        input:checked + .enhanced-slider:before {
            transform: translateX(26px);
        }

        .enhanced-toggle-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--gray-50);
            border-radius: var(--border-radius-lg);
            border: 2px solid var(--gray-200);
            transition: all var(--transition-base);
        }

        .enhanced-toggle-group:hover {
            border-color: var(--primary-color);
            background: var(--white);
        }

        .enhanced-toggle-label {
            font-weight: var(--font-weight-medium);
            color: var(--gray-700);
            margin: 0;
            font-size: var(--font-size-base);
        }

        .enhanced-info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 2px solid #2196f3;
            border-left: 4px solid #2196f3;
            color: #0d47a1;
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            margin: var(--spacing-lg) 0;
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
        }

        .enhanced-info-box .info-icon {
            font-size: var(--font-size-lg);
            color: #2196f3;
            margin-top: 2px;
        }

        .enhanced-warning-box {
            background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
            border: 2px solid #ff9800;
            border-left: 4px solid #ff9800;
            color: #e65100;
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            margin: var(--spacing-lg) 0;
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
        }

        .enhanced-warning-box .warning-icon {
            font-size: var(--font-size-lg);
            color: #ff9800;
            margin-top: 2px;
        }

        .enhanced-form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .enhanced-btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            border: none;
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-lg);
            color: var(--white);
            font-weight: var(--font-weight-medium);
            transition: all var(--transition-base);
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            text-decoration: none;
            cursor: pointer;
            font-size: var(--font-size-base);
        }

        .enhanced-btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: var(--white);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .settings-page-header .header-content {
                flex-direction: column;
                text-align: center;
            }

            .settings-stats {
                flex-direction: column;
                align-items: center;
            }

            .enhanced-form-row {
                grid-template-columns: 1fr;
            }

            .enhanced-toggle-group {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-sm);
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Enhanced Settings Header -->
                <div class="settings-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-cogs"></i>
                                Site Settings
                            </h1>
                            <p class="page-description">Configure your website's core settings and system preferences</p>
                        </div>
                        <div class="header-actions">
                            <a href="email-test.php" class="btn btn-outline btn-lg">
                                <i class="fas fa-envelope"></i>
                                <span>Test Email</span>
                            </a>
                            <a href="../index.php" target="_blank" class="btn btn-primary btn-lg">
                                <i class="fas fa-external-link-alt"></i>
                                <span>View Website</span>
                            </a>
                        </div>
                    </div>

                    <div class="settings-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= $maintenanceData['maintenance_mode'] ? 'ON' : 'OFF' ?></span>
                            <span class="stat-label">Maintenance</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $emailData['smtp_enabled'] ? 'ON' : 'OFF' ?></span>
                            <span class="stat-label">SMTP</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $emailData['auto_reply_enabled'] ? 'ON' : 'OFF' ?></span>
                            <span class="stat-label">Auto-Reply</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">Live</span>
                            <span class="stat-label">Status</span>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-triangle' ?>"></i>
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <!-- Site Information -->
                <div class="enhanced-settings-section">
                    <div class="enhanced-settings-card">
                        <div class="enhanced-card-header">
                            <h3><i class="fas fa-info-circle"></i> Site Information</h3>
                        </div>
                        <div class="enhanced-card-content">
                            <form method="POST">
                                <?= getCSRFField() ?>
                                <input type="hidden" name="action" value="update_site_info">

                                <div class="enhanced-form-row">
                                    <div class="enhanced-form-group">
                                        <label for="site_name">Site Name *</label>
                                        <input type="text" id="site_name" name="site_name" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($siteData['site_name']) ?>" required>
                                    </div>
                                    <div class="enhanced-form-group">
                                        <label for="company_email">Company Email *</label>
                                        <input type="email" id="company_email" name="company_email" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($siteData['company_email']) ?>" required>
                                    </div>
                                </div>

                                <div class="enhanced-form-group">
                                    <label for="site_description">Site Description</label>
                                    <textarea id="site_description" name="site_description" class="enhanced-form-control" rows="3"><?= htmlspecialchars($siteData['site_description']) ?></textarea>
                                </div>

                                <div class="enhanced-form-group">
                                    <label for="site_keywords">SEO Keywords</label>
                                    <input type="text" id="site_keywords" name="site_keywords" class="enhanced-form-control"
                                           value="<?= htmlspecialchars($siteData['site_keywords']) ?>"
                                           placeholder="Comma-separated keywords">
                                </div>

                                <div class="enhanced-form-group">
                                    <label for="company_address">Company Address</label>
                                    <textarea id="company_address" name="company_address" class="enhanced-form-control" rows="2"><?= htmlspecialchars($siteData['company_address']) ?></textarea>
                                </div>

                                <div class="enhanced-form-row">
                                    <div class="enhanced-form-group">
                                        <label for="company_phone">Company Phone</label>
                                        <input type="text" id="company_phone" name="company_phone" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($siteData['company_phone']) ?>">
                                    </div>
                                    <div class="enhanced-form-group">
                                        <label for="company_mobile">Company Mobile</label>
                                        <input type="text" id="company_mobile" name="company_mobile" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($siteData['company_mobile']) ?>">
                                    </div>
                                </div>

                                <button type="submit" class="enhanced-btn-primary">
                                    <i class="fas fa-save"></i> Update Site Information
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Email Settings -->
                <div class="enhanced-settings-section">
                    <div class="enhanced-settings-card">
                        <div class="enhanced-card-header">
                            <h3><i class="fas fa-envelope"></i> Email Settings</h3>
                        </div>
                        <div class="enhanced-card-content">
                            <form method="POST">
                                <?= getCSRFField() ?>
                                <input type="hidden" name="action" value="update_email_settings">

                                <div class="enhanced-toggle-group">
                                    <label class="enhanced-toggle-switch">
                                        <input type="checkbox" name="smtp_enabled" <?= $emailData['smtp_enabled'] ? 'checked' : '' ?>>
                                        <span class="enhanced-slider"></span>
                                    </label>
                                    <label class="enhanced-toggle-label">Enable SMTP (recommended for production)</label>
                                </div>

                                <div class="enhanced-info-box">
                                    <i class="fas fa-info-circle info-icon"></i>
                                    <div>
                                        <strong>Note:</strong> SMTP is recommended for production websites to ensure reliable email delivery.
                                        For development/testing, you can leave SMTP disabled to use PHP's built-in mail() function.
                                    </div>
                                </div>

                                <div class="enhanced-form-row">
                                    <div class="enhanced-form-group">
                                        <label for="smtp_host">SMTP Host</label>
                                        <input type="text" id="smtp_host" name="smtp_host" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($emailData['smtp_host']) ?>"
                                               placeholder="smtp.gmail.com">
                                    </div>
                                    <div class="enhanced-form-group">
                                        <label for="smtp_port">SMTP Port</label>
                                        <input type="number" id="smtp_port" name="smtp_port" class="enhanced-form-control"
                                               value="<?= $emailData['smtp_port'] ?>" placeholder="587">
                                    </div>
                                </div>

                                <div class="enhanced-form-row">
                                    <div class="enhanced-form-group">
                                        <label for="smtp_username">SMTP Username</label>
                                        <input type="text" id="smtp_username" name="smtp_username" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($emailData['smtp_username']) ?>">
                                    </div>
                                    <div class="enhanced-form-group">
                                        <label for="smtp_password">SMTP Password</label>
                                        <input type="password" id="smtp_password" name="smtp_password" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($emailData['smtp_password']) ?>">
                                    </div>
                                </div>

                                <div class="enhanced-form-row">
                                    <div class="enhanced-form-group">
                                        <label for="from_email">From Email</label>
                                        <input type="email" id="from_email" name="from_email" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($emailData['from_email']) ?>" required>
                                    </div>
                                    <div class="enhanced-form-group">
                                        <label for="from_name">From Name</label>
                                        <input type="text" id="from_name" name="from_name" class="enhanced-form-control"
                                               value="<?= htmlspecialchars($emailData['from_name']) ?>" required>
                                    </div>
                                </div>

                                <div class="enhanced-form-group">
                                    <label for="notification_email">Notification Email</label>
                                    <input type="email" id="notification_email" name="notification_email" class="enhanced-form-control"
                                           value="<?= htmlspecialchars($emailData['notification_email']) ?>" required>
                                    <small>Email address to receive contact form notifications</small>
                                </div>

                                <div class="enhanced-toggle-group">
                                    <label class="enhanced-toggle-switch">
                                        <input type="checkbox" name="auto_reply_enabled" <?= $emailData['auto_reply_enabled'] ? 'checked' : '' ?>>
                                        <span class="enhanced-slider"></span>
                                    </label>
                                    <label class="enhanced-toggle-label">Enable Auto-Reply for Contact Forms</label>
                                </div>

                                <button type="submit" class="enhanced-btn-primary">
                                    <i class="fas fa-save"></i> Update Email Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Maintenance Mode -->
                <div class="enhanced-settings-section">
                    <div class="enhanced-settings-card">
                        <div class="enhanced-card-header">
                            <h3><i class="fas fa-tools"></i> Maintenance Mode</h3>
                        </div>
                        <div class="enhanced-card-content">
                            <form method="POST">
                                <?= getCSRFField() ?>
                                <input type="hidden" name="action" value="update_maintenance">

                                <div class="enhanced-toggle-group">
                                    <label class="enhanced-toggle-switch">
                                        <input type="checkbox" name="maintenance_mode" <?= $maintenanceData['maintenance_mode'] ? 'checked' : '' ?>>
                                        <span class="enhanced-slider"></span>
                                    </label>
                                    <label class="enhanced-toggle-label">Enable Maintenance Mode</label>
                                </div>

                                <?php if ($maintenanceData['maintenance_mode']): ?>
                                <div class="enhanced-warning-box">
                                    <i class="fas fa-exclamation-triangle warning-icon"></i>
                                    <div>
                                        <strong>Warning:</strong> Maintenance mode is currently enabled. Visitors will see the maintenance message instead of your website.
                                    </div>
                                </div>
                                <?php endif; ?>

                                <div class="enhanced-form-group">
                                    <label for="maintenance_message">Maintenance Message</label>
                                    <textarea id="maintenance_message" name="maintenance_message" class="enhanced-form-control" rows="3"><?= htmlspecialchars($maintenanceData['maintenance_message']) ?></textarea>
                                    <small>This message will be displayed to visitors when maintenance mode is enabled</small>
                                </div>

                                <button type="submit" class="enhanced-btn-primary">
                                    <i class="fas fa-save"></i> Update Maintenance Settings
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="settings-section">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3><i class="fas fa-server"></i> System Information</h3>
                        </div>
                        <div class="card-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>Server Information</h4>
                                    <ul style="list-style: none; padding: 0;">
                                        <li><strong>PHP Version:</strong> <?= PHP_VERSION ?></li>
                                        <li><strong>Server Software:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></li>
                                        <li><strong>Document Root:</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?></li>
                                        <li><strong>Server Time:</strong> <?= date('Y-m-d H:i:s T') ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h4>Database Information</h4>
                                    <ul style="list-style: none; padding: 0;">
                                        <?php
                                        try {
                                            $dbVersion = $db->fetchOne("SELECT VERSION() as version");
                                            echo "<li><strong>MySQL Version:</strong> " . $dbVersion['version'] . "</li>";

                                            $tables = $db->fetchAll("SHOW TABLES");
                                            echo "<li><strong>Tables:</strong> " . count($tables) . "</li>";

                                            $dbSize = $db->fetchOne("SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'size_mb' FROM information_schema.tables WHERE table_schema = DATABASE()");
                                            echo "<li><strong>Database Size:</strong> " . ($dbSize['size_mb'] ?: '0') . " MB</li>";

                                        } catch (Exception $e) {
                                            echo "<li><strong>Database:</strong> Connected</li>";
                                        }
                                        ?>
                                        <li><strong>Connection:</strong> Active</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3><i class="fas fa-bolt"></i> Quick Actions</h3>
                    </div>
                    <div class="card-content">
                        <div class="row">
                            <div class="col-md-3">
                                <a href="email-test.php" class="btn btn-outline btn-block">
                                    <i class="fas fa-envelope"></i> Test Email
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../index.php" target="_blank" class="btn btn-outline btn-block">
                                    <i class="fas fa-external-link-alt"></i> View Website
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../debug-auth.php" class="btn btn-outline btn-block">
                                    <i class="fas fa-bug"></i> Debug Tools
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="../setup.php" class="btn btn-outline btn-block">
                                    <i class="fas fa-database"></i> Database Setup
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
</body>
</html>
