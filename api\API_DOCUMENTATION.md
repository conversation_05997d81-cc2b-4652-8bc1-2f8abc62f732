# Flori Construction API Documentation

Complete API documentation for the mobile app backend integration.

## 🚀 Base URL

```
https://your-domain.com/api/
```

## 🔐 Authentication

All API endpoints (except login) require authentication using Bearer tokens.

### Headers Required
```
Authorization: Bearer {your-token}
Content-Type: application/json
```

---

## 📋 Authentication Endpoints

### POST `/auth.php?action=login`
Login and get authentication token.

**Request Body:**
```json
{
    "username": "admin",
    "password": "your-password"
}
```

**Response:**
```json
{
    "success": true,
    "token": "your-jwt-token",
    "expires_at": "2024-02-01 12:00:00",
    "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "full_name": "Administrator",
        "role": "admin"
    }
}
```

### GET `/auth.php?action=verify`
Verify current token validity.

**Response:**
```json
{
    "success": true,
    "user": {
        "id": 1,
        "username": "admin",
        "role": "admin"
    }
}
```

### POST `/auth.php?action=logout`
Logout and invalidate token.

**Response:**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

---

## 📊 Dashboard Endpoints

### GET `/mobile.php?action=dashboard`
Get dashboard statistics and recent data.

**Response:**
```json
{
    "success": true,
    "data": {
        "stats": {
            "total_projects": 25,
            "completed_projects": 20,
            "ongoing_projects": 5,
            "featured_projects": 8,
            "total_media": 150,
            "total_services": 5,
            "total_testimonials": 12
        },
        "recent_projects": [...],
        "recent_media": [...],
        "last_sync": "2024-01-15 10:30:00"
    }
}
```

---

## 🏗️ Projects Endpoints

### GET `/mobile.php?action=projects`
Get paginated list of projects.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `limit` (int): Items per page (default: 20)
- `type` (string): Filter by project_type (completed, ongoing, upcoming)
- `search` (string): Search in title, description, location
- `featured` (0|1): Filter featured projects

**Response:**
```json
{
    "success": true,
    "data": {
        "projects": [
            {
                "id": 1,
                "title": "Modern Office Building",
                "slug": "modern-office-building",
                "description": "Complete construction of modern office building",
                "client_name": "ABC Corp",
                "location": "London, UK",
                "project_type": "completed",
                "start_date": "2023-01-15",
                "end_date": "2023-12-20",
                "project_value": "500000.00",
                "featured_image": "uploads/project1.jpg",
                "gallery": ["1", "2", "3"],
                "is_featured": 1,
                "created_at": "2023-01-10 09:00:00",
                "updated_at": "2023-12-21 15:30:00"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 25,
            "pages": 2
        }
    }
}
```

### GET `/mobile.php?action=project&id={id}`
Get single project details with media.

**Response:**
```json
{
    "success": true,
    "data": {
        "project": {
            "id": 1,
            "title": "Modern Office Building",
            // ... full project data
        },
        "media": [
            {
                "id": 1,
                "filename": "image1.jpg",
                "original_name": "office-building.jpg",
                "file_path": "uploads/image1.jpg",
                "file_type": "image/jpeg",
                "alt_text": "Office building exterior",
                "caption": "Main entrance view"
            }
        ]
    }
}
```

### POST `/mobile.php?action=project`
Create new project.

**Request Body:**
```json
{
    "title": "New Project",
    "description": "Project description",
    "client_name": "Client Name",
    "location": "London, UK",
    "project_type": "ongoing",
    "start_date": "2024-01-15",
    "project_value": "250000.00",
    "is_featured": 0
}
```

### PUT `/mobile.php?action=project`
Update existing project.

**Request Body:**
```json
{
    "id": 1,
    "title": "Updated Project Title",
    "description": "Updated description",
    // ... other fields to update
}
```

### DELETE `/mobile.php?action=project&id={id}`
Delete project (soft delete).

---

## 📸 Media Endpoints

### GET `/mobile.php?action=media`
Get paginated list of media files.

**Query Parameters:**
- `page` (int): Page number
- `limit` (int): Items per page
- `type` (string): Filter by file type (image, video)
- `search` (string): Search in filename, alt_text

**Response:**
```json
{
    "success": true,
    "data": {
        "media": [
            {
                "id": 1,
                "filename": "unique_filename.jpg",
                "original_name": "photo.jpg",
                "file_path": "uploads/unique_filename.jpg",
                "file_type": "image/jpeg",
                "file_size": 1024000,
                "mime_type": "image/jpeg",
                "alt_text": "Description",
                "caption": "Photo caption",
                "uploaded_by": 1,
                "created_at": "2024-01-15 10:00:00"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 150,
            "pages": 8
        }
    }
}
```

### POST `/mobile.php?action=upload`
Upload media file.

**Request:** Multipart form data
- `file`: File to upload
- `alt_text`: Alt text for accessibility
- `caption`: Optional caption
- `project_id`: Optional project ID to associate with

**Response:**
```json
{
    "success": true,
    "media": {
        "id": 25,
        "filename": "unique_filename.jpg",
        "original_name": "photo.jpg",
        "file_path": "uploads/unique_filename.jpg",
        "file_type": "image/jpeg",
        "file_size": 1024000,
        "alt_text": "Description",
        "caption": "Photo caption",
        "uploaded_by": 1,
        "created_at": "2024-01-15 10:00:00"
    }
}
```

### PUT `/mobile.php?action=media`
Update media metadata.

**Request Body:**
```json
{
    "id": 1,
    "alt_text": "Updated alt text",
    "caption": "Updated caption"
}
```

### DELETE `/mobile.php?action=media&id={id}`
Delete media file.

---

## 📄 Content Endpoints

### GET `/mobile.php?action=content`
Get content pages/sections.

**Query Parameters:**
- `page`, `limit`, `type`, `search`

### PUT `/mobile.php?action=content`
Update content.

**Request Body:**
```json
{
    "id": 1,
    "title": "Updated Title",
    "content": "Updated content",
    "content_type": "page"
}
```

---

## 🔔 Notification Endpoints

### GET `/notifications.php`
Get user's push subscription status.

### POST `/notifications.php`
Subscribe to push notifications.

**Request Body:**
```json
{
    "action": "subscribe",
    "subscription": {
        "endpoint": "https://fcm.googleapis.com/fcm/send/...",
        "keys": {
            "p256dh": "key-data",
            "auth": "auth-data"
        }
    },
    "user_agent": "Mozilla/5.0...",
    "device_type": "mobile"
}
```

### POST `/notifications.php` (Admin only)
Send push notification.

**Request Body:**
```json
{
    "action": "send",
    "title": "Notification Title",
    "body": "Notification message",
    "user_id": 1,
    "data": {
        "action": "view-project",
        "project_id": 5
    }
}
```

### GET `/notifications.php?action=history`
Get notification history.

### PUT `/notifications.php`
Mark notification as read.

**Request Body:**
```json
{
    "action": "mark_read",
    "notification_id": 1
}
```

### DELETE `/notifications.php`
Unsubscribe from notifications.

---

## 🔄 Sync Endpoints

### GET `/mobile.php?action=sync&last_sync={timestamp}`
Get data updated since last sync.

**Response:**
```json
{
    "success": true,
    "data": {
        "projects": [...],
        "media": [...],
        "content": [...],
        "sync_timestamp": "2024-01-15 12:00:00"
    }
}
```

### POST `/mobile.php?action=sync`
Sync offline changes to server.

**Request Body:**
```json
{
    "projects": [
        {
            "id": 1,
            "title": "Updated offline",
            // ... updated fields
        }
    ],
    "media": [...],
    "content": [...]
}
```

---

## 🛠️ Utility Endpoints

### GET `/init-database.php` (Admin only)
Initialize/update database schema.

### GET `/test-api.php`
Interactive API testing interface.

---

## 📝 Error Responses

All endpoints return consistent error format:

```json
{
    "error": "Error message",
    "code": "ERROR_CODE"
}
```

**Common HTTP Status Codes:**
- `200`: Success
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

---

## 🔒 Security Notes

1. **HTTPS Required**: All API calls must use HTTPS in production
2. **Token Expiry**: Tokens expire after 30 days
3. **Rate Limiting**: Implement rate limiting in production
4. **File Upload**: Max 5MB per file, validated file types only
5. **CORS**: Configure proper CORS headers for your domain

---

## 📱 Mobile App Integration

### JavaScript Example:
```javascript
// Login
const response = await fetch('/api/auth.php?action=login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username: 'admin', password: 'password' })
});

const data = await response.json();
const token = data.token;

// Authenticated request
const projects = await fetch('/api/mobile.php?action=projects', {
    headers: { 'Authorization': `Bearer ${token}` }
});
```

### File Upload Example:
```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('alt_text', 'Photo description');

const response = await fetch('/api/mobile.php?action=upload', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: formData
});
```

---

## 🧪 Testing

Use the interactive API tester at `/api/test-api.php` to test all endpoints.

**Default Test Credentials:**
- Username: `admin`
- Password: `admin123`

---

**Last Updated:** January 2024  
**API Version:** 1.0
