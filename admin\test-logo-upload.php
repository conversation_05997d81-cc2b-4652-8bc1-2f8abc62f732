<?php
/**
 * Logo Upload Test Script
 * This script helps test and debug logo upload functionality
 */

require_once '../config/config.php';

// Check if user is logged in
requireLogin();

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Debug Information</h2>";
    echo "<h3>POST Data:</h3>";
    echo "<pre>" . print_r($_POST, true) . "</pre>";

    echo "<h3>FILES Data:</h3>";
    echo "<pre>" . print_r($_FILES, true) . "</pre>";

    echo "<h3>Server Info:</h3>";
    echo "<pre>";
    echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
    echo "Post Max Size: " . ini_get('post_max_size') . "\n";
    echo "Max File Uploads: " . ini_get('max_file_uploads') . "\n";
    echo "Memory Limit: " . ini_get('memory_limit') . "\n";
    echo "Max Execution Time: " . ini_get('max_execution_time') . "\n";
    echo "Upload Temp Dir: " . ini_get('upload_tmp_dir') . "\n";
    echo "</pre>";

    if (isset($_FILES['test_logo'])) {
        $file = $_FILES['test_logo'];

        echo "<h3>File Analysis:</h3>";
        echo "<pre>";
        echo "File Name: " . $file['name'] . "\n";
        echo "File Size: " . $file['size'] . " bytes (" . round($file['size'] / 1024, 2) . " KB)\n";
        echo "File Type: " . $file['type'] . "\n";
        echo "Temp Name: " . $file['tmp_name'] . "\n";
        echo "Error Code: " . $file['error'] . "\n";

        if ($file['error'] === UPLOAD_ERR_OK) {
            echo "File uploaded successfully to temp location\n";

            if (file_exists($file['tmp_name'])) {
                echo "Temp file exists: YES\n";
                echo "Temp file size: " . filesize($file['tmp_name']) . " bytes\n";

                // Check if it's an SVG file
                $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                if ($fileExtension === 'svg') {
                    echo "File type: SVG (vector image)\n";
                    $content = file_get_contents($file['tmp_name']);
                    if ($content && preg_match('/<svg[^>]*>/i', $content)) {
                        echo "SVG validation: Valid SVG file\n";
                        // Try to extract dimensions from SVG
                        if (preg_match('/width\s*=\s*["\']([^"\']+)["\']/', $content, $widthMatch) &&
                            preg_match('/height\s*=\s*["\']([^"\']+)["\']/', $content, $heightMatch)) {
                            echo "SVG dimensions: " . $widthMatch[1] . "x" . $heightMatch[1] . "\n";
                        }
                    } else {
                        echo "SVG validation: Invalid SVG file\n";
                    }
                } else {
                    // Use getimagesize for raster images
                    $imageInfo = getimagesize($file['tmp_name']);
                    if ($imageInfo) {
                        echo "Image dimensions: " . $imageInfo[0] . "x" . $imageInfo[1] . "\n";
                        echo "Image type: " . $imageInfo['mime'] . "\n";
                    } else {
                        echo "Not a valid image file\n";
                    }
                }
            } else {
                echo "Temp file exists: NO\n";
            }
        } else {
            $uploadErrors = [
                UPLOAD_ERR_INI_SIZE => 'File exceeds upload_max_filesize',
                UPLOAD_ERR_FORM_SIZE => 'File exceeds MAX_FILE_SIZE',
                UPLOAD_ERR_PARTIAL => 'File was only partially uploaded',
                UPLOAD_ERR_NO_FILE => 'No file was uploaded',
                UPLOAD_ERR_NO_TMP_DIR => 'Missing temporary folder',
                UPLOAD_ERR_CANT_WRITE => 'Failed to write file to disk',
                UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
            ];

            $errorMsg = isset($uploadErrors[$file['error']])
                ? $uploadErrors[$file['error']]
                : 'Unknown error';
            echo "Upload Error: " . $errorMsg . "\n";
        }
        echo "</pre>";

        // Test the uploadFile function
        if ($file['error'] === UPLOAD_ERR_OK) {
            echo "<h3>Testing uploadFile() function:</h3>";
            try {
                $result = uploadFile($file, 'general');
                echo "<div style='color: green;'>✅ Upload successful!</div>";
                echo "<pre>" . print_r($result, true) . "</pre>";

                // Check if file actually exists
                $fullPath = UPLOAD_PATH . '/' . $result['file_path'];
                if (file_exists($fullPath)) {
                    echo "<div style='color: green;'>✅ File exists at: " . $fullPath . "</div>";
                    echo "<div>File size: " . filesize($fullPath) . " bytes</div>";
                } else {
                    echo "<div style='color: red;'>❌ File does not exist at: " . $fullPath . "</div>";
                }

            } catch (Exception $e) {
                echo "<div style='color: red;'>❌ Upload failed: " . $e->getMessage() . "</div>";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logo Upload Test - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <style>
        .test-container { max-width: 800px; margin: 20px auto; padding: 20px; }
        .test-form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        pre { background: #e9ecef; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Logo Upload Test</h1>
        <p>This page helps test and debug logo upload functionality.</p>

        <div class="test-form">
            <h2>Test Upload</h2>
            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="MAX_FILE_SIZE" value="<?= MAX_FILE_SIZE ?>">

                <div style="margin-bottom: 15px;">
                    <label for="test_logo">Select Logo File:</label><br>
                    <input type="file" id="test_logo" name="test_logo" accept="image/*" style="margin-top: 5px;">
                </div>

                <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                    Test Upload
                </button>
            </form>
        </div>

        <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h3>Configuration Info:</h3>
            <ul>
                <li><strong>Upload Path:</strong> <?= UPLOAD_PATH ?></li>
                <li><strong>Upload URL:</strong> <?= UPLOAD_URL ?></li>
                <li><strong>Max File Size:</strong> <?= (MAX_FILE_SIZE / 1024 / 1024) ?>MB</li>
                <li><strong>Allowed Types:</strong> <?= implode(', ', ALLOWED_IMAGE_TYPES) ?></li>
                <li><strong>Upload Directory Writable:</strong> <?= is_writable(UPLOAD_PATH) ? 'YES' : 'NO' ?></li>
                <li><strong>General Directory Exists:</strong> <?= is_dir(UPLOAD_PATH . '/general') ? 'YES' : 'NO' ?></li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="branding.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
                ← Back to Branding Settings
            </a>
        </div>
    </div>
</body>
</html>
