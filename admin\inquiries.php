<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle form submissions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    $action = $_POST['action'] ?? '';

    if ($action === 'update_status') {
        $inquiryId = (int)$_POST['inquiry_id'];
        $status = sanitize($_POST['status']);
        $notes = sanitize($_POST['notes'] ?? '');

        if (in_array($status, ['new', 'read', 'replied', 'closed'])) {
            try {
                $db->update('contact_inquiries', [
                    'status' => $status,
                    'notes' => $notes,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$inquiryId]);

                $message = 'Inquiry status updated successfully!';
                $messageType = 'success';

            } catch (Exception $e) {
                $message = 'Error updating inquiry: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }

    if ($action === 'delete') {
        $inquiryId = (int)$_POST['inquiry_id'];

        try {
            $db->delete('contact_inquiries', 'id = ?', [$inquiryId]);
            $message = 'Inquiry deleted successfully!';
            $messageType = 'success';

        } catch (Exception $e) {
            $message = 'Error deleting inquiry: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Build query
$where = ['1 = 1'];
$params = [];

if ($statusFilter && in_array($statusFilter, ['new', 'read', 'replied', 'closed'])) {
    $where[] = 'status = ?';
    $params[] = $statusFilter;
}

if ($search) {
    $where[] = '(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)';
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

$whereClause = implode(' AND ', $where);

// Get total count
$totalQuery = "SELECT COUNT(*) as total FROM contact_inquiries WHERE $whereClause";
$total = $db->fetchOne($totalQuery, $params)['total'];

// Get inquiries
$inquiriesQuery = "SELECT * FROM contact_inquiries
                   WHERE $whereClause
                   ORDER BY created_at DESC
                   LIMIT $limit OFFSET $offset";
$inquiries = $db->fetchAll($inquiriesQuery, $params);

$totalPages = ceil($total / $limit);

// Get status counts for dashboard
$statusCounts = [
    'new' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'],
    'read' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'read'")['count'],
    'replied' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'replied'")['count'],
    'closed' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'closed'")['count']
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Inquiries - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Enhanced Inquiries Page Styles */
        .inquiries-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .inquiries-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .inquiries-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .inquiries-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .inquiries-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .inquiries-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .inquiries-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .inquiries-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .inquiries-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        .enhanced-inquiries-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-2xl);
        }

        .enhanced-inquiry-stat-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            overflow: hidden;
            position: relative;
        }

        .enhanced-inquiry-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: opacity var(--transition-base);
        }

        .enhanced-inquiry-stat-card.new::before { background: linear-gradient(90deg, #f39c12, #e67e22); }
        .enhanced-inquiry-stat-card.read::before { background: linear-gradient(90deg, #3498db, #2980b9); }
        .enhanced-inquiry-stat-card.replied::before { background: linear-gradient(90deg, #27ae60, #229954); }
        .enhanced-inquiry-stat-card.closed::before { background: linear-gradient(90deg, #95a5a6, #7f8c8d); }

        .enhanced-inquiry-stat-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .enhanced-inquiry-stat-card:hover::before {
            opacity: 1;
        }

        .enhanced-stat-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .enhanced-stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--border-radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-xl);
            color: var(--white);
        }

        .enhanced-stat-icon.new { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .enhanced-stat-icon.read { background: linear-gradient(135deg, #3498db, #2980b9); }
        .enhanced-stat-icon.replied { background: linear-gradient(135deg, #27ae60, #229954); }
        .enhanced-stat-icon.closed { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }

        .enhanced-stat-content {
            flex: 1;
        }

        .enhanced-stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--gray-800);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .enhanced-stat-label {
            font-size: var(--font-size-lg);
            color: var(--gray-600);
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--spacing-xs);
        }

        .enhanced-stat-trend {
            font-size: var(--font-size-sm);
            color: var(--gray-500);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .enhanced-inquiries-filters {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-2xl);
            overflow: hidden;
        }

        .enhanced-filters-header {
            background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
            padding: var(--spacing-xl);
            border-bottom: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .enhanced-filters-header h3 {
            margin: 0;
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .enhanced-filters-content {
            padding: var(--spacing-xl);
        }

        .enhanced-filter-form {
            display: grid;
            grid-template-columns: 2fr 1fr auto;
            gap: var(--spacing-lg);
            align-items: end;
        }

        .enhanced-search-group {
            position: relative;
        }

        .enhanced-search-group .search-icon {
            position: absolute;
            left: var(--spacing-md);
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: var(--font-size-sm);
        }

        .enhanced-search-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) calc(var(--spacing-md) * 2.5);
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            transition: all var(--transition-base);
            background: var(--gray-50);
        }

        .enhanced-search-input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: var(--white);
            outline: none;
        }

        .enhanced-filter-select {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 2px solid var(--gray-200);
            border-radius: var(--border-radius-md);
            font-size: var(--font-size-base);
            transition: all var(--transition-base);
            background: var(--gray-50);
        }

        .enhanced-filter-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: var(--white);
            outline: none;
        }

        .enhanced-filter-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .enhanced-empty-state {
            text-align: center;
            padding: var(--spacing-4xl);
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
        }

        .enhanced-empty-state-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto var(--spacing-xl);
            background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-4xl);
            color: var(--gray-400);
        }

        .enhanced-empty-state h3 {
            margin: 0 0 var(--spacing-md) 0;
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
        }

        .enhanced-empty-state p {
            margin: 0 0 var(--spacing-xl) 0;
            font-size: var(--font-size-lg);
            color: var(--gray-600);
            line-height: var(--line-height-relaxed);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .inquiries-page-header .header-content {
                flex-direction: column;
                text-align: center;
            }

            .inquiries-stats {
                flex-direction: column;
                align-items: center;
            }

            .enhanced-inquiries-stats-grid {
                grid-template-columns: 1fr;
            }

            .enhanced-filter-form {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }

            .enhanced-filter-actions {
                justify-content: stretch;
            }

            .enhanced-filter-actions .btn {
                flex: 1;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <!-- Enhanced Inquiries Header -->
                <div class="inquiries-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-envelope-open-text"></i>
                                Contact Inquiries
                            </h1>
                            <p class="page-description">Manage and respond to customer inquiries and contact form submissions</p>
                        </div>
                    </div>

                    <div class="inquiries-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= $statusCounts['new'] ?></span>
                            <span class="stat-label">New</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $statusCounts['read'] ?></span>
                            <span class="stat-label">Read</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $statusCounts['replied'] ?></span>
                            <span class="stat-label">Replied</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= array_sum($statusCounts) ?></span>
                            <span class="stat-label">Total</span>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Statistics Grid -->
                <div class="enhanced-inquiries-stats-grid">
                    <div class="enhanced-inquiry-stat-card new">
                        <div class="enhanced-stat-header">
                            <div class="enhanced-stat-icon new">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="enhanced-stat-content">
                                <span class="enhanced-stat-number"><?= $statusCounts['new'] ?></span>
                                <span class="enhanced-stat-label">New Inquiries</span>
                                <div class="enhanced-stat-trend">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>Requires attention</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-inquiry-stat-card read">
                        <div class="enhanced-stat-header">
                            <div class="enhanced-stat-icon read">
                                <i class="fas fa-envelope-open"></i>
                            </div>
                            <div class="enhanced-stat-content">
                                <span class="enhanced-stat-number"><?= $statusCounts['read'] ?></span>
                                <span class="enhanced-stat-label">Read</span>
                                <div class="enhanced-stat-trend">
                                    <i class="fas fa-eye"></i>
                                    <span>Under review</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-inquiry-stat-card replied">
                        <div class="enhanced-stat-header">
                            <div class="enhanced-stat-icon replied">
                                <i class="fas fa-reply"></i>
                            </div>
                            <div class="enhanced-stat-content">
                                <span class="enhanced-stat-number"><?= $statusCounts['replied'] ?></span>
                                <span class="enhanced-stat-label">Replied</span>
                                <div class="enhanced-stat-trend">
                                    <i class="fas fa-check"></i>
                                    <span>Responded</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="enhanced-inquiry-stat-card closed">
                        <div class="enhanced-stat-header">
                            <div class="enhanced-stat-icon closed">
                                <i class="fas fa-archive"></i>
                            </div>
                            <div class="enhanced-stat-content">
                                <span class="enhanced-stat-number"><?= $statusCounts['closed'] ?></span>
                                <span class="enhanced-stat-label">Closed</span>
                                <div class="enhanced-stat-trend">
                                    <i class="fas fa-check-double"></i>
                                    <span>Completed</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Filters -->
                <div class="enhanced-inquiries-filters">
                    <div class="enhanced-filters-header">
                        <h3>
                            <i class="fas fa-filter"></i>
                            Filter & Search
                        </h3>
                        <div class="results-count">
                            <span><?= $total ?> inquiries found</span>
                        </div>
                    </div>
                    <div class="enhanced-filters-content">
                        <form method="GET" class="enhanced-filter-form">
                            <div class="enhanced-search-group">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text"
                                       name="search"
                                       placeholder="Search by name, email, subject, or message..."
                                       value="<?= htmlspecialchars($search ?? '', ENT_QUOTES, 'UTF-8') ?>"
                                       class="enhanced-search-input">
                            </div>

                            <div class="enhanced-filter-group">
                                <select name="status" class="enhanced-filter-select">
                                    <option value="">All Status</option>
                                    <option value="new" <?= $statusFilter === 'new' ? 'selected' : '' ?>>New</option>
                                    <option value="read" <?= $statusFilter === 'read' ? 'selected' : '' ?>>Read</option>
                                    <option value="replied" <?= $statusFilter === 'replied' ? 'selected' : '' ?>>Replied</option>
                                    <option value="closed" <?= $statusFilter === 'closed' ? 'selected' : '' ?>>Closed</option>
                                </select>
                            </div>

                            <div class="enhanced-filter-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    Apply
                                </button>
                                <?php if ($search || $statusFilter): ?>
                                <a href="inquiries.php" class="btn btn-outline">
                                    <i class="fas fa-times"></i>
                                    Clear
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Inquiries List -->
                <?php if (empty($inquiries)): ?>
                <div class="enhanced-empty-state">
                    <div class="enhanced-empty-state-icon">
                        <i class="fas fa-envelope-open"></i>
                    </div>
                    <h3>No inquiries found</h3>
                    <p>
                        <?php if ($search || $statusFilter): ?>
                            No inquiries match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            Contact form submissions will appear here when customers reach out to you.
                        <?php endif; ?>
                    </p>
                    <?php if ($search || $statusFilter): ?>
                    <a href="inquiries.php" class="btn btn-primary btn-lg">
                        <i class="fas fa-times"></i>
                        Clear Filters
                    </a>
                    <?php endif; ?>
                </div>
                <?php else: ?>

                <div class="inquiries-list-header">
                    <h2 class="list-title">
                        <i class="fas fa-list"></i>
                        Inquiries (<?= $total ?>)
                    </h2>
                    <div class="list-actions">
                        <button class="btn btn-outline" onclick="markAllAsRead()">
                            <i class="fas fa-eye"></i>
                            Mark All as Read
                        </button>
                    </div>
                </div>

                <div class="inquiries-list">
                    <?php foreach ($inquiries as $inquiry): ?>
                    <div class="modern-inquiry-card <?= $inquiry['status'] ?>" data-inquiry-id="<?= $inquiry['id'] ?>">
                        <div class="inquiry-priority">
                            <div class="priority-indicator <?= $inquiry['status'] ?>"></div>
                        </div>

                        <div class="inquiry-main">
                            <div class="inquiry-header">
                                <div class="inquiry-title">
                                    <h4 class="client-name">
                                        <i class="fas fa-user"></i>
                                        <?= htmlspecialchars($inquiry['name'] ?? '', ENT_QUOTES, 'UTF-8') ?>
                                    </h4>
                                    <?php if ($inquiry['subject']): ?>
                                    <h5 class="inquiry-subject">
                                        <?= htmlspecialchars($inquiry['subject'] ?? '', ENT_QUOTES, 'UTF-8') ?>
                                    </h5>
                                    <?php endif; ?>
                                </div>
                                <div class="inquiry-meta">
                                    <span class="inquiry-date">
                                        <i class="fas fa-clock"></i>
                                        <?= formatDate($inquiry['created_at']) ?>
                                    </span>
                                    <span class="status-badge status-<?= $inquiry['status'] ?>">
                                        <i class="fas fa-<?= $inquiry['status'] === 'new' ? 'envelope' : ($inquiry['status'] === 'read' ? 'envelope-open' : ($inquiry['status'] === 'replied' ? 'reply' : 'archive')) ?>"></i>
                                        <?= ucfirst($inquiry['status']) ?>
                                    </span>
                                </div>
                            </div>

                            <div class="inquiry-details">
                                <div class="contact-info">
                                    <div class="contact-item">
                                        <div class="contact-icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="contact-content">
                                            <label>Email Address</label>
                                            <a href="mailto:<?= htmlspecialchars($inquiry['email'] ?? '', ENT_QUOTES, 'UTF-8') ?>" class="contact-link">
                                                <?= htmlspecialchars($inquiry['email'] ?? '', ENT_QUOTES, 'UTF-8') ?>
                                            </a>
                                        </div>
                                    </div>

                                    <?php if ($inquiry['phone']): ?>
                                    <div class="contact-item">
                                        <div class="contact-icon">
                                            <i class="fas fa-phone"></i>
                                        </div>
                                        <div class="contact-content">
                                            <label>Phone Number</label>
                                            <a href="tel:<?= htmlspecialchars($inquiry['phone'] ?? '', ENT_QUOTES, 'UTF-8') ?>" class="contact-link">
                                                <?= htmlspecialchars($inquiry['phone'] ?? '', ENT_QUOTES, 'UTF-8') ?>
                                            </a>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($inquiry['service_interest']): ?>
                                    <div class="contact-item">
                                        <div class="contact-icon">
                                            <i class="fas fa-tools"></i>
                                        </div>
                                        <div class="contact-content">
                                            <label>Service Interest</label>
                                            <span class="service-tag">
                                                <?= htmlspecialchars($inquiry['service_interest'] ?? '', ENT_QUOTES, 'UTF-8') ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <?php if ($inquiry['project_reference']): ?>
                                    <div class="contact-item">
                                        <div class="contact-icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="contact-content">
                                            <label>Project Reference</label>
                                            <span class="project-ref">
                                                <?= htmlspecialchars($inquiry['project_reference'] ?? '', ENT_QUOTES, 'UTF-8') ?>
                                            </span>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <div class="message-section">
                                    <div class="message-header">
                                        <h6>
                                            <i class="fas fa-comment-alt"></i>
                                            Message
                                        </h6>
                                    </div>
                                    <div class="message-content">
                                        <?= nl2br(htmlspecialchars($inquiry['message'] ?? '', ENT_QUOTES, 'UTF-8')) ?>
                                    </div>
                                </div>

                                <?php if ($inquiry['notes']): ?>
                                <div class="notes-section">
                                    <div class="notes-header">
                                        <h6>
                                            <i class="fas fa-sticky-note"></i>
                                            Internal Notes
                                        </h6>
                                    </div>
                                    <div class="notes-content">
                                        <?= nl2br(htmlspecialchars($inquiry['notes'] ?? '', ENT_QUOTES, 'UTF-8')) ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="inquiry-actions">
                                <div class="action-group primary-actions">
                                    <a href="mailto:<?= htmlspecialchars($inquiry['email'] ?? '', ENT_QUOTES, 'UTF-8') ?>?subject=Re: <?= urlencode($inquiry['subject'] ?: 'Your Inquiry') ?>"
                                       class="btn btn-primary action-btn">
                                        <i class="fas fa-reply"></i>
                                        <span>Reply via Email</span>
                                    </a>

                                    <?php if ($inquiry['phone']): ?>
                                    <a href="tel:<?= htmlspecialchars($inquiry['phone'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                                       class="btn btn-success action-btn">
                                        <i class="fas fa-phone"></i>
                                        <span>Call Client</span>
                                    </a>
                                    <?php endif; ?>
                                </div>

                                <div class="action-group status-actions">
                                    <form method="POST" class="status-form">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="update_status">
                                        <input type="hidden" name="inquiry_id" value="<?= $inquiry['id'] ?>">

                                        <div class="status-update-group">
                                            <select name="status" class="status-select">
                                                <option value="new" <?= $inquiry['status'] === 'new' ? 'selected' : '' ?>>
                                                    New
                                                </option>
                                                <option value="read" <?= $inquiry['status'] === 'read' ? 'selected' : '' ?>>
                                                    Read
                                                </option>
                                                <option value="replied" <?= $inquiry['status'] === 'replied' ? 'selected' : '' ?>>
                                                    Replied
                                                </option>
                                                <option value="closed" <?= $inquiry['status'] === 'closed' ? 'selected' : '' ?>>
                                                    Closed
                                                </option>
                                            </select>

                                            <input type="text"
                                                   name="notes"
                                                   placeholder="Add internal notes..."
                                                   value="<?= htmlspecialchars($inquiry['notes'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                                                   class="notes-input">

                                            <button type="submit" class="btn btn-outline action-btn">
                                                <i class="fas fa-save"></i>
                                                <span>Update</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>

                                <div class="action-group danger-actions">
                                    <form method="POST" class="delete-form"
                                          onsubmit="return confirm('Are you sure you want to delete this inquiry? This action cannot be undone.')">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="inquiry_id" value="<?= $inquiry['id'] ?>">
                                        <button type="submit" class="btn btn-danger action-btn">
                                            <i class="fas fa-trash-alt"></i>
                                            <span>Delete</span>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Enhanced Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="modern-pagination">
                    <div class="pagination-info">
                        <span>Showing page <?= $page ?> of <?= $totalPages ?></span>
                    </div>
                    <div class="pagination-controls">
                        <?php if ($page > 1): ?>
                        <a href="?page=<?= $page - 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $statusFilter ? '&status=' . $statusFilter : '' ?>"
                           class="pagination-btn prev">
                            <i class="fas fa-chevron-left"></i>
                            <span>Previous</span>
                        </a>
                        <?php endif; ?>

                        <div class="pagination-numbers">
                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <?php if ($i == $page): ?>
                                <span class="pagination-number active"><?= $i ?></span>
                                <?php else: ?>
                                <a href="?page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $statusFilter ? '&status=' . $statusFilter : '' ?>"
                                   class="pagination-number"><?= $i ?></a>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>

                        <?php if ($page < $totalPages): ?>
                        <a href="?page=<?= $page + 1 ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $statusFilter ? '&status=' . $statusFilter : '' ?>"
                           class="pagination-btn next">
                            <span>Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Mark all as read functionality
            window.markAllAsRead = function() {
                if (confirm('Mark all visible inquiries as read?')) {
                    const forms = document.querySelectorAll('.status-form');
                    forms.forEach(form => {
                        const statusSelect = form.querySelector('.status-select');
                        if (statusSelect.value === 'new') {
                            statusSelect.value = 'read';
                            form.submit();
                        }
                    });
                }
            };

            // Auto-submit status changes
            const statusSelects = document.querySelectorAll('.status-select');
            statusSelects.forEach(select => {
                select.addEventListener('change', function() {
                    if (confirm('Update inquiry status?')) {
                        this.closest('form').submit();
                    }
                });
            });

            // Enhanced search functionality
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        if (this.value.length >= 3 || this.value.length === 0) {
                            this.closest('form').submit();
                        }
                    }, 500);
                });
            }

            // Inquiry card interactions
            const inquiryCards = document.querySelectorAll('.modern-inquiry-card');
            inquiryCards.forEach(card => {
                // Mark as read when clicked
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('.inquiry-actions')) {
                        const statusBadge = this.querySelector('.status-badge');
                        if (statusBadge && statusBadge.textContent.trim() === 'New') {
                            const statusForm = this.querySelector('.status-form');
                            const statusSelect = statusForm.querySelector('.status-select');
                            statusSelect.value = 'read';
                            statusForm.submit();
                        }
                    }
                });

                // Hover effects
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + F to focus search
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    const searchInput = document.querySelector('.search-input');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }

                // Escape to clear search
                if (e.key === 'Escape') {
                    const searchInput = document.querySelector('.search-input');
                    if (searchInput && searchInput === document.activeElement) {
                        searchInput.value = '';
                        searchInput.closest('form').submit();
                    }
                }
            });

            // Real-time status updates
            const updateForms = document.querySelectorAll('.status-form');
            updateForms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Updating...</span>';
                    submitBtn.disabled = true;
                });
            });

            // Auto-expand long messages
            const messageContents = document.querySelectorAll('.message-content');
            messageContents.forEach(content => {
                if (content.scrollHeight > 100) {
                    content.style.maxHeight = '100px';
                    content.style.overflow = 'hidden';

                    const expandBtn = document.createElement('button');
                    expandBtn.className = 'expand-message-btn';
                    expandBtn.innerHTML = '<i class="fas fa-chevron-down"></i> Show more';
                    expandBtn.type = 'button';

                    expandBtn.addEventListener('click', function() {
                        if (content.style.maxHeight === '100px') {
                            content.style.maxHeight = 'none';
                            this.innerHTML = '<i class="fas fa-chevron-up"></i> Show less';
                        } else {
                            content.style.maxHeight = '100px';
                            this.innerHTML = '<i class="fas fa-chevron-down"></i> Show more';
                        }
                    });

                    content.parentNode.appendChild(expandBtn);
                }
            });
        });
    </script>
</body>
</html>
