<?php
require_once '../config/config.php';
require_once '../config/database.php';

requireLogin();

$db = new Database();
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    requireCSRF();

    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'update_page_seo':
            $pageId = (int)$_POST['page_id'];
            $pageKey = 'page_' . $pageId;
            $metaTitle = sanitize($_POST['meta_title']);
            $metaDescription = sanitize($_POST['meta_description']);
            $metaKeywords = sanitize($_POST['meta_keywords']);
            $ogTitle = sanitize($_POST['og_title']);
            $ogDescription = sanitize($_POST['og_description']);
            $ogImage = sanitize($_POST['og_image']);
            $canonicalUrl = sanitize($_POST['canonical_url']);
            $robotsIndex = isset($_POST['robots_index']) ? 1 : 0;
            $robotsFollow = isset($_POST['robots_follow']) ? 1 : 0;

            try {
                $existing = $db->fetchOne("SELECT id FROM page_seo WHERE page_key = ?", [$pageKey]);

                if ($existing) {
                    $db->query(
                        "UPDATE page_seo SET
                         meta_title = ?, meta_description = ?, meta_keywords = ?,
                         og_title = ?, og_description = ?, og_image = ?,
                         canonical_url = ?, robots_index = ?, robots_follow = ?,
                         updated_at = NOW()
                         WHERE page_key = ?",
                        [$metaTitle, $metaDescription, $metaKeywords, $ogTitle, $ogDescription,
                         $ogImage, $canonicalUrl, $robotsIndex, $robotsFollow, $pageKey]
                    );
                } else {
                    $db->query(
                        "INSERT INTO page_seo
                         (page_key, meta_title, meta_description, meta_keywords, og_title, og_description,
                          og_image, canonical_url, robots_index, robots_follow, created_at, updated_at)
                         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())",
                        [$pageKey, $metaTitle, $metaDescription, $metaKeywords, $ogTitle, $ogDescription,
                         $ogImage, $canonicalUrl, $robotsIndex, $robotsFollow]
                    );
                }

                $message = 'SEO settings updated successfully!';
            } catch (Exception $e) {
                $error = 'Error updating SEO settings: ' . $e->getMessage();
            }
            break;

        case 'update_global_seo':
            $siteName = sanitize($_POST['site_name']);
            $siteDescription = sanitize($_POST['site_description']);
            $defaultOgImage = sanitize($_POST['default_og_image']);
            $googleAnalytics = sanitize($_POST['google_analytics']);
            $googleSearchConsole = sanitize($_POST['google_search_console']);
            $facebookPixel = sanitize($_POST['facebook_pixel']);

            try {
                $settings = [
                    'site_name' => $siteName,
                    'site_description' => $siteDescription,
                    'default_og_image' => $defaultOgImage,
                    'google_analytics' => $googleAnalytics,
                    'google_search_console' => $googleSearchConsole,
                    'facebook_pixel' => $facebookPixel
                ];

                foreach ($settings as $key => $value) {
                    $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = ?", [$key]);

                    if ($existing) {
                        $db->query(
                            "UPDATE site_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?",
                            [$value, $key]
                        );
                    } else {
                        $db->query(
                            "INSERT INTO site_settings (setting_key, setting_value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
                            [$key, $value]
                        );
                    }
                }

                $message = 'Global SEO settings updated successfully!';
            } catch (Exception $e) {
                $error = 'Error updating global SEO settings: ' . $e->getMessage();
            }
            break;
    }
}

// Get pages for SEO management
$pages = [
    ['id' => 1, 'name' => 'Homepage', 'url' => '/'],
    ['id' => 2, 'name' => 'About', 'url' => '/about'],
    ['id' => 3, 'name' => 'Services', 'url' => '/services'],
    ['id' => 4, 'name' => 'Projects', 'url' => '/projects'],
    ['id' => 5, 'name' => 'Contact', 'url' => '/contact']
];

// Get current global SEO settings
$globalSettings = [];
$settingsResult = $db->fetchAll("SELECT setting_key, setting_value FROM site_settings WHERE setting_key IN ('site_name', 'site_description', 'default_og_image', 'google_analytics', 'google_search_console', 'facebook_pixel')");
foreach ($settingsResult as $setting) {
    $globalSettings[$setting['setting_key']] = $setting['setting_value'];
}

// Get current page if editing
$currentPage = null;
$currentSEO = null;
if (isset($_GET['page_id'])) {
    $pageId = (int)$_GET['page_id'];
    $filteredPages = array_filter($pages, function($p) use ($pageId) { return $p['id'] == $pageId; });
    $currentPage = reset($filteredPages);

    // Ensure we have a valid page (reset returns false if array is empty)
    if ($currentPage !== false && is_array($currentPage) && isset($currentPage['id'])) {
        // Use page_key instead of page_id to match database schema
        $pageKey = 'page_' . $pageId;
        $currentSEO = $db->fetchOne("SELECT * FROM page_seo WHERE page_key = ?", [$pageKey]);
        // If no SEO data exists, initialize empty array to prevent errors
        if (!$currentSEO) {
            $currentSEO = [];
        }
    } else {
        $currentPage = null; // Reset to null if no valid page found
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Management - <?= SITE_NAME ?></title>
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Enhanced SEO Page Styles */
        .seo-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .seo-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .seo-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .seo-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .seo-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .seo-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .seo-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .seo-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .seo-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .seo-page-header .header-content {
                flex-direction: column;
                text-align: center;
            }

            .seo-stats {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Enhanced SEO Header -->
                <div class="seo-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-search-plus"></i>
                                SEO Management
                            </h1>
                            <p class="page-description">Optimize your website's search engine visibility and performance</p>
                        </div>
                        <div class="header-actions">
                            <a href="#" class="btn btn-outline btn-lg" onclick="checkSEOHealth()">
                                <i class="fas fa-stethoscope"></i>
                                <span>SEO Health Check</span>
                            </a>
                            <a href="#" class="btn btn-primary btn-lg" onclick="generateSitemap()">
                                <i class="fas fa-sitemap"></i>
                                <span>Generate Sitemap</span>
                            </a>
                        </div>
                    </div>

                    <div class="seo-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= count($pages) ?></span>
                            <span class="stat-label">Pages</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= count(array_filter($pages, function($p) use ($db) {
                                $pageKey = 'page_' . $p['id'];
                                return $db->fetchOne("SELECT id FROM page_seo WHERE page_key = ?", [$pageKey]);
                            })) ?></span>
                            <span class="stat-label">Optimized</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= !empty($globalSettings['google_analytics']) ? '1' : '0' ?></span>
                            <span class="stat-label">Analytics</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= !empty($globalSettings['google_search_console']) ? '1' : '0' ?></span>
                            <span class="stat-label">Console</span>
                        </div>
                    </div>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= $message ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i> <?= $error ?>
                    </div>
                <?php endif; ?>

                <div class="seo-tabs">
                    <div class="seo-tab-navigation">
                        <button class="seo-tab-btn active" data-tab="global">
                            <i class="fas fa-globe"></i>
                            <span>Global SEO</span>
                            <div class="tab-indicator"></div>
                        </button>
                        <button class="seo-tab-btn" data-tab="pages">
                            <i class="fas fa-file-alt"></i>
                            <span>Page SEO</span>
                            <div class="tab-indicator"></div>
                        </button>
                        <button class="seo-tab-btn" data-tab="analytics">
                            <i class="fas fa-chart-line"></i>
                            <span>Analytics</span>
                            <div class="tab-indicator"></div>
                        </button>
                        <button class="seo-tab-btn" data-tab="tools">
                            <i class="fas fa-tools"></i>
                            <span>SEO Tools</span>
                            <div class="tab-indicator"></div>
                        </button>
                    </div>

                    <!-- Global SEO Tab -->
                    <div class="seo-tab-content active" id="global-tab">
                        <div class="seo-section">
                            <div class="seo-section-header">
                                <h3><i class="fas fa-globe"></i> Global SEO Settings</h3>
                                <p>Configure site-wide SEO settings that apply to all pages</p>
                            </div>

                            <div class="seo-form-container">
                                <form method="POST" class="modern-seo-form">
                                    <?= getCSRFField() ?>
                                    <input type="hidden" name="action" value="update_global_seo">

                                    <div class="form-section">
                                        <h4><i class="fas fa-info-circle"></i> Basic Information</h4>
                                        <div class="form-grid">
                                            <div class="form-field">
                                                <label for="site_name">
                                                    <i class="fas fa-tag"></i>
                                                    Site Name
                                                </label>
                                                <input type="text" id="site_name" name="site_name" class="form-input"
                                                       value="<?= htmlspecialchars($globalSettings['site_name'] ?? SITE_NAME) ?>" required>
                                                <span class="form-help">The name of your website</span>
                                            </div>

                                            <div class="form-field full-width">
                                                <label for="site_description">
                                                    <i class="fas fa-align-left"></i>
                                                    Site Description
                                                </label>
                                                <textarea id="site_description" name="site_description" class="form-textarea" rows="3" required><?= htmlspecialchars($globalSettings['site_description'] ?? '') ?></textarea>
                                                <span class="form-help">A brief description of your website (used in search results)</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section">
                                        <h4><i class="fas fa-share-alt"></i> Social Media</h4>
                                        <div class="form-grid">
                                            <div class="form-field full-width">
                                                <label for="default_og_image">
                                                    <i class="fas fa-image"></i>
                                                    Default Open Graph Image URL
                                                </label>
                                                <input type="url" id="default_og_image" name="default_og_image" class="form-input"
                                                       value="<?= htmlspecialchars($globalSettings['default_og_image'] ?? '') ?>"
                                                       placeholder="https://example.com/og-image.jpg">
                                                <span class="form-help">Default image for social media sharing (1200x630px recommended)</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section">
                                        <h4><i class="fas fa-chart-bar"></i> Analytics & Tracking</h4>
                                        <div class="form-grid">
                                            <div class="form-field">
                                                <label for="google_analytics">
                                                    <i class="fab fa-google"></i>
                                                    Google Analytics ID
                                                </label>
                                                <input type="text" id="google_analytics" name="google_analytics" class="form-input"
                                                       value="<?= htmlspecialchars($globalSettings['google_analytics'] ?? '') ?>"
                                                       placeholder="G-XXXXXXXXXX">
                                                <span class="form-help">Your Google Analytics measurement ID</span>
                                            </div>

                                            <div class="form-field">
                                                <label for="facebook_pixel">
                                                    <i class="fab fa-facebook"></i>
                                                    Facebook Pixel ID
                                                </label>
                                                <input type="text" id="facebook_pixel" name="facebook_pixel" class="form-input"
                                                       value="<?= htmlspecialchars($globalSettings['facebook_pixel'] ?? '') ?>"
                                                       placeholder="123456789012345">
                                                <span class="form-help">Your Facebook Pixel ID for tracking</span>
                                            </div>

                                            <div class="form-field full-width">
                                                <label for="google_search_console">
                                                    <i class="fas fa-search"></i>
                                                    Google Search Console Verification
                                                </label>
                                                <input type="text" id="google_search_console" name="google_search_console" class="form-input"
                                                       value="<?= htmlspecialchars($globalSettings['google_search_console'] ?? '') ?>"
                                                       placeholder="Verification meta tag content">
                                                <span class="form-help">Meta tag content for Google Search Console verification</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary btn-large">
                                            <i class="fas fa-save"></i>
                                            <span>Save Global Settings</span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Page SEO Tab -->
                    <div class="seo-tab-content" id="pages-tab">
                        <div class="seo-section">
                            <div class="seo-section-header">
                                <h3><i class="fas fa-file-alt"></i> Page SEO Settings</h3>
                                <p>Optimize individual pages for better search engine rankings</p>
                            </div>

                            <div class="seo-content">
                                <?php if (!$currentPage): ?>
                                    <div class="pages-overview">
                                        <div class="pages-header">
                                            <h4>Select a page to optimize:</h4>
                                            <div class="pages-stats">
                                                <span class="pages-count"><?= count($pages) ?> pages available</span>
                                            </div>
                                        </div>
                                        <div class="modern-pages-grid">
                                            <?php foreach ($pages as $page): ?>
                                                <?php
                                                    $pageKey = 'page_' . $page['id'];
                                                    $pageSEO = $db->fetchOne("SELECT * FROM page_seo WHERE page_key = ?", [$pageKey]);
                                                    $hasOptimization = !empty($pageSEO);
                                                ?>
                                                <a href="?page_id=<?= $page['id'] ?>" class="modern-page-card">
                                                    <div class="page-card-header">
                                                        <div class="page-icon">
                                                            <i class="fas fa-file-alt"></i>
                                                        </div>
                                                        <div class="page-status <?= $hasOptimization ? 'optimized' : 'pending' ?>">
                                                            <i class="fas <?= $hasOptimization ? 'fa-check-circle' : 'fa-clock' ?>"></i>
                                                        </div>
                                                    </div>
                                                    <div class="page-card-content">
                                                        <h5><?= htmlspecialchars($page['name']) ?></h5>
                                                        <p class="page-url"><?= htmlspecialchars($page['url']) ?></p>
                                                        <div class="page-optimization-status">
                                                            <?php if ($hasOptimization): ?>
                                                                <span class="status-badge optimized">
                                                                    <i class="fas fa-check"></i> Optimized
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="status-badge pending">
                                                                    <i class="fas fa-exclamation-triangle"></i> Needs Optimization
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <div class="page-card-footer">
                                                        <span class="edit-link">
                                                            <i class="fas fa-edit"></i> Edit SEO
                                                        </span>
                                                    </div>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="page-seo-editor">
                                        <div class="page-editor-header">
                                            <div class="page-breadcrumb">
                                                <div class="breadcrumb-item">
                                                    <i class="fas fa-file-alt"></i>
                                                    <span>Editing Page SEO</span>
                                                </div>
                                                <div class="breadcrumb-separator">
                                                    <i class="fas fa-chevron-right"></i>
                                                </div>
                                                <div class="breadcrumb-item active">
                                                    <span><?= htmlspecialchars($currentPage['name'] ?? 'Unknown Page') ?></span>
                                                </div>
                                            </div>
                                            <div class="page-url-display">
                                                <i class="fas fa-link"></i>
                                                <span><?= htmlspecialchars($currentPage['url'] ?? '/') ?></span>
                                            </div>
                                            <a href="seo.php" class="btn btn-outline btn-sm">
                                                <i class="fas fa-arrow-left"></i> Back to Pages
                                            </a>
                                        </div>

                                        <div class="seo-form-container">
                                            <form method="POST" class="modern-seo-form">
                                                <?= getCSRFField() ?>
                                                <input type="hidden" name="action" value="update_page_seo">
                                                <input type="hidden" name="page_id" value="<?= $currentPage['id'] ?? 0 ?>">

                                                <div class="form-section">
                                                    <h4><i class="fas fa-search"></i> Basic SEO Meta Tags</h4>
                                                    <div class="form-grid">
                                                        <div class="form-field">
                                                            <label for="meta_title">
                                                                <i class="fas fa-heading"></i>
                                                                Meta Title
                                                            </label>
                                                            <input type="text" id="meta_title" name="meta_title" class="form-input"
                                                                   value="<?= htmlspecialchars($currentSEO['meta_title'] ?? '') ?>"
                                                                   maxlength="60" placeholder="Enter page title for search results">
                                                            <span class="form-help">Recommended: 50-60 characters. This appears as the clickable headline in search results.</span>
                                                        </div>

                                                        <div class="form-field full-width">
                                                            <label for="meta_description">
                                                                <i class="fas fa-align-left"></i>
                                                                Meta Description
                                                            </label>
                                                            <textarea id="meta_description" name="meta_description" class="form-textarea" rows="3"
                                                                      maxlength="160" placeholder="Enter a compelling description for search results"><?= htmlspecialchars($currentSEO['meta_description'] ?? '') ?></textarea>
                                                            <span class="form-help">Recommended: 150-160 characters. This appears as the description snippet in search results.</span>
                                                        </div>

                                                        <div class="form-field full-width">
                                                            <label for="meta_keywords">
                                                                <i class="fas fa-tags"></i>
                                                                Meta Keywords
                                                            </label>
                                                            <input type="text" id="meta_keywords" name="meta_keywords" class="form-input"
                                                                   value="<?= htmlspecialchars($currentSEO['meta_keywords'] ?? '') ?>"
                                                                   placeholder="keyword1, keyword2, keyword3">
                                                            <span class="form-help">Comma-separated keywords relevant to this page content.</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-section">
                                                    <h4><i class="fas fa-share-alt"></i> Open Graph (Social Media)</h4>
                                                    <div class="form-grid">
                                                        <div class="form-field">
                                                            <label for="og_title">
                                                                <i class="fab fa-facebook"></i>
                                                                OG Title
                                                            </label>
                                                            <input type="text" id="og_title" name="og_title" class="form-input"
                                                                   value="<?= htmlspecialchars($currentSEO['og_title'] ?? '') ?>"
                                                                   placeholder="Title for social media sharing">
                                                            <span class="form-help">Title that appears when shared on social media platforms.</span>
                                                        </div>

                                                        <div class="form-field">
                                                            <label for="og_description">
                                                                <i class="fab fa-twitter"></i>
                                                                OG Description
                                                            </label>
                                                            <textarea id="og_description" name="og_description" class="form-textarea" rows="2"
                                                                      placeholder="Description for social media sharing"><?= htmlspecialchars($currentSEO['og_description'] ?? '') ?></textarea>
                                                            <span class="form-help">Description that appears when shared on social media.</span>
                                                        </div>

                                                        <div class="form-field full-width">
                                                            <label for="og_image">
                                                                <i class="fas fa-image"></i>
                                                                OG Image URL
                                                            </label>
                                                            <input type="url" id="og_image" name="og_image" class="form-input"
                                                                   value="<?= htmlspecialchars($currentSEO['og_image'] ?? '') ?>"
                                                                   placeholder="https://example.com/image.jpg">
                                                            <span class="form-help">Image that appears when shared on social media (1200x630px recommended).</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-section">
                                                    <h4><i class="fas fa-cog"></i> Advanced SEO Settings</h4>
                                                    <div class="form-grid">
                                                        <div class="form-field full-width">
                                                            <label for="canonical_url">
                                                                <i class="fas fa-link"></i>
                                                                Canonical URL
                                                            </label>
                                                            <input type="url" id="canonical_url" name="canonical_url" class="form-input"
                                                                   value="<?= htmlspecialchars($currentSEO['canonical_url'] ?? '') ?>"
                                                                   placeholder="https://example.com/canonical-page">
                                                            <span class="form-help">Specify the preferred URL for this page to avoid duplicate content issues.</span>
                                                        </div>

                                                        <div class="form-field">
                                                            <label class="checkbox-field">
                                                                <input type="checkbox" name="robots_index" class="form-checkbox"
                                                                       <?= ($currentSEO['robots_index'] ?? 1) ? 'checked' : '' ?>>
                                                                <span class="checkbox-label">
                                                                    <i class="fas fa-search"></i>
                                                                    Allow search engines to index this page
                                                                </span>
                                                            </label>
                                                            <span class="form-help">When enabled, search engines can include this page in search results.</span>
                                                        </div>

                                                        <div class="form-field">
                                                            <label class="checkbox-field">
                                                                <input type="checkbox" name="robots_follow" class="form-checkbox"
                                                                       <?= ($currentSEO['robots_follow'] ?? 1) ? 'checked' : '' ?>>
                                                                <span class="checkbox-label">
                                                                    <i class="fas fa-external-link-alt"></i>
                                                                    Allow search engines to follow links
                                                                </span>
                                                            </label>
                                                            <span class="form-help">When enabled, search engines can follow links on this page to discover other pages.</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="form-actions">
                                                    <button type="submit" class="btn btn-primary btn-large">
                                                        <i class="fas fa-save"></i>
                                                        <span>Save Page SEO Settings</span>
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div class="seo-tab-content" id="analytics-tab">
                        <div class="seo-section">
                            <div class="seo-section-header">
                                <h3><i class="fas fa-chart-line"></i> SEO Analytics & Performance</h3>
                                <p>Monitor your website's search engine performance and analytics</p>
                            </div>

                            <div class="analytics-dashboard">
                                <div class="analytics-overview">
                                    <div class="analytics-stat">
                                        <div class="stat-icon analytics">
                                            <i class="fab fa-google"></i>
                                        </div>
                                        <div class="stat-content">
                                            <h4>Google Analytics</h4>
                                            <p class="status <?= !empty($globalSettings['google_analytics']) ? 'active' : 'inactive' ?>">
                                                <?= !empty($globalSettings['google_analytics']) ? 'Active' : 'Not Configured' ?>
                                            </p>
                                            <?php if (!empty($globalSettings['google_analytics'])): ?>
                                                <a href="https://analytics.google.com" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-external-link-alt"></i> View Analytics
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <div class="analytics-stat">
                                        <div class="stat-icon console">
                                            <i class="fas fa-search"></i>
                                        </div>
                                        <div class="stat-content">
                                            <h4>Search Console</h4>
                                            <p class="status <?= !empty($globalSettings['google_search_console']) ? 'active' : 'inactive' ?>">
                                                <?= !empty($globalSettings['google_search_console']) ? 'Verified' : 'Not Verified' ?>
                                            </p>
                                            <a href="https://search.google.com/search-console" target="_blank" class="btn btn-sm btn-primary">
                                                <i class="fas fa-external-link-alt"></i> View Console
                                            </a>
                                        </div>
                                    </div>

                                    <div class="analytics-stat">
                                        <div class="stat-icon sitemap">
                                            <i class="fas fa-sitemap"></i>
                                        </div>
                                        <div class="stat-content">
                                            <h4>XML Sitemap</h4>
                                            <p class="status active">Available</p>
                                            <a href="<?= BASE_URL ?>/sitemap.php" target="_blank" class="btn btn-sm btn-primary">
                                                <i class="fas fa-external-link-alt"></i> View Sitemap
                                            </a>
                                        </div>
                                    </div>

                                    <div class="analytics-stat">
                                        <div class="stat-icon robots">
                                            <i class="fas fa-robot"></i>
                                        </div>
                                        <div class="stat-content">
                                            <h4>Robots.txt</h4>
                                            <p class="status active">Configured</p>
                                            <a href="<?= BASE_URL ?>/robots.txt" target="_blank" class="btn btn-sm btn-primary">
                                                <i class="fas fa-external-link-alt"></i> View Robots.txt
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SEO Tools Tab -->
                    <div class="seo-tab-content" id="tools-tab">
                        <div class="seo-section">
                            <div class="seo-section-header">
                                <h3><i class="fas fa-tools"></i> SEO Tools & Utilities</h3>
                                <p>Helpful tools to analyze and improve your website's SEO</p>
                            </div>

                            <div class="seo-tools-grid">
                                <div class="seo-tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-search-plus"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h4>SEO Analyzer</h4>
                                        <p>Analyze your pages for SEO best practices and get improvement suggestions</p>
                                        <button class="btn btn-primary" onclick="openSEOAnalyzer()">
                                            <i class="fas fa-play"></i> Run Analysis
                                        </button>
                                    </div>
                                </div>

                                <div class="seo-tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-link"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h4>Broken Links Checker</h4>
                                        <p>Scan your website for broken internal and external links</p>
                                        <button class="btn btn-primary" onclick="checkBrokenLinks()">
                                            <i class="fas fa-search"></i> Check Links
                                        </button>
                                    </div>
                                </div>

                                <div class="seo-tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-tachometer-alt"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h4>Page Speed Test</h4>
                                        <p>Test your website's loading speed and performance metrics</p>
                                        <a href="https://pagespeed.web.dev/?url=<?= urlencode(BASE_URL) ?>" target="_blank" class="btn btn-primary">
                                            <i class="fas fa-external-link-alt"></i> Test Speed
                                        </a>
                                    </div>
                                </div>

                                <div class="seo-tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h4>Mobile-Friendly Test</h4>
                                        <p>Check if your website is optimized for mobile devices</p>
                                        <a href="https://search.google.com/test/mobile-friendly?url=<?= urlencode(BASE_URL) ?>" target="_blank" class="btn btn-primary">
                                            <i class="fas fa-external-link-alt"></i> Test Mobile
                                        </a>
                                    </div>
                                </div>

                                <div class="seo-tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-share-alt"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h4>Social Media Preview</h4>
                                        <p>Preview how your pages will look when shared on social media</p>
                                        <button class="btn btn-primary" onclick="previewSocialMedia()">
                                            <i class="fas fa-eye"></i> Preview
                                        </button>
                                    </div>
                                </div>

                                <div class="seo-tool-card">
                                    <div class="tool-icon">
                                        <i class="fas fa-file-export"></i>
                                    </div>
                                    <div class="tool-content">
                                        <h4>Generate Sitemap</h4>
                                        <p>Generate and update your XML sitemap for search engines</p>
                                        <button class="btn btn-primary" onclick="generateSitemap()">
                                            <i class="fas fa-cog"></i> Generate
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        // Enhanced SEO Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            initSEOTabs();
            initSEOTools();
            initCharacterCounters();
            initFormValidation();
        });

        function initSEOTabs() {
            const tabButtons = document.querySelectorAll('.seo-tab-btn');
            const tabContents = document.querySelectorAll('.seo-tab-content');

            console.log('SEO Tabs initialized:', {
                buttons: tabButtons.length,
                contents: tabContents.length
            });

            tabButtons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const tabId = btn.dataset.tab;
                    console.log('Tab clicked:', tabId);

                    // Update buttons
                    tabButtons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');

                    // Hide all content first
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                        content.style.display = 'none';
                        content.style.opacity = '0';
                        content.style.transform = 'translateY(20px)';
                    });

                    // Show the active content with animation
                    const activeContent = document.getElementById(tabId + '-tab');
                    console.log('Active content element:', activeContent);

                    if (activeContent) {
                        activeContent.style.display = 'block';
                        setTimeout(() => {
                            activeContent.classList.add('active');
                            activeContent.style.opacity = '1';
                            activeContent.style.transform = 'translateY(0)';
                            console.log('Tab content shown:', tabId);
                        }, 50);
                    } else {
                        console.error('Tab content not found for:', tabId + '-tab');
                    }
                });
            });

            // Initialize the first tab as active if none is active
            const activeTab = document.querySelector('.seo-tab-content.active');
            if (activeTab) {
                activeTab.style.display = 'block';
                activeTab.style.opacity = '1';
                activeTab.style.transform = 'translateY(0)';
                console.log('Initial active tab set');
            } else {
                // If no tab is active, activate the first one
                const firstTab = document.querySelector('.seo-tab-content');
                const firstButton = document.querySelector('.seo-tab-btn');
                if (firstTab && firstButton) {
                    firstTab.classList.add('active');
                    firstTab.style.display = 'block';
                    firstTab.style.opacity = '1';
                    firstTab.style.transform = 'translateY(0)';
                    firstButton.classList.add('active');
                    console.log('First tab activated by default');
                }
            }
        }

        function initSEOTools() {
            // Add hover effects to tool cards
            const toolCards = document.querySelectorAll('.seo-tool-card');
            toolCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add hover effects to page cards
            const pageCards = document.querySelectorAll('.modern-page-card');
            pageCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        }

        function initCharacterCounters() {
            function updateCharCount(input, maxLength) {
                const current = input.value.length;
                let counter = input.parentNode.querySelector('.char-count');

                if (!counter) {
                    counter = document.createElement('div');
                    counter.className = 'char-count';
                    input.parentNode.appendChild(counter);
                }

                counter.innerHTML = `
                    <span class="count-text">${current}/${maxLength}</span>
                    <div class="count-bar">
                        <div class="count-progress" style="width: ${(current/maxLength)*100}%"></div>
                    </div>
                `;

                // Color coding
                if (current > maxLength) {
                    counter.className = 'char-count over-limit';
                } else if (current > maxLength * 0.8) {
                    counter.className = 'char-count near-limit';
                } else {
                    counter.className = 'char-count normal';
                }
            }

            document.querySelectorAll('input[maxlength], textarea[maxlength]').forEach(input => {
                const maxLength = parseInt(input.getAttribute('maxlength'));
                updateCharCount(input, maxLength);
                input.addEventListener('input', () => updateCharCount(input, maxLength));
            });
        }

        function initFormValidation() {
            const forms = document.querySelectorAll('.modern-seo-form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>Saving...</span>';
                        submitBtn.disabled = true;
                    }
                });
            });
        }

        // SEO Tool Functions
        function openSEOAnalyzer() {
            AdminJS.showAlert('info', 'SEO Analyzer will be available in the next update. Stay tuned!');
        }

        function checkBrokenLinks() {
            AdminJS.showAlert('info', 'Broken Links Checker will scan your website for broken links. Feature coming soon!');
        }

        function previewSocialMedia() {
            AdminJS.showAlert('info', 'Social Media Preview tool will show how your pages appear on social platforms. Coming soon!');
        }

        function generateSitemap() {
            AdminJS.showAlert('info', 'Sitemap generation will create an updated XML sitemap. Feature in development!');
        }

        // Add smooth animations for form sections
        document.querySelectorAll('.form-section').forEach((section, index) => {
            section.style.animationDelay = `${index * 0.1}s`;
            section.classList.add('fade-in-up');
        });
    </script>

    <style>
        .seo-tab-content {
            display: none;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .seo-tab-content.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</body>
</html>
