<?php
/**
 * Database Setup Script for Flori Construction Ltd
 * Run this file once to create the database and tables
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'flori_construction';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Flori Construction - Database Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>";

echo "<h1>Flori Construction Ltd - Database Setup</h1>";

try {
    // Connect to MySQL server (without database)
    echo "<div class='info'>Connecting to MySQL server...</div>";
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<div class='success'>✓ Connected to MySQL server successfully</div>";
    
    // Create database
    echo "<div class='info'>Creating database '$database'...</div>";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<div class='success'>✓ Database '$database' created successfully</div>";
    
    // Connect to the specific database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Read and execute SQL schema
    $sqlFile = __DIR__ . '/database/schema.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("Schema file not found: $sqlFile");
    }
    
    echo "<div class='info'>Reading SQL schema file...</div>";
    $sql = file_get_contents($sqlFile);
    
    // Remove the CREATE DATABASE and USE statements since we're already connected
    $sql = preg_replace('/CREATE DATABASE IF NOT EXISTS.*?;/', '', $sql);
    $sql = preg_replace('/USE.*?;/', '', $sql);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "<div class='info'>Executing SQL statements...</div>";
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            try {
                $pdo->exec($statement);
                
                // Show which table was created
                if (preg_match('/CREATE TABLE\s+(\w+)/i', $statement, $matches)) {
                    echo "<div class='success'>✓ Created table: {$matches[1]}</div>";
                } elseif (preg_match('/INSERT INTO\s+(\w+)/i', $statement, $matches)) {
                    echo "<div class='success'>✓ Inserted data into: {$matches[1]}</div>";
                }
            } catch (PDOException $e) {
                echo "<div class='error'>Error executing statement: " . $e->getMessage() . "</div>";
                echo "<pre>" . htmlspecialchars($statement) . "</pre>";
            }
        }
    }
    
    // Verify tables were created
    echo "<div class='info'>Verifying database setup...</div>";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    $expectedTables = ['users', 'projects', 'services', 'media', 'content', 'testimonials', 'contact_inquiries', 'api_tokens'];
    $missingTables = array_diff($expectedTables, $tables);
    
    if (empty($missingTables)) {
        echo "<div class='success'>✓ All tables created successfully!</div>";
        echo "<div class='success'><strong>Database setup completed!</strong></div>";
        
        echo "<h2>Next Steps:</h2>";
        echo "<ol>";
        echo "<li>Delete this setup.php file for security</li>";
        echo "<li>Visit <a href='admin/login.php'>admin/login.php</a> to access the admin panel</li>";
        echo "<li>Login with username: <strong>admin</strong> and password: <strong>admin123</strong></li>";
        echo "<li>Change the default password immediately</li>";
        echo "<li>Visit <a href='index.php'>index.php</a> to see your website</li>";
        echo "<li>Visit <a href='mobile-app/'>mobile-app/</a> to access the mobile app</li>";
        echo "</ol>";
        
        echo "<h2>Default Login Credentials:</h2>";
        echo "<div class='info'>";
        echo "<strong>Username:</strong> admin<br>";
        echo "<strong>Password:</strong> admin123<br>";
        echo "<strong>Email:</strong> <EMAIL>";
        echo "</div>";
        
        echo "<h2>Created Tables:</h2>";
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
        
    } else {
        echo "<div class='error'>Missing tables: " . implode(', ', $missingTables) . "</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='error'>Setup failed: " . $e->getMessage() . "</div>";
    
    echo "<h2>Manual Setup Instructions:</h2>";
    echo "<div class='info'>";
    echo "<p>If the automatic setup failed, you can set up the database manually:</p>";
    echo "<ol>";
    echo "<li>Open phpMyAdmin or your MySQL client</li>";
    echo "<li>Create a new database called 'flori_construction'</li>";
    echo "<li>Import the file: database/schema.sql</li>";
    echo "<li>Update database credentials in config/database.php if needed</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>Database Configuration:</h2>";
    echo "<pre>";
    echo "Host: $host\n";
    echo "Username: $username\n";
    echo "Password: " . (empty($password) ? '(empty)' : '(set)') . "\n";
    echo "Database: $database";
    echo "</pre>";
}

echo "<hr>";
echo "<p><strong>Important:</strong> Delete this setup.php file after successful setup for security reasons.</p>";

echo "</body></html>";
?>
