{"name": "Flori Construction Admin", "short_name": "<PERSON><PERSON><PERSON>", "description": "Mobile admin app for Flori Construction Ltd website management", "start_url": "/mobile-app/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#e74c3c", "orientation": "portrait-primary", "scope": "/mobile-app/", "lang": "en", "categories": ["business", "productivity"], "icons": [{"src": "icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Dashboard view"}, {"src": "screenshots/mobile-1.png", "sizes": "375x667", "type": "image/png", "form_factor": "narrow", "label": "Mobile dashboard"}], "shortcuts": [{"name": "Add Project", "short_name": "Add Project", "description": "Quickly add a new project", "url": "/mobile-app/#projects?action=add", "icons": [{"src": "icons/shortcut-add.png", "sizes": "96x96"}]}, {"name": "Upload Media", "short_name": "Upload", "description": "Upload new media files", "url": "/mobile-app/#media?action=upload", "icons": [{"src": "icons/shortcut-upload.png", "sizes": "96x96"}]}], "related_applications": [{"platform": "webapp", "url": "https://floriconstructionltd.com/mobile-app/manifest.json"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}