<?php
require_once '../config/config.php';

// Check if user is logged in
requireLogin();
$user = getCurrentUser();

// Handle actions
$action = $_GET['action'] ?? 'list';
$message = '';
$messageType = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add CSRF protection for POST requests
    requireCSRF();

    if ($action === 'add') {
        $clientName = sanitize($_POST['client_name']);
        $company = sanitize($_POST['company']);
        $rating = (int)$_POST['rating'];
        $review = $_POST['review'];
        $projectId = !empty($_POST['project_id']) ? (int)$_POST['project_id'] : null;
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
        $isApproved = isset($_POST['is_approved']) ? 1 : 0;
        $sortOrder = (int)($_POST['sort_order'] ?? 0);

        // Validate input
        if (empty($clientName) || empty($review)) {
            $message = 'Client name and review are required';
            $messageType = 'error';
        } elseif ($rating < 1 || $rating > 5) {
            $message = 'Rating must be between 1 and 5';
            $messageType = 'error';
        } else {
            try {
                $testimonialData = [
                    'client_name' => $clientName,
                    'company' => $company,
                    'rating' => $rating,
                    'review' => $review,
                    'project_id' => $projectId,
                    'is_featured' => $isFeatured,
                    'is_approved' => $isApproved,
                    'sort_order' => $sortOrder,
                    'created_at' => date('Y-m-d H:i:s')
                ];

                $db->insert('testimonials', $testimonialData);
                $message = 'Testimonial added successfully!';
                $messageType = 'success';
                $action = 'list';

            } catch (Exception $e) {
                $message = 'Error adding testimonial: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    } elseif ($action === 'edit') {
        $testimonialId = $_POST['id'];
        $clientName = sanitize($_POST['client_name']);
        $company = sanitize($_POST['company']);
        $rating = (int)$_POST['rating'];
        $review = $_POST['review'];
        $projectId = !empty($_POST['project_id']) ? (int)$_POST['project_id'] : null;
        $isFeatured = isset($_POST['is_featured']) ? 1 : 0;
        $isApproved = isset($_POST['is_approved']) ? 1 : 0;
        $sortOrder = (int)($_POST['sort_order'] ?? 0);

        // Validate input
        if (empty($clientName) || empty($review)) {
            $message = 'Client name and review are required';
            $messageType = 'error';
        } elseif ($rating < 1 || $rating > 5) {
            $message = 'Rating must be between 1 and 5';
            $messageType = 'error';
        } else {
            try {
                $updateData = [
                    'client_name' => $clientName,
                    'company' => $company,
                    'rating' => $rating,
                    'review' => $review,
                    'project_id' => $projectId,
                    'is_featured' => $isFeatured,
                    'is_approved' => $isApproved,
                    'sort_order' => $sortOrder,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $db->update('testimonials', $updateData, 'id = ?', [$testimonialId]);
                $message = 'Testimonial updated successfully!';
                $messageType = 'success';
                $action = 'list';

            } catch (Exception $e) {
                $message = 'Error updating testimonial: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    } elseif ($action === 'delete') {
        $testimonialId = $_POST['id'];

        try {
            $db->query("DELETE FROM testimonials WHERE id = ?", [$testimonialId]);
            $message = 'Testimonial deleted successfully!';
            $messageType = 'success';
            $action = 'list';

        } catch (Exception $e) {
            $message = 'Error deleting testimonial: ' . $e->getMessage();
            $messageType = 'error';
        }
    } elseif ($action === 'toggle_status') {
        $testimonialId = $_POST['id'];
        $field = $_POST['field']; // 'is_approved' or 'is_featured'
        $value = (int)$_POST['value'];

        if (in_array($field, ['is_approved', 'is_featured'])) {
            try {
                $db->update('testimonials', [
                    $field => $value,
                    'updated_at' => date('Y-m-d H:i:s')
                ], 'id = ?', [$testimonialId]);

                $message = ucfirst(str_replace('_', ' ', $field)) . ' updated successfully!';
                $messageType = 'success';

            } catch (Exception $e) {
                $message = 'Error updating status: ' . $e->getMessage();
                $messageType = 'error';
            }
        }
    }
}

// Get testimonial for editing
$editTestimonial = null;
if ($action === 'edit' && isset($_GET['id'])) {
    $editTestimonial = $db->fetchOne("SELECT * FROM testimonials WHERE id = ?", [$_GET['id']]);
    if (!$editTestimonial) {
        $action = 'list';
        $message = 'Testimonial not found';
        $messageType = 'error';
    }
}

// Get testimonials list
if ($action === 'list') {
    $page = (int)($_GET['page'] ?? 1);
    $limit = ADMIN_ITEMS_PER_PAGE;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';
    $statusFilter = $_GET['status'] ?? '';

    $where = ['1=1'];
    $params = [];

    if ($search) {
        $where[] = '(t.client_name LIKE ? OR t.company LIKE ? OR t.review LIKE ?)';
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }

    if ($statusFilter === 'approved') {
        $where[] = 't.is_approved = 1';
    } elseif ($statusFilter === 'pending') {
        $where[] = 't.is_approved = 0';
    } elseif ($statusFilter === 'featured') {
        $where[] = 't.is_featured = 1';
    }

    $whereClause = implode(' AND ', $where);

    $totalQuery = "SELECT COUNT(*) as total FROM testimonials t WHERE $whereClause";
    $total = $db->fetchOne($totalQuery, $params)['total'];

    $testimonialsQuery = "SELECT t.*, p.title as project_title
                         FROM testimonials t
                         LEFT JOIN projects p ON t.project_id = p.id
                         WHERE $whereClause
                         ORDER BY t.sort_order ASC, t.created_at DESC
                         LIMIT $limit OFFSET $offset";
    $testimonials = $db->fetchAll($testimonialsQuery, $params);

    $totalPages = ceil($total / $limit);
}

// Get projects for dropdown
$projects = $db->fetchAll("SELECT id, title FROM projects WHERE is_active = 1 ORDER BY title");
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testimonials Management - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Enhanced Testimonials Specific Styles */
        .testimonials-page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: var(--border-radius-xl);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            color: var(--white);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .testimonials-page-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(30%, -30%);
        }

        .testimonials-page-header .header-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-xl);
            position: relative;
            z-index: 2;
        }

        .testimonials-page-header .page-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            margin: 0 0 var(--spacing-sm) 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .testimonials-page-header .page-description {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            margin: 0;
            line-height: var(--line-height-relaxed);
        }

        .testimonials-stats {
            margin-top: var(--spacing-xl);
            display: flex;
            justify-content: center;
            gap: var(--spacing-xl);
        }

        .testimonials-stats .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.15);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-radius: var(--border-radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
        }

        .testimonials-stats .stat-number {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            display: block;
            margin-bottom: var(--spacing-xs);
        }

        .testimonials-stats .stat-label {
            font-size: var(--font-size-sm);
            opacity: 0.9;
            font-weight: var(--font-weight-medium);
        }

        .testimonial-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            transition: all var(--transition-base);
            overflow: hidden;
            position: relative;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
            opacity: 0;
            transition: opacity var(--transition-base);
        }

        .testimonial-card:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .testimonial-card:hover::before {
            opacity: 1;
        }

        .testimonial-header {
            padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-md);
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--spacing-md);
        }

        .client-info h4 {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-800);
            margin: 0 0 var(--spacing-xs) 0;
        }

        .client-info .company {
            font-size: var(--font-size-sm);
            color: var(--gray-600);
            font-weight: var(--font-weight-medium);
        }

        .rating-display {
            color: #fbbf24;
            font-size: var(--font-size-lg);
            display: flex;
            gap: 2px;
        }

        .testimonial-content {
            padding: 0 var(--spacing-xl) var(--spacing-md);
        }

        .testimonial-preview {
            background: var(--gray-50);
            padding: var(--spacing-lg);
            border-radius: var(--border-radius-lg);
            border-left: 4px solid var(--primary-color);
            position: relative;
        }

        .testimonial-preview::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: var(--spacing-md);
            font-size: 3rem;
            color: var(--primary-color);
            font-family: Georgia, serif;
            opacity: 0.3;
        }

        .testimonial-preview p {
            margin: 0;
            font-style: italic;
            color: var(--gray-700);
            line-height: var(--line-height-relaxed);
            font-size: var(--font-size-base);
        }

        .project-info {
            padding: 0 var(--spacing-xl) var(--spacing-md);
        }

        .project-tag {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--primary-color);
            color: var(--white);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
        }

        .testimonial-actions {
            padding: var(--spacing-md) var(--spacing-xl) var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--spacing-md);
        }

        .status-toggles {
            display: flex;
            gap: var(--spacing-sm);
        }

        .status-toggle {
            cursor: pointer;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-sm);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            border: none;
            transition: all var(--transition-base);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-toggle.active {
            background: var(--success-color);
            color: var(--white);
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.3);
        }

        .status-toggle.inactive {
            background: var(--gray-400);
            color: var(--white);
        }

        .status-toggle:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }

        .testimonial-meta {
            padding: 0 var(--spacing-xl) var(--spacing-md);
            font-size: var(--font-size-xs);
            color: var(--gray-500);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .filters-card {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-xl);
        }

        .filters-content {
            padding: var(--spacing-xl);
        }

        .filters-form {
            display: flex;
            gap: var(--spacing-lg);
            align-items: end;
            flex-wrap: wrap;
        }

        .filter-group {
            flex: 1;
            min-width: 200px;
        }

        .filter-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        .empty-state {
            text-align: center;
            padding: var(--spacing-3xl) var(--spacing-xl);
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
        }

        .empty-state-icon {
            font-size: 4rem;
            color: var(--gray-300);
            margin-bottom: var(--spacing-xl);
        }

        .empty-state h3 {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--gray-700);
            margin: 0 0 var(--spacing-md) 0;
        }

        .empty-state p {
            font-size: var(--font-size-base);
            color: var(--gray-600);
            margin: 0 0 var(--spacing-xl) 0;
        }

        /* Form Enhancement */
        .form-header {
            background: var(--white);
            border-radius: var(--border-radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            margin-bottom: var(--spacing-xl);
            overflow: hidden;
        }

        .form-header-content {
            padding: var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--info-color) 100%);
            color: var(--white);
        }

        .form-header h2 {
            font-size: var(--font-size-2xl);
            font-weight: var(--font-weight-bold);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        /* Form Actions */
        .form-actions {
            display: flex;
            gap: var(--spacing-lg);
            justify-content: flex-start;
            align-items: center;
            margin-top: var(--spacing-xl);
            padding: var(--spacing-xl);
            background: var(--gray-50);
            border-radius: var(--border-radius-xl);
            border: 1px solid var(--gray-200);
        }

        /* Enhanced Form Check Styles */
        .form-check-group {
            margin-bottom: var(--spacing-md);
        }

        .form-check-label {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-medium);
            color: var(--gray-700);
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: var(--border-radius-md);
            transition: all var(--transition-base);
        }

        .form-check-label:hover {
            background: var(--gray-50);
        }

        .form-check-input {
            width: 18px;
            height: 18px;
            margin: 0;
            accent-color: var(--primary-color);
        }

        .form-text {
            margin-top: var(--spacing-xs);
            font-size: var(--font-size-sm);
            color: var(--gray-600);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .testimonials-page-header .header-content {
                flex-direction: column;
                text-align: center;
            }

            .testimonials-stats {
                flex-direction: column;
                align-items: center;
            }

            .testimonial-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .testimonial-actions {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-md);
            }

            .status-toggles {
                justify-content: center;
            }

            .filters-form {
                flex-direction: column;
            }

            .filter-group {
                min-width: auto;
            }

            .filter-actions {
                justify-content: center;
            }

            .form-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .form-actions .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                <!-- Enhanced Testimonials Header -->
                <div class="testimonials-page-header">
                    <div class="header-content">
                        <div class="header-info">
                            <h1 class="page-title">
                                <i class="fas fa-quote-left"></i>
                                Customer Testimonials
                            </h1>
                            <p class="page-description">Manage customer reviews and testimonials to showcase your work quality</p>
                        </div>
                        <div class="header-actions">
                            <a href="?action=add" class="btn btn-success btn-lg">
                                <i class="fas fa-plus"></i>
                                <span>Add New Testimonial</span>
                            </a>
                        </div>
                    </div>

                    <?php
                    // Calculate additional stats
                    $approvedCount = $db->fetchOne("SELECT COUNT(*) as count FROM testimonials WHERE is_approved = 1")['count'];
                    $featuredCount = $db->fetchOne("SELECT COUNT(*) as count FROM testimonials WHERE is_featured = 1")['count'];
                    $avgRating = $db->fetchOne("SELECT AVG(rating) as avg FROM testimonials WHERE is_approved = 1")['avg'];
                    ?>

                    <div class="testimonials-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?= $total ?></span>
                            <span class="stat-label">Total Reviews</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $approvedCount ?></span>
                            <span class="stat-label">Approved</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $featuredCount ?></span>
                            <span class="stat-label">Featured</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?= $avgRating ? number_format($avgRating, 1) : '0.0' ?></span>
                            <span class="stat-label">Avg Rating</span>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Filters -->
                <div class="filters-card">
                    <div class="filters-content">
                        <form method="GET" class="filters-form">
                            <input type="hidden" name="action" value="list">

                            <div class="filter-group">
                                <label for="search" class="form-label">
                                    <i class="fas fa-search"></i>
                                    Search Testimonials
                                </label>
                                <input type="text"
                                       id="search"
                                       name="search"
                                       placeholder="Search by client name, company, or review content..."
                                       value="<?= htmlspecialchars($search) ?>"
                                       class="form-control">
                            </div>

                            <div class="filter-group">
                                <label for="status" class="form-label">
                                    <i class="fas fa-filter"></i>
                                    Status Filter
                                </label>
                                <select id="status" name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="approved" <?= $statusFilter === 'approved' ? 'selected' : '' ?>>
                                        <i class="fas fa-check"></i> Approved
                                    </option>
                                    <option value="pending" <?= $statusFilter === 'pending' ? 'selected' : '' ?>>
                                        <i class="fas fa-clock"></i> Pending Review
                                    </option>
                                    <option value="featured" <?= $statusFilter === 'featured' ? 'selected' : '' ?>>
                                        <i class="fas fa-star"></i> Featured
                                    </option>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                    Apply Filters
                                </button>
                                <?php if ($search || $statusFilter): ?>
                                <a href="?action=list" class="btn btn-outline">
                                    <i class="fas fa-times"></i>
                                    Clear All
                                </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Testimonials Grid -->
                <?php if (empty($testimonials)): ?>
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-quote-left"></i>
                    </div>
                    <h3>No testimonials found</h3>
                    <p>
                        <?php if ($search || $statusFilter): ?>
                            No testimonials match your current filters. Try adjusting your search criteria.
                        <?php else: ?>
                            Start building your reputation by adding your first customer testimonial.
                        <?php endif; ?>
                    </p>
                    <a href="?action=add" class="btn btn-success btn-lg">
                        <i class="fas fa-plus"></i>
                        Add Your First Testimonial
                    </a>
                </div>
                <?php else: ?>
                <div class="row">
                    <?php foreach ($testimonials as $testimonial): ?>
                    <div class="col-md-6 mb-4">
                        <div class="testimonial-card">
                            <!-- Testimonial Header -->
                            <div class="testimonial-header">
                                <div class="client-info">
                                    <h4><?= htmlspecialchars($testimonial['client_name']) ?></h4>
                                    <?php if ($testimonial['company']): ?>
                                    <div class="company"><?= htmlspecialchars($testimonial['company']) ?></div>
                                    <?php endif; ?>
                                </div>
                                <div class="rating-display">
                                    <?php for ($i = 1; $i <= 5; $i++): ?>
                                    <i class="fas fa-star<?= $i <= $testimonial['rating'] ? '' : '-o' ?>"></i>
                                    <?php endfor; ?>
                                </div>
                            </div>

                            <!-- Testimonial Content -->
                            <div class="testimonial-content">
                                <div class="testimonial-preview">
                                    <p>
                                        <?= htmlspecialchars(substr($testimonial['review'], 0, 200)) ?><?= strlen($testimonial['review']) > 200 ? '...' : '' ?>
                                    </p>
                                </div>
                            </div>

                            <!-- Project Info -->
                            <?php if ($testimonial['project_title']): ?>
                            <div class="project-info">
                                <div class="project-tag">
                                    <i class="fas fa-building"></i>
                                    <?= htmlspecialchars($testimonial['project_title']) ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Testimonial Actions -->
                            <div class="testimonial-actions">
                                <div class="status-toggles">
                                    <button class="status-toggle <?= $testimonial['is_approved'] ? 'active' : 'inactive' ?>"
                                            onclick="toggleStatus(<?= $testimonial['id'] ?>, 'is_approved', <?= $testimonial['is_approved'] ? 0 : 1 ?>)"
                                            title="<?= $testimonial['is_approved'] ? 'Click to mark as pending' : 'Click to approve' ?>">
                                        <i class="fas fa-<?= $testimonial['is_approved'] ? 'check' : 'clock' ?>"></i>
                                        <?= $testimonial['is_approved'] ? 'Approved' : 'Pending' ?>
                                    </button>
                                    <button class="status-toggle <?= $testimonial['is_featured'] ? 'active' : 'inactive' ?>"
                                            onclick="toggleStatus(<?= $testimonial['id'] ?>, 'is_featured', <?= $testimonial['is_featured'] ? 0 : 1 ?>)"
                                            title="<?= $testimonial['is_featured'] ? 'Click to unfeature' : 'Click to feature' ?>">
                                        <i class="fas fa-<?= $testimonial['is_featured'] ? 'star' : 'star-o' ?>"></i>
                                        <?= $testimonial['is_featured'] ? 'Featured' : 'Regular' ?>
                                    </button>
                                </div>
                                <div class="action-buttons">
                                    <a href="?action=edit&id=<?= $testimonial['id'] ?>"
                                       class="btn btn-sm btn-outline"
                                       title="Edit testimonial">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" style="display: inline;"
                                          onsubmit="return confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')">
                                        <?= getCSRFField() ?>
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="id" value="<?= $testimonial['id'] ?>">
                                        <button type="submit"
                                                class="btn btn-sm btn-danger"
                                                title="Delete testimonial">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <!-- Testimonial Meta -->
                            <div class="testimonial-meta">
                                <span>
                                    <i class="fas fa-calendar-alt"></i>
                                    Added: <?= formatDate($testimonial['created_at']) ?>
                                </span>
                                <?php if ($testimonial['sort_order'] > 0): ?>
                                <span>
                                    <i class="fas fa-sort-numeric-down"></i>
                                    Order: <?= $testimonial['sort_order'] ?>
                                </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $page): ?>
                        <span class="active"><?= $i ?></span>
                        <?php else: ?>
                        <a href="?action=list&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $statusFilter ? '&status=' . $statusFilter : '' ?>"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>
                </div>
                <?php endif; ?>
                <?php endif; ?>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                <!-- Enhanced Add/Edit Form -->
                <div class="form-header">
                    <div class="form-header-content">
                        <h2>
                            <i class="fas fa-<?= $action === 'add' ? 'plus' : 'edit' ?>"></i>
                            <?= $action === 'add' ? 'Add New Testimonial' : 'Edit Testimonial' ?>
                        </h2>
                        <a href="?action=list" class="btn btn-light">
                            <i class="fas fa-arrow-left"></i>
                            Back to Testimonials
                        </a>
                    </div>
                </div>

                <form method="POST">
                    <?= getCSRFField() ?>
                    <?php if ($action === 'edit'): ?>
                    <input type="hidden" name="id" value="<?= $editTestimonial['id'] ?>">
                    <?php endif; ?>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>
                                <i class="fas fa-info-circle"></i>
                                Testimonial Information
                            </h3>
                        </div>
                        <div class="card-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="client_name" class="form-label">
                                            <i class="fas fa-user"></i>
                                            Client Name *
                                        </label>
                                        <input type="text"
                                               id="client_name"
                                               name="client_name"
                                               class="form-control"
                                               placeholder="Enter client's full name"
                                               value="<?= $editTestimonial ? htmlspecialchars($editTestimonial['client_name']) : '' ?>"
                                               required>
                                    </div>

                                    <div class="form-group">
                                        <label for="company" class="form-label">
                                            <i class="fas fa-building"></i>
                                            Company
                                        </label>
                                        <input type="text"
                                               id="company"
                                               name="company"
                                               class="form-control"
                                               placeholder="Company or organization name"
                                               value="<?= $editTestimonial ? htmlspecialchars($editTestimonial['company']) : '' ?>">
                                    </div>

                                    <div class="form-group">
                                        <label for="rating" class="form-label">
                                            <i class="fas fa-star"></i>
                                            Rating *
                                        </label>
                                        <select id="rating" name="rating" class="form-control" required>
                                            <option value="">Select Rating</option>
                                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <option value="<?= $i ?>" <?= ($editTestimonial && $editTestimonial['rating'] == $i) ? 'selected' : '' ?>>
                                                <?= str_repeat('★', $i) . str_repeat('☆', 5-$i) ?> (<?= $i ?> Star<?= $i > 1 ? 's' : '' ?>)
                                            </option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="project_id" class="form-label">
                                            <i class="fas fa-hammer"></i>
                                            Related Project
                                        </label>
                                        <select id="project_id" name="project_id" class="form-control">
                                            <option value="">No Project Associated</option>
                                            <?php foreach ($projects as $project): ?>
                                            <option value="<?= $project['id'] ?>"
                                                    <?= ($editTestimonial && $editTestimonial['project_id'] == $project['id']) ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($project['title']) ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="review" class="form-label">
                                            <i class="fas fa-quote-left"></i>
                                            Customer Review *
                                        </label>
                                        <textarea id="review"
                                                  name="review"
                                                  class="form-control"
                                                  rows="8"
                                                  placeholder="Enter the customer's testimonial or review..."
                                                  required><?= $editTestimonial ? htmlspecialchars($editTestimonial['review']) : '' ?></textarea>
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            Write the customer's feedback in their own words
                                        </small>
                                    </div>

                                    <div class="form-group">
                                        <label for="sort_order" class="form-label">
                                            <i class="fas fa-sort-numeric-down"></i>
                                            Display Order
                                        </label>
                                        <input type="number"
                                               id="sort_order"
                                               name="sort_order"
                                               class="form-control"
                                               min="0"
                                               placeholder="0"
                                               value="<?= $editTestimonial ? $editTestimonial['sort_order'] : 0 ?>">
                                        <small class="form-text text-muted">
                                            <i class="fas fa-info-circle"></i>
                                            Lower numbers appear first (0 = default order)
                                        </small>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-check-group">
                                            <label class="form-check-label">
                                                <input type="checkbox"
                                                       name="is_approved"
                                                       class="form-check-input"
                                                       <?= ($editTestimonial && $editTestimonial['is_approved']) ? 'checked' : '' ?>>
                                                <span class="checkmark"></span>
                                                <i class="fas fa-check-circle"></i>
                                                Approved for Public Display
                                            </label>
                                            <small class="form-text text-muted">
                                                Only approved testimonials will be visible on the website
                                            </small>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-check-group">
                                            <label class="form-check-label">
                                                <input type="checkbox"
                                                       name="is_featured"
                                                       class="form-check-input"
                                                       <?= ($editTestimonial && $editTestimonial['is_featured']) ? 'checked' : '' ?>>
                                                <span class="checkmark"></span>
                                                <i class="fas fa-star"></i>
                                                Featured Testimonial
                                            </label>
                                            <small class="form-text text-muted">
                                                Featured testimonials get priority placement
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-save"></i>
                            <?= $action === 'add' ? 'Add Testimonial' : 'Update Testimonial' ?>
                        </button>
                        <a href="?action=list" class="btn btn-outline btn-lg">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    </div>
                </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Hidden form for status toggles -->
    <form id="statusForm" method="POST" style="display: none;">
        <?= getCSRFField() ?>
        <input type="hidden" name="action" value="toggle_status">
        <input type="hidden" name="id" id="statusId">
        <input type="hidden" name="field" id="statusField">
        <input type="hidden" name="value" id="statusValue">
    </form>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        function toggleStatus(id, field, value) {
            document.getElementById('statusId').value = id;
            document.getElementById('statusField').value = field;
            document.getElementById('statusValue').value = value;
            document.getElementById('statusForm').submit();
        }
    </script>
</body>
</html>
