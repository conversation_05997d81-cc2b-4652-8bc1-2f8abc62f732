<?php
/**
 * Test Runner - Main Testing Dashboard
 * Provides access to all admin dashboard tests
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();
$pageTitle = 'Test Runner';
$currentPage = 'test-runner.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Runner - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .test-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .test-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 1.5rem;
        }
        
        .test-icon.responsive { background: linear-gradient(135deg, #3498db, #2980b9); }
        .test-icon.mobile { background: linear-gradient(135deg, #e74c3c, #c0392b); }
        .test-icon.accessibility { background: linear-gradient(135deg, #27ae60, #229954); }
        .test-icon.forms { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .test-icon.browser { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 1rem;
        }
        
        .test-description {
            color: #6c757d;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .test-features {
            list-style: none;
            margin: 1rem 0;
        }
        
        .test-features li {
            padding: 0.25rem 0;
            color: #495057;
            font-size: 0.9rem;
        }
        
        .test-features li::before {
            content: '✓';
            color: #27ae60;
            font-weight: bold;
            margin-right: 0.5rem;
        }
        
        .test-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .test-btn {
            flex: 1;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            text-decoration: none;
            text-align: center;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .test-btn.primary {
            background: #3498db;
            color: white;
        }
        
        .test-btn.primary:hover {
            background: #2980b9;
            transform: translateY(-1px);
        }
        
        .test-btn.secondary {
            background: #f8f9fa;
            color: #495057;
            border: 1px solid #dee2e6;
        }
        
        .test-btn.secondary:hover {
            background: #e9ecef;
        }
        
        .overview-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            border-radius: 16px;
            margin-bottom: 3rem;
            text-align: center;
        }
        
        .overview-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .overview-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .quick-stat {
            text-align: center;
        }
        
        .quick-stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .quick-stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        .instructions {
            background: #e3f2fd;
            border: 1px solid #3498db;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .instructions h3 {
            color: #2980b9;
            margin-bottom: 1rem;
        }
        
        .instructions ol {
            color: #495057;
            line-height: 1.6;
        }
        
        .instructions li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Overview Section -->
                <div class="overview-section">
                    <h1 class="overview-title">
                        <i class="fas fa-flask"></i>
                        Admin Dashboard Test Suite
                    </h1>
                    <p class="overview-subtitle">
                        Comprehensive testing tools for validating admin dashboard functionality, 
                        responsiveness, accessibility, and cross-browser compatibility.
                    </p>
                    
                    <div class="quick-stats">
                        <div class="quick-stat">
                            <div class="quick-stat-number">5</div>
                            <div class="quick-stat-label">Test Categories</div>
                        </div>
                        <div class="quick-stat">
                            <div class="quick-stat-number">50+</div>
                            <div class="quick-stat-label">Individual Tests</div>
                        </div>
                        <div class="quick-stat">
                            <div class="quick-stat-number">100%</div>
                            <div class="quick-stat-label">Coverage</div>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="instructions">
                    <h3><i class="fas fa-info-circle"></i> How to Use the Test Suite</h3>
                    <ol>
                        <li><strong>Choose a test category</strong> from the options below based on what you want to test</li>
                        <li><strong>Run the tests</strong> by clicking the "Run Test" button for each category</li>
                        <li><strong>Follow the instructions</strong> on each test page to perform manual testing</li>
                        <li><strong>Check the results</strong> and note any issues or failures</li>
                        <li><strong>Test on multiple devices</strong> and browsers for comprehensive coverage</li>
                    </ol>
                </div>

                <!-- Test Categories -->
                <div class="test-grid">
                    <!-- Responsive Design Test -->
                    <div class="test-card">
                        <div class="test-icon responsive">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h2 class="test-title">Responsive Design Test</h2>
                        <p class="test-description">
                            Test the admin dashboard layout across different screen sizes and devices. 
                            Verify that all components adapt properly to mobile, tablet, and desktop viewports.
                        </p>
                        <ul class="test-features">
                            <li>Screen size indicators</li>
                            <li>Grid layout responsiveness</li>
                            <li>Navigation adaptation</li>
                            <li>Content reflow testing</li>
                            <li>Breakpoint validation</li>
                        </ul>
                        <div class="test-actions">
                            <a href="test-responsive.php" class="test-btn primary">
                                <i class="fas fa-play"></i> Run Test
                            </a>
                            <button class="test-btn secondary" onclick="openInNewTab('test-responsive.php')">
                                <i class="fas fa-external-link-alt"></i> New Tab
                            </button>
                        </div>
                    </div>

                    <!-- Mobile Navigation Test -->
                    <div class="test-card">
                        <div class="test-icon mobile">
                            <i class="fas fa-bars"></i>
                        </div>
                        <h2 class="test-title">Mobile Navigation Test</h2>
                        <p class="test-description">
                            Test mobile navigation functionality including sidebar toggle, touch interactions, 
                            and gesture support. Verify that navigation works smoothly on touch devices.
                        </p>
                        <ul class="test-features">
                            <li>Sidebar toggle functionality</li>
                            <li>Touch event handling</li>
                            <li>Swipe gesture detection</li>
                            <li>Dropdown menu behavior</li>
                            <li>Performance monitoring</li>
                        </ul>
                        <div class="test-actions">
                            <a href="test-mobile-nav.php" class="test-btn primary">
                                <i class="fas fa-play"></i> Run Test
                            </a>
                            <button class="test-btn secondary" onclick="openInNewTab('test-mobile-nav.php')">
                                <i class="fas fa-external-link-alt"></i> New Tab
                            </button>
                        </div>
                    </div>

                    <!-- Accessibility Test -->
                    <div class="test-card">
                        <div class="test-icon accessibility">
                            <i class="fas fa-universal-access"></i>
                        </div>
                        <h2 class="test-title">Accessibility Test</h2>
                        <p class="test-description">
                            Test accessibility features including screen reader compatibility, keyboard navigation, 
                            color contrast, and WCAG 2.1 AA compliance.
                        </p>
                        <ul class="test-features">
                            <li>ARIA labels and roles</li>
                            <li>Keyboard navigation</li>
                            <li>Color contrast ratios</li>
                            <li>Screen reader support</li>
                            <li>Focus management</li>
                        </ul>
                        <div class="test-actions">
                            <a href="test-accessibility.php" class="test-btn primary">
                                <i class="fas fa-play"></i> Run Test
                            </a>
                            <button class="test-btn secondary" onclick="openInNewTab('test-accessibility.php')">
                                <i class="fas fa-external-link-alt"></i> New Tab
                            </button>
                        </div>
                    </div>

                    <!-- Form Submission Test -->
                    <div class="test-card">
                        <div class="test-icon forms">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <h2 class="test-title">Form Submission & Loading States</h2>
                        <p class="test-description">
                            Test form functionality including validation, AJAX submissions, loading states, 
                            file uploads, and error handling.
                        </p>
                        <ul class="test-features">
                            <li>Form validation</li>
                            <li>AJAX submissions</li>
                            <li>Loading state indicators</li>
                            <li>File upload handling</li>
                            <li>Real-time validation</li>
                        </ul>
                        <div class="test-actions">
                            <a href="test-forms.php" class="test-btn primary">
                                <i class="fas fa-play"></i> Run Test
                            </a>
                            <button class="test-btn secondary" onclick="openInNewTab('test-forms.php')">
                                <i class="fas fa-external-link-alt"></i> New Tab
                            </button>
                        </div>
                    </div>

                    <!-- Browser Compatibility Test -->
                    <div class="test-card">
                        <div class="test-icon browser">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h2 class="test-title">Cross-Browser Compatibility</h2>
                        <p class="test-description">
                            Test browser compatibility and feature detection. Verify that the admin dashboard 
                            works correctly across different browsers and versions.
                        </p>
                        <ul class="test-features">
                            <li>Browser feature detection</li>
                            <li>CSS support testing</li>
                            <li>JavaScript compatibility</li>
                            <li>HTML5 feature support</li>
                            <li>Performance metrics</li>
                        </ul>
                        <div class="test-actions">
                            <a href="test-browser-compatibility.php" class="test-btn primary">
                                <i class="fas fa-play"></i> Run Test
                            </a>
                            <button class="test-btn secondary" onclick="openInNewTab('test-browser-compatibility.php')">
                                <i class="fas fa-external-link-alt"></i> New Tab
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="test-card">
                    <h2 class="test-title">
                        <i class="fas fa-rocket"></i>
                        Quick Actions
                    </h2>
                    <p class="test-description">
                        Run multiple tests or access testing tools quickly.
                    </p>
                    
                    <div class="test-actions" style="margin-top: 1rem;">
                        <button class="test-btn primary" onclick="runAllTests()">
                            <i class="fas fa-play-circle"></i> Open All Tests
                        </button>
                        <button class="test-btn secondary" onclick="clearTestData()">
                            <i class="fas fa-trash"></i> Clear Test Data
                        </button>
                        <a href="index.php" class="test-btn secondary">
                            <i class="fas fa-home"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        function openInNewTab(url) {
            window.open(url, '_blank');
        }
        
        function runAllTests() {
            const tests = [
                'test-responsive.php',
                'test-mobile-nav.php',
                'test-accessibility.php',
                'test-forms.php',
                'test-browser-compatibility.php'
            ];
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    window.open(test, '_blank');
                }, index * 500); // Stagger the opening to avoid overwhelming the browser
            });
            
            if (window.AdminJS && window.AdminJS.showAlert) {
                window.AdminJS.showAlert('info', `Opening ${tests.length} test pages...`);
            }
        }
        
        function clearTestData() {
            // Clear any test data from localStorage
            const keys = Object.keys(localStorage);
            const testKeys = keys.filter(key => key.startsWith('admin_test_') || key.startsWith('test_'));
            
            testKeys.forEach(key => localStorage.removeItem(key));
            
            if (window.AdminJS && window.AdminJS.showAlert) {
                window.AdminJS.showAlert('success', `Cleared ${testKeys.length} test data entries`);
            }
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            if (window.AdminJS && window.AdminJS.showAlert) {
                window.AdminJS.showAlert('info', 'Test suite loaded successfully!');
            }
        });
    </script>
</body>
</html>
