<?php
/**
 * Fix Admin User - Create or Reset Admin User
 * This script creates the admin user or resets the password
 */

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Admin User - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #0c5460; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .form-group { margin: 15px 0; }
        .form-control { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🔧 Fix Admin User</h1>
    <p>This tool will help you create or reset the admin user for the mobile app authentication.</p>

    <?php
    try {
        require_once 'config/config.php';
        echo "<div class='success'>✅ Database connection successful</div>";
        
        // Check if users table exists
        $tables = $db->fetchAll("SHOW TABLES LIKE 'users'");
        if (empty($tables)) {
            echo "<div class='error'>❌ Users table doesn't exist. Please run setup.php first.</div>";
            echo "<p><a href='setup.php' class='btn'>Run Database Setup</a></p>";
            exit;
        }
        
        echo "<div class='success'>✅ Users table exists</div>";
        
        // Check current users
        echo "<h2>Current Users in Database:</h2>";
        $users = $db->fetchAll("SELECT id, username, email, full_name, role, is_active, created_at FROM users");
        
        if (empty($users)) {
            echo "<div class='warning'>⚠️ No users found in database</div>";
        } else {
            echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Full Name</th><th>Role</th><th>Active</th><th>Created</th></tr>";
            foreach ($users as $user) {
                $activeStatus = $user['is_active'] ? '✅ Yes' : '❌ No';
                echo "<tr>";
                echo "<td>{$user['id']}</td>";
                echo "<td>{$user['username']}</td>";
                echo "<td>{$user['email']}</td>";
                echo "<td>{$user['full_name']}</td>";
                echo "<td>{$user['role']}</td>";
                echo "<td>$activeStatus</td>";
                echo "<td>{$user['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            if ($action === 'create_admin') {
                $username = trim($_POST['username'] ?? 'admin');
                $email = trim($_POST['email'] ?? '<EMAIL>');
                $password = $_POST['password'] ?? 'admin123';
                $fullName = trim($_POST['full_name'] ?? 'Administrator');
                
                if (empty($username) || empty($email) || empty($password)) {
                    echo "<div class='error'>❌ All fields are required</div>";
                } else {
                    try {
                        // Check if user already exists
                        $existingUser = $db->fetchOne("SELECT id FROM users WHERE username = ? OR email = ?", [$username, $email]);
                        
                        if ($existingUser) {
                            // Update existing user
                            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                            $db->update('users', [
                                'email' => $email,
                                'password_hash' => $passwordHash,
                                'full_name' => $fullName,
                                'role' => 'admin',
                                'is_active' => 1,
                                'updated_at' => date('Y-m-d H:i:s')
                            ], 'username = ?', [$username]);
                            
                            echo "<div class='success'>✅ Admin user updated successfully!</div>";
                        } else {
                            // Create new user
                            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
                            $db->insert('users', [
                                'username' => $username,
                                'email' => $email,
                                'password_hash' => $passwordHash,
                                'full_name' => $fullName,
                                'role' => 'admin',
                                'is_active' => 1,
                                'created_at' => date('Y-m-d H:i:s')
                            ]);
                            
                            echo "<div class='success'>✅ Admin user created successfully!</div>";
                        }
                        
                        echo "<div class='info'>You can now login with:</div>";
                        echo "<pre>Username: $username\nPassword: $password</pre>";
                        
                        // Test the login immediately
                        echo "<h3>Testing Login...</h3>";
                        $testUser = $db->fetchOne(
                            "SELECT id, username, email, password_hash, full_name, role, is_active FROM users WHERE username = ? AND is_active = 1",
                            [$username]
                        );
                        
                        if ($testUser && password_verify($password, $testUser['password_hash'])) {
                            echo "<div class='success'>✅ Login test successful! Authentication should work now.</div>";
                            echo "<p><a href='mobile-app/' class='btn'>Test Mobile App</a></p>";
                            echo "<p><a href='debug-auth.php' class='btn'>Run Debug Tool Again</a></p>";
                        } else {
                            echo "<div class='error'>❌ Login test failed. There might be an issue with password hashing.</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ Error creating/updating user: " . $e->getMessage() . "</div>";
                    }
                }
            }
            
            if ($action === 'delete_user') {
                $userId = (int)$_POST['user_id'];
                if ($userId > 0) {
                    try {
                        $db->delete('users', 'id = ?', [$userId]);
                        echo "<div class='success'>✅ User deleted successfully</div>";
                    } catch (Exception $e) {
                        echo "<div class='error'>❌ Error deleting user: " . $e->getMessage() . "</div>";
                    }
                }
            }
        }
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Database error: " . $e->getMessage() . "</div>";
        echo "<p>Please check your database configuration and make sure the database is set up correctly.</p>";
        echo "<p><a href='setup.php' class='btn'>Run Database Setup</a></p>";
        exit;
    }
    ?>

    <h2>Create/Reset Admin User</h2>
    <form method="POST">
        <input type="hidden" name="action" value="create_admin">
        
        <div class="form-group">
            <label for="username">Username:</label><br>
            <input type="text" id="username" name="username" value="admin" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label for="email">Email:</label><br>
            <input type="email" id="email" name="email" value="<EMAIL>" class="form-control" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label><br>
            <input type="text" id="password" name="password" value="admin123" class="form-control" required>
            <small>You can change this to any password you want</small>
        </div>
        
        <div class="form-group">
            <label for="full_name">Full Name:</label><br>
            <input type="text" id="full_name" name="full_name" value="Administrator" class="form-control" required>
        </div>
        
        <button type="submit" class="btn">Create/Update Admin User</button>
    </form>

    <?php if (!empty($users)): ?>
    <h2>Delete Existing Users</h2>
    <div class="warning">⚠️ Use this carefully! This will permanently delete users.</div>
    <?php foreach ($users as $user): ?>
    <form method="POST" style="display: inline-block; margin: 5px;">
        <input type="hidden" name="action" value="delete_user">
        <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
        <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete user <?= htmlspecialchars($user['username']) ?>?')">
            Delete <?= htmlspecialchars($user['username']) ?>
        </button>
    </form>
    <?php endforeach; ?>
    <?php endif; ?>

    <h2>Quick Actions</h2>
    <p>
        <a href="debug-auth.php" class="btn">🔍 Run Debug Tool</a>
        <a href="mobile-app/" class="btn">📱 Test Mobile App</a>
        <a href="admin/" class="btn">🖥️ Admin Panel</a>
        <a href="setup.php" class="btn">⚙️ Database Setup</a>
    </p>

    <h2>Password Hash Information</h2>
    <div class="info">
        <p><strong>Current PHP password_hash() test:</strong></p>
        <?php
        $testPassword = 'admin123';
        $testHash = password_hash($testPassword, PASSWORD_DEFAULT);
        echo "<pre>Password: $testPassword\nHash: $testHash\nVerification: " . (password_verify($testPassword, $testHash) ? 'SUCCESS' : 'FAILED') . "</pre>";
        ?>
    </div>

    <h2>Troubleshooting</h2>
    <div class="info">
        <p><strong>If you're still getting 401 errors after creating the admin user:</strong></p>
        <ol>
            <li>Make sure the <code>api_tokens</code> table exists (run database setup if needed)</li>
            <li>Check that the user is marked as active (is_active = 1)</li>
            <li>Verify the password hash is working correctly</li>
            <li>Check PHP error logs for detailed error messages</li>
            <li>Make sure the mobile app is sending the correct JSON format</li>
        </ol>
    </div>
</body>
</html>
