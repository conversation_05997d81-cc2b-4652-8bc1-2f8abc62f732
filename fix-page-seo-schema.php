<?php
/**
 * Fix page_seo table schema by adding missing columns
 * This script adds the missing columns that the SEO form expects
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Fix Page SEO Schema - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>Fix Page SEO Schema</h1>";
echo "<p>Adding missing columns to page_seo table...</p>";

$db = new Database();

try {
    // Check current table structure
    echo "<h2>Current page_seo Table Structure</h2>";
    $columns = $db->fetchAll("DESCRIBE page_seo");
    echo "<pre>";
    foreach ($columns as $column) {
        echo $column['Field'] . " - " . $column['Type'] . "\n";
    }
    echo "</pre>";
    
    // List of columns that should exist
    $requiredColumns = [
        'og_title' => 'VARCHAR(255)',
        'og_description' => 'TEXT',
        'og_image' => 'VARCHAR(500)',
        'canonical_url' => 'VARCHAR(500)',
        'robots_index' => 'TINYINT(1) DEFAULT 1',
        'robots_follow' => 'TINYINT(1) DEFAULT 1'
    ];
    
    $existingColumns = array_column($columns, 'Field');
    
    echo "<h2>Adding Missing Columns</h2>";
    
    foreach ($requiredColumns as $columnName => $columnType) {
        if (!in_array($columnName, $existingColumns)) {
            $sql = "ALTER TABLE page_seo ADD COLUMN $columnName $columnType";
            $db->query($sql);
            echo "<div class='success'>✓ Added column: $columnName ($columnType)</div>";
        } else {
            echo "<div class='info'>✓ Column already exists: $columnName</div>";
        }
    }
    
    echo "<h2>Updated page_seo Table Structure</h2>";
    $updatedColumns = $db->fetchAll("DESCRIBE page_seo");
    echo "<pre>";
    foreach ($updatedColumns as $column) {
        echo $column['Field'] . " - " . $column['Type'] . "\n";
    }
    echo "</pre>";
    
    echo "<div class='success'>";
    echo "<h3>✅ Schema Update Complete!</h3>";
    echo "<p>The page_seo table now has all required columns for the SEO management form.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h3>❌ Error Updating Schema</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>Next Steps</h2>";
echo "<div class='info'>";
echo "<p><strong>Test the SEO management:</strong></p>";
echo "<ul>";
echo "<li>Visit <a href='admin/seo.php'>SEO Management</a> to verify it loads without errors</li>";
echo "<li>Try editing a page's SEO settings to test the form submission</li>";
echo "<li>Check that all form fields save correctly</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
