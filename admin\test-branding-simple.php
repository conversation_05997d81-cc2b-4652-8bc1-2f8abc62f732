<?php
/**
 * Simple Branding Test - Minimal test to isolate the issue
 */

require_once '../config/config.php';

// Check if user is logged in
requireLogin();

echo "<!DOCTYPE html>";
echo "<html><head><title>Simple Branding Test</title>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .success { background: #d4edda; color: #155724; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .info { background: #d1ecf1; color: #0c5460; padding: 10px; border-radius: 5px; margin: 10px 0; }
    form { background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
    input, button { margin: 5px; padding: 8px; }
    button { background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
</style></head><body>";

echo "<h1>🧪 Simple Branding Test</h1>";

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h2>Processing Form Submission...</h2>";
    
    // Skip CSRF for this test
    echo "<div class='info'>Skipping CSRF validation for testing...</div>";
    
    $action = $_POST['action'] ?? '';
    echo "<div class='info'>Action: " . htmlspecialchars($action) . "</div>";
    
    if ($action === 'test_colors') {
        $primaryColor = $_POST['primary_color'] ?? '#ff0000';
        echo "<div class='info'>Primary Color: " . htmlspecialchars($primaryColor) . "</div>";
        
        try {
            // Test database connection
            echo "<div class='info'>Testing database connection...</div>";
            $testQuery = $db->fetchOne("SELECT 1 as test");
            if ($testQuery) {
                echo "<div class='success'>✅ Database connection OK</div>";
            } else {
                echo "<div class='error'>❌ Database connection failed</div>";
            }
            
            // Check if table exists
            echo "<div class='info'>Checking if site_settings table exists...</div>";
            $tableExists = $db->fetchOne("SHOW TABLES LIKE 'site_settings'");
            if ($tableExists) {
                echo "<div class='success'>✅ site_settings table exists</div>";
            } else {
                echo "<div class='error'>❌ site_settings table does not exist</div>";
                echo "<div class='info'>Run database/update-schema.php to create the table</div>";
                exit;
            }
            
            // Test simple insert
            echo "<div class='info'>Testing simple insert...</div>";
            $testData = [
                'setting_key' => 'test_simple_' . time(),
                'setting_value' => json_encode(['color' => $primaryColor, 'time' => date('Y-m-d H:i:s')]),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            $insertId = $db->insert('site_settings', $testData);
            echo "<div class='success'>✅ Insert successful (ID: $insertId)</div>";
            
            // Verify insert
            $inserted = $db->fetchOne("SELECT * FROM site_settings WHERE id = ?", [$insertId]);
            if ($inserted) {
                echo "<div class='success'>✅ Data verified in database</div>";
                echo "<div class='info'>Inserted data: " . htmlspecialchars($inserted['setting_value']) . "</div>";
                
                // Clean up
                $db->query("DELETE FROM site_settings WHERE id = ?", [$insertId]);
                echo "<div class='info'>Test data cleaned up</div>";
            } else {
                echo "<div class='error'>❌ Could not verify inserted data</div>";
            }
            
            // Now test the actual branding colors update
            echo "<div class='info'>Testing branding colors update...</div>";
            
            $settings = [
                'primary_color' => $primaryColor,
                'secondary_color' => '#2c3e50',
                'accent_color' => '#f39c12',
                'text_color' => '#333333',
                'background_color' => '#ffffff',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            // Check if branding settings exist
            $existing = $db->fetchOne("SELECT id FROM site_settings WHERE setting_key = 'branding_colors'");
            
            if ($existing) {
                echo "<div class='info'>Updating existing branding_colors record...</div>";
                $result = $db->update('site_settings',
                    ['setting_value' => json_encode($settings), 'updated_at' => date('Y-m-d H:i:s')],
                    'setting_key = ?',
                    ['branding_colors']
                );
                echo "<div class='success'>✅ Update completed</div>";
            } else {
                echo "<div class='info'>Creating new branding_colors record...</div>";
                $insertId = $db->insert('site_settings', [
                    'setting_key' => 'branding_colors',
                    'setting_value' => json_encode($settings),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
                echo "<div class='success'>✅ Insert completed (ID: $insertId)</div>";
            }
            
            // Verify the save
            $verification = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_colors'");
            if ($verification) {
                echo "<div class='success'>✅ Branding colors saved successfully!</div>";
                echo "<div class='info'>Saved data: " . htmlspecialchars($verification['setting_value']) . "</div>";
                
                $savedData = json_decode($verification['setting_value'], true);
                if ($savedData && $savedData['primary_color'] === $primaryColor) {
                    echo "<div class='success'>✅ Primary color matches what we saved!</div>";
                } else {
                    echo "<div class='error'>❌ Primary color doesn't match</div>";
                }
            } else {
                echo "<div class='error'>❌ Could not verify branding colors save</div>";
            }
            
            $message = 'Test completed successfully!';
            $messageType = 'success';
            
        } catch (Exception $e) {
            $message = 'Error: ' . $e->getMessage();
            $messageType = 'error';
            echo "<div class='error'>❌ Exception: " . $e->getMessage() . "</div>";
            echo "<div class='info'>Stack trace: " . $e->getTraceAsString() . "</div>";
        }
    }
}

// Show current branding colors
echo "<h2>Current Branding Colors</h2>";
try {
    $currentColors = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_colors'");
    if ($currentColors) {
        echo "<div class='success'>Found existing branding colors:</div>";
        echo "<div class='info'>" . htmlspecialchars($currentColors['setting_value']) . "</div>";
        
        $colors = json_decode($currentColors['setting_value'], true);
        if ($colors) {
            echo "<div style='background: " . ($colors['primary_color'] ?? '#ccc') . "; color: white; padding: 10px; margin: 10px 0;'>";
            echo "Primary Color: " . ($colors['primary_color'] ?? 'Not set');
            echo "</div>";
        }
    } else {
        echo "<div class='info'>No branding colors found in database</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>Error loading current colors: " . $e->getMessage() . "</div>";
}

if ($message) {
    echo "<div class='alert alert-$messageType'>$message</div>";
}
?>

<h2>Test Form</h2>
<form method="POST">
    <input type="hidden" name="action" value="test_colors">
    
    <label for="primary_color">Primary Color:</label>
    <input type="color" id="primary_color" name="primary_color" value="#e74c3c">
    
    <br><br>
    <button type="submit">Test Save Colors</button>
</form>

<div style="margin-top: 30px; text-align: center;">
    <a href="branding.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">← Back to Branding Settings</a>
</div>

</body></html>
