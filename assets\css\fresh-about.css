/* Fresh About Section Styles */

/* Animated Background */
.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.bg-wave {
    position: absolute;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, rgba(74, 144, 226, 0.1), rgba(80, 227, 194, 0.1));
    border-radius: 50%;
    animation: wave-float 20s ease-in-out infinite;
}

.wave-1 {
    top: -50%;
    left: -50%;
    animation-delay: 0s;
}

.wave-2 {
    bottom: -50%;
    right: -50%;
    animation-delay: -10s;
    background: linear-gradient(45deg, rgba(142, 68, 173, 0.1), rgba(74, 144, 226, 0.1));
}

.bg-particles {
    position: absolute;
    width: 100%;
    height: 100%;
}

.bg-particles .particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: particle-float 15s linear infinite;
}

.bg-particles .particle:nth-child(1) {
    top: 20%;
    left: 20%;
    animation-delay: 0s;
}

.bg-particles .particle:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: -5s;
}

.bg-particles .particle:nth-child(3) {
    top: 80%;
    left: 40%;
    animation-delay: -10s;
}

.bg-particles .particle:nth-child(4) {
    top: 40%;
    left: 60%;
    animation-delay: -15s;
}

.bg-particles .particle:nth-child(5) {
    top: 10%;
    left: 90%;
    animation-delay: -20s;
}

/* Hero Section */
.about-hero {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 12px 24px;
    border-radius: 50px;
    margin-bottom: 32px;
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.badge-icon {
    font-size: 20px;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    color: white;
}

.gradient-text {
    background: linear-gradient(135deg, #4A90E2 0%, #50E3C2 50%, #F5A623 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

.hero-subtitle {
    font-size: 18px;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    max-width: 600px;
    margin: 0 auto;
}

/* Stats Dashboard */
.stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 100px;
    position: relative;
    z-index: 2;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 24px;
    color: white;
}

.stat-number {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: white;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stat-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(74, 144, 226, 0.2), rgba(80, 227, 194, 0.2));
    border-radius: 20px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover .stat-glow {
    opacity: 1;
}

/* Main Grid */
.about-main-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

/* Content Column */
.content-column {
    space-y: 40px;
}

.mission-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 40px;
    margin-bottom: 40px;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
}

.header-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.mission-card h3 {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 600;
    color: white;
    margin: 0;
}

.mission-card p {
    font-size: 16px;
    line-height: 1.7;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Values Grid */
.values-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.value-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
}

.value-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-5px);
}

.value-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    color: white;
    font-size: 20px;
}

.value-item h4 {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
}

.value-item p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
    margin: 0;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary {
    padding: 16px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    color: white;
    border: none;
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-primary:hover,
.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Visual Column */
.visual-column {
    position: relative;
}

.image-gallery {
    position: relative;
    height: 600px;
}

.main-showcase {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.showcase-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.main-showcase:hover .image-overlay {
    opacity: 1;
}

.main-showcase:hover .showcase-image {
    transform: scale(1.05);
}

.play-button {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #4A90E2;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    transform: scale(1.1);
    background: white;
}

/* Achievement Cards */
.achievement-cards {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.achievement-card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    pointer-events: auto;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-1 {
    top: 20px;
    right: 20px;
}

.card-2 {
    bottom: 120px;
    left: 20px;
}

.card-3 {
    top: 50%;
    right: -20px;
    transform: translateY(-50%);
}

.achievement-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.achievement-title {
    font-size: 14px;
    font-weight: 700;
    color: #1a1a1a;
    display: block;
    line-height: 1;
}

.achievement-subtitle {
    font-size: 12px;
    color: #666;
    display: block;
    line-height: 1;
}

/* Progress Indicators */
.progress-indicators {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 16px;
    padding: 20px;
    pointer-events: auto;
}

.progress-item {
    margin-bottom: 16px;
}

.progress-item:last-child {
    margin-bottom: 0;
}

.progress-label {
    font-size: 12px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.progress-bar {
    height: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    border-radius: 3px;
    width: 0;
    transition: width 2s ease;
    animation: progress-fill 2s ease forwards;
}

.progress-fill[data-progress="98"] {
    animation-delay: 0.5s;
}

.progress-fill[data-progress="95"] {
    animation-delay: 1s;
}

.progress-value {
    font-size: 14px;
    font-weight: 700;
    color: #4A90E2;
    text-align: right;
    margin-top: 4px;
}

/* Services Preview */
.services-preview {
    text-align: center;
    position: relative;
    z-index: 2;
}

.preview-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: white;
    margin-bottom: 50px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-preview {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 30px 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.service-preview:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
}

.service-preview .service-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #4A90E2, #50E3C2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 28px;
    color: white;
}

.service-preview h4 {
    font-size: 20px;
    font-weight: 600;
    color: white;
    margin-bottom: 12px;
}

.service-preview p {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin: 0;
}

/* Animations */
@keyframes wave-float {

    0%,
    100% {
        transform: translate(0, 0) rotate(0deg);
    }

    25% {
        transform: translate(5%, -5%) rotate(90deg);
    }

    50% {
        transform: translate(-5%, 5%) rotate(180deg);
    }

    75% {
        transform: translate(-5%, -5%) rotate(270deg);
    }
}

@keyframes particle-float {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

@keyframes gradient-shift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

@keyframes progress-fill {
    from {
        width: 0;
    }

    to {
        width: var(--progress, 100%);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-main-grid {
        gap: 60px;
    }

    .stats-dashboard {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .values-grid {
        gap: 20px;
    }
}

@media (max-width: 768px) {
    .fresh-about {
        padding: 80px 0;
    }

    .about-hero {
        margin-bottom: 60px;
    }

    .hero-title {
        font-size: clamp(2.5rem, 8vw, 3.5rem);
    }

    .stats-dashboard {
        grid-template-columns: 1fr;
        gap: 20px;
        margin-bottom: 60px;
    }

    .about-main-grid {
        grid-template-columns: 1fr;
        gap: 40px;
        margin-bottom: 60px;
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .image-gallery {
        height: 400px;
    }

    .achievement-cards {
        display: none;
    }

    .progress-indicators {
        position: static;
        margin-top: 20px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .preview-title {
        font-size: 2rem;
        margin-bottom: 30px;
    }
}

@media (max-width: 480px) {
    .fresh-about {
        padding: 60px 0;
    }

    .hero-badge {
        padding: 10px 20px;
        font-size: 12px;
    }

    .hero-title {
        font-size: clamp(2rem, 10vw, 2.8rem);
        margin-bottom: 20px;
    }

    .hero-subtitle {
        font-size: 16px;
    }

    .stat-card {
        padding: 20px 15px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .mission-card {
        padding: 30px 20px;
    }

    .value-item {
        padding: 20px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 14px 28px;
        font-size: 14px;
    }

    .image-gallery {
        height: 300px;
    }

    .preview-title {
        font-size: 1.8rem;
    }

    .service-preview {
        padding: 25px 15px;
    }

    .service-preview .service-icon {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
}