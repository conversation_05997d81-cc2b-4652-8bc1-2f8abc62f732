<?php
/**
 * Cross-Browser Compatibility Test Page
 * Tests browser compatibility and feature detection
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Compatibility Test - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .browser-info {
            background: #e3f2fd;
            border: 1px solid #3498db;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .feature-test {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            margin: 0.5rem 0;
            border-radius: 6px;
            background: #f8f9fa;
            border-left: 4px solid #dee2e6;
        }
        
        .feature-test.supported {
            background: #d4edda;
            border-left-color: #27ae60;
        }
        
        .feature-test.not-supported {
            background: #f8d7da;
            border-left-color: #e74c3c;
        }
        
        .feature-test.partial {
            background: #fff3cd;
            border-left-color: #f39c12;
        }
        
        .feature-status {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            color: white;
        }
        
        .status-supported { background: #27ae60; }
        .status-not-supported { background: #e74c3c; }
        .status-partial { background: #f39c12; }
        
        .css-grid-test {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .grid-item {
            background: #3498db;
            color: white;
            padding: 1rem;
            border-radius: 6px;
            text-align: center;
        }
        
        .flexbox-test {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 1rem;
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .flex-item {
            background: #27ae60;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            flex: 1;
            text-align: center;
        }
        
        .animation-test {
            width: 50px;
            height: 50px;
            background: #e74c3c;
            border-radius: 50%;
            margin: 1rem 0;
            animation: bounce 2s infinite;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-20px); }
            60% { transform: translateY(-10px); }
        }
        
        .transform-test {
            width: 100px;
            height: 100px;
            background: #9b59b6;
            margin: 1rem;
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .transform-test:hover {
            transform: rotate(45deg) scale(1.1);
        }
        
        .gradient-test {
            height: 60px;
            background: linear-gradient(45deg, #3498db, #e74c3c, #27ae60);
            border-radius: 6px;
            margin: 1rem 0;
        }
        
        .shadow-test {
            padding: 1rem;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        
        .performance-metrics {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <div class="test-section">
                    <h1>
                        <i class="fas fa-globe"></i>
                        Cross-Browser Compatibility Test
                    </h1>
                    <p>Test browser compatibility and modern web features.</p>
                    
                    <div class="browser-info" id="browserInfo">
                        <h3>Browser Information</h3>
                        <div id="browserDetails">Loading browser information...</div>
                    </div>
                </div>

                <!-- CSS Features Test -->
                <div class="test-section">
                    <h2>CSS Features Support</h2>
                    <div id="cssFeatures">
                        <!-- Will be populated by JavaScript -->
                    </div>
                    
                    <h3>CSS Grid Test</h3>
                    <div class="css-grid-test">
                        <div class="grid-item">Grid Item 1</div>
                        <div class="grid-item">Grid Item 2</div>
                        <div class="grid-item">Grid Item 3</div>
                        <div class="grid-item">Grid Item 4</div>
                    </div>
                    
                    <h3>Flexbox Test</h3>
                    <div class="flexbox-test">
                        <div class="flex-item">Flex 1</div>
                        <div class="flex-item">Flex 2</div>
                        <div class="flex-item">Flex 3</div>
                    </div>
                    
                    <h3>CSS Animations & Transforms</h3>
                    <div class="animation-test" title="CSS Animation Test"></div>
                    <div class="transform-test" title="Hover to test CSS transforms"></div>
                    
                    <h3>CSS Gradients</h3>
                    <div class="gradient-test"></div>
                    
                    <h3>CSS Box Shadow</h3>
                    <div class="shadow-test">
                        This box should have a subtle shadow effect.
                    </div>
                </div>

                <!-- JavaScript Features Test -->
                <div class="test-section">
                    <h2>JavaScript Features Support</h2>
                    <div id="jsFeatures">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <!-- HTML5 Features Test -->
                <div class="test-section">
                    <h2>HTML5 Features Support</h2>
                    <div id="html5Features">
                        <!-- Will be populated by JavaScript -->
                    </div>
                    
                    <h3>HTML5 Input Types Test</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test-date">Date Input</label>
                                <input type="date" id="test-date" class="form-control">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test-color">Color Input</label>
                                <input type="color" id="test-color" class="form-control">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test-range">Range Input</label>
                                <input type="range" id="test-range" class="form-control" min="0" max="100" value="50">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="test-number">Number Input</label>
                                <input type="number" id="test-number" class="form-control" min="0" max="100">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Performance Test -->
                <div class="test-section">
                    <h2>Performance Metrics</h2>
                    <button type="button" class="btn btn-primary" onclick="runPerformanceTest()">
                        <i class="fas fa-tachometer-alt"></i>
                        Run Performance Test
                    </button>
                    
                    <div class="performance-metrics" id="performanceResults">
                        Click the button above to run performance tests...
                    </div>
                </div>

                <!-- Network Features Test -->
                <div class="test-section">
                    <h2>Network & Storage Features</h2>
                    <div id="networkFeatures">
                        <!-- Will be populated by JavaScript -->
                    </div>
                    
                    <h3>Local Storage Test</h3>
                    <div class="d-flex gap-10 mb-20">
                        <button type="button" class="btn" onclick="testLocalStorage()">
                            Test Local Storage
                        </button>
                        <button type="button" class="btn btn-outline" onclick="clearLocalStorage()">
                            Clear Test Data
                        </button>
                    </div>
                    
                    <div id="storageResults" class="performance-metrics">
                        Local storage test results will appear here...
                    </div>
                </div>

                <!-- Compatibility Summary -->
                <div class="test-section">
                    <h2>Compatibility Summary</h2>
                    <div id="compatibilitySummary">
                        <div class="browser-info">
                            <h3>Overall Compatibility Score: <span id="compatibilityScore">Calculating...</span></h3>
                            <p id="compatibilityRecommendations">Analyzing browser compatibility...</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
    <script>
        let compatibilityTests = {
            passed: 0,
            total: 0
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            detectBrowserInfo();
            testCSSFeatures();
            testJavaScriptFeatures();
            testHTML5Features();
            testNetworkFeatures();
            calculateCompatibilityScore();
        });
        
        function detectBrowserInfo() {
            const browserInfo = document.getElementById('browserDetails');
            const userAgent = navigator.userAgent;
            
            // Detect browser
            let browserName = 'Unknown';
            let browserVersion = 'Unknown';
            
            if (userAgent.indexOf('Chrome') > -1) {
                browserName = 'Chrome';
                browserVersion = userAgent.match(/Chrome\/([0-9.]+)/)[1];
            } else if (userAgent.indexOf('Firefox') > -1) {
                browserName = 'Firefox';
                browserVersion = userAgent.match(/Firefox\/([0-9.]+)/)[1];
            } else if (userAgent.indexOf('Safari') > -1) {
                browserName = 'Safari';
                browserVersion = userAgent.match(/Version\/([0-9.]+)/)[1];
            } else if (userAgent.indexOf('Edge') > -1) {
                browserName = 'Edge';
                browserVersion = userAgent.match(/Edge\/([0-9.]+)/)[1];
            }
            
            // Detect OS
            let os = 'Unknown';
            if (userAgent.indexOf('Windows') > -1) os = 'Windows';
            else if (userAgent.indexOf('Mac') > -1) os = 'macOS';
            else if (userAgent.indexOf('Linux') > -1) os = 'Linux';
            else if (userAgent.indexOf('Android') > -1) os = 'Android';
            else if (userAgent.indexOf('iOS') > -1) os = 'iOS';
            
            browserInfo.innerHTML = `
                <strong>Browser:</strong> ${browserName} ${browserVersion}<br>
                <strong>Operating System:</strong> ${os}<br>
                <strong>Screen Resolution:</strong> ${screen.width}x${screen.height}<br>
                <strong>Viewport:</strong> ${window.innerWidth}x${window.innerHeight}<br>
                <strong>Color Depth:</strong> ${screen.colorDepth}-bit<br>
                <strong>Language:</strong> ${navigator.language}<br>
                <strong>Cookies Enabled:</strong> ${navigator.cookieEnabled ? 'Yes' : 'No'}<br>
                <strong>Online:</strong> ${navigator.onLine ? 'Yes' : 'No'}
            `;
        }
        
        function testCSSFeatures() {
            const features = [
                { name: 'CSS Grid', test: () => CSS.supports('display', 'grid') },
                { name: 'CSS Flexbox', test: () => CSS.supports('display', 'flex') },
                { name: 'CSS Custom Properties', test: () => CSS.supports('--custom', 'value') },
                { name: 'CSS Transforms', test: () => CSS.supports('transform', 'rotate(45deg)') },
                { name: 'CSS Animations', test: () => CSS.supports('animation', 'test 1s') },
                { name: 'CSS Gradients', test: () => CSS.supports('background', 'linear-gradient(red, blue)') },
                { name: 'CSS Box Shadow', test: () => CSS.supports('box-shadow', '0 0 10px black') },
                { name: 'CSS Border Radius', test: () => CSS.supports('border-radius', '10px') },
                { name: 'CSS Calc()', test: () => CSS.supports('width', 'calc(100% - 10px)') },
                { name: 'CSS Object Fit', test: () => CSS.supports('object-fit', 'cover') }
            ];
            
            displayFeatureTests('cssFeatures', features);
        }
        
        function testJavaScriptFeatures() {
            const features = [
                { name: 'ES6 Arrow Functions', test: () => {
                    try { eval('() => {}'); return true; } catch(e) { return false; }
                }},
                { name: 'ES6 Classes', test: () => {
                    try { eval('class Test {}'); return true; } catch(e) { return false; }
                }},
                { name: 'ES6 Template Literals', test: () => {
                    try { eval('`template`'); return true; } catch(e) { return false; }
                }},
                { name: 'ES6 Destructuring', test: () => {
                    try { eval('const {a} = {}'); return true; } catch(e) { return false; }
                }},
                { name: 'ES6 Promises', test: () => typeof Promise !== 'undefined' },
                { name: 'ES6 Map/Set', test: () => typeof Map !== 'undefined' && typeof Set !== 'undefined' },
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
                { name: 'Local Storage', test: () => typeof localStorage !== 'undefined' },
                { name: 'Session Storage', test: () => typeof sessionStorage !== 'undefined' },
                { name: 'Geolocation API', test: () => 'geolocation' in navigator },
                { name: 'File API', test: () => typeof FileReader !== 'undefined' },
                { name: 'Web Workers', test: () => typeof Worker !== 'undefined' },
                { name: 'Service Workers', test: () => 'serviceWorker' in navigator },
                { name: 'WebSocket', test: () => typeof WebSocket !== 'undefined' },
                { name: 'IndexedDB', test: () => 'indexedDB' in window }
            ];
            
            displayFeatureTests('jsFeatures', features);
        }
        
        function testHTML5Features() {
            const features = [
                { name: 'HTML5 Semantic Elements', test: () => 'HTMLElement' in window },
                { name: 'Canvas API', test: () => 'HTMLCanvasElement' in window },
                { name: 'SVG Support', test: () => 'SVGElement' in window },
                { name: 'Audio API', test: () => 'HTMLAudioElement' in window },
                { name: 'Video API', test: () => 'HTMLVideoElement' in window },
                { name: 'Form Validation', test: () => 'checkValidity' in document.createElement('input') },
                { name: 'Input Types (date)', test: () => {
                    const input = document.createElement('input');
                    input.type = 'date';
                    return input.type === 'date';
                }},
                { name: 'Input Types (color)', test: () => {
                    const input = document.createElement('input');
                    input.type = 'color';
                    return input.type === 'color';
                }},
                { name: 'Input Types (range)', test: () => {
                    const input = document.createElement('input');
                    input.type = 'range';
                    return input.type === 'range';
                }},
                { name: 'Drag and Drop', test: () => 'draggable' in document.createElement('div') }
            ];
            
            displayFeatureTests('html5Features', features);
        }
        
        function testNetworkFeatures() {
            const features = [
                { name: 'XMLHttpRequest', test: () => typeof XMLHttpRequest !== 'undefined' },
                { name: 'Fetch API', test: () => typeof fetch !== 'undefined' },
                { name: 'WebSocket', test: () => typeof WebSocket !== 'undefined' },
                { name: 'Server-Sent Events', test: () => typeof EventSource !== 'undefined' },
                { name: 'Network Information API', test: () => 'connection' in navigator },
                { name: 'Online/Offline Events', test: () => 'onLine' in navigator },
                { name: 'CORS Support', test: () => 'withCredentials' in new XMLHttpRequest() }
            ];
            
            displayFeatureTests('networkFeatures', features);
        }
        
        function displayFeatureTests(containerId, features) {
            const container = document.getElementById(containerId);
            let html = '';
            
            features.forEach(feature => {
                const supported = feature.test();
                compatibilityTests.total++;
                if (supported) compatibilityTests.passed++;
                
                const statusClass = supported ? 'supported' : 'not-supported';
                const statusIcon = supported ? '✓' : '✗';
                const statusIconClass = supported ? 'status-supported' : 'status-not-supported';
                
                html += `
                    <div class="feature-test ${statusClass}">
                        <div class="feature-status ${statusIconClass}">${statusIcon}</div>
                        <div>
                            <strong>${feature.name}</strong>
                            <span style="margin-left: 1rem; color: #6c757d;">
                                ${supported ? 'Supported' : 'Not Supported'}
                            </span>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        function runPerformanceTest() {
            const results = document.getElementById('performanceResults');
            results.innerHTML = 'Running performance tests...\n';
            
            // Test 1: DOM manipulation performance
            const startTime = performance.now();
            const testDiv = document.createElement('div');
            for (let i = 0; i < 1000; i++) {
                const span = document.createElement('span');
                span.textContent = `Item ${i}`;
                testDiv.appendChild(span);
            }
            const domTime = performance.now() - startTime;
            
            // Test 2: Array operations performance
            const arrayStart = performance.now();
            const testArray = [];
            for (let i = 0; i < 10000; i++) {
                testArray.push(Math.random());
            }
            testArray.sort();
            const arrayTime = performance.now() - arrayStart;
            
            // Test 3: CSS animation performance
            const animationStart = performance.now();
            const testElement = document.createElement('div');
            testElement.style.cssText = 'width: 100px; height: 100px; background: red; transition: transform 1s;';
            document.body.appendChild(testElement);
            testElement.style.transform = 'translateX(100px)';
            
            setTimeout(() => {
                const animationTime = performance.now() - animationStart;
                document.body.removeChild(testElement);
                
                results.innerHTML = `
Performance Test Results:
========================
DOM Manipulation (1000 elements): ${domTime.toFixed(2)}ms
Array Operations (10000 items): ${arrayTime.toFixed(2)}ms
CSS Animation Test: ${animationTime.toFixed(2)}ms

Browser Performance Rating: ${getPerformanceRating(domTime, arrayTime)}

Memory Usage: ${navigator.deviceMemory || 'Unknown'} GB
Hardware Concurrency: ${navigator.hardwareConcurrency || 'Unknown'} cores
                `;
            }, 1100);
        }
        
        function getPerformanceRating(domTime, arrayTime) {
            const totalTime = domTime + arrayTime;
            if (totalTime < 50) return 'Excellent';
            if (totalTime < 100) return 'Good';
            if (totalTime < 200) return 'Average';
            return 'Poor';
        }
        
        function testLocalStorage() {
            const results = document.getElementById('storageResults');
            
            try {
                const testKey = 'admin_test_' + Date.now();
                const testData = {
                    timestamp: new Date().toISOString(),
                    browser: navigator.userAgent,
                    test: 'Local storage functionality test'
                };
                
                localStorage.setItem(testKey, JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                
                results.innerHTML = `
Local Storage Test Results:
==========================
Status: SUCCESS
Test Key: ${testKey}
Data Stored: ${JSON.stringify(testData, null, 2)}
Data Retrieved: ${JSON.stringify(retrieved, null, 2)}
Storage Quota: ${navigator.storage ? 'Available' : 'Not Available'}
                `;
                
                // Clean up
                localStorage.removeItem(testKey);
                
            } catch (error) {
                results.innerHTML = `
Local Storage Test Results:
==========================
Status: FAILED
Error: ${error.message}
                `;
            }
        }
        
        function clearLocalStorage() {
            const results = document.getElementById('storageResults');
            
            // Clear only test data
            const keys = Object.keys(localStorage);
            const testKeys = keys.filter(key => key.startsWith('admin_test_'));
            
            testKeys.forEach(key => localStorage.removeItem(key));
            
            results.innerHTML = `
Local Storage Cleanup:
=====================
Cleared ${testKeys.length} test entries
Remaining entries: ${Object.keys(localStorage).length}
            `;
        }
        
        function calculateCompatibilityScore() {
            setTimeout(() => {
                const score = Math.round((compatibilityTests.passed / compatibilityTests.total) * 100);
                const scoreElement = document.getElementById('compatibilityScore');
                const recommendationsElement = document.getElementById('compatibilityRecommendations');
                
                scoreElement.textContent = `${score}% (${compatibilityTests.passed}/${compatibilityTests.total} features supported)`;
                
                let recommendations = '';
                if (score >= 90) {
                    recommendations = 'Excellent! Your browser supports most modern web features. The admin dashboard should work perfectly.';
                } else if (score >= 75) {
                    recommendations = 'Good compatibility. Most features are supported. You may experience minor issues with some advanced features.';
                } else if (score >= 60) {
                    recommendations = 'Moderate compatibility. Some features may not work as expected. Consider updating your browser.';
                } else {
                    recommendations = 'Poor compatibility. Many features are not supported. Please update to a modern browser for the best experience.';
                }
                
                recommendationsElement.textContent = recommendations;
                
                // Show alert with results
                if (window.AdminJS && window.AdminJS.showAlert) {
                    const alertType = score >= 75 ? 'success' : score >= 60 ? 'warning' : 'error';
                    window.AdminJS.showAlert(alertType, `Browser compatibility: ${score}%`);
                }
            }, 1000);
        }
    </script>
</body>
</html>
