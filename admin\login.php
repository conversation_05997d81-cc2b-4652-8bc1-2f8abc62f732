<?php
/**
 * Admin Login Page for Flori Construction Ltd
 */

require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';

$success = '';

// Check for logout message
if (isset($_GET['logged_out']) && $_GET['logged_out'] == '1') {
    $success = 'You have been successfully logged out';
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']) ? true : false;

    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password';
    } else {
        // Get user from database
        $user = $db->fetchOne(
            "SELECT id, username, email, password_hash, full_name, role, is_active
             FROM users
             WHERE (username = ? OR email = ?) AND is_active = 1",
            [$username, $username]
        );

        if ($user && password_verify($password, $user['password_hash'])) {
            // Login successful
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['full_name'] = $user['full_name'];

            // Handle remember me functionality
            if ($remember) {
                $token = bin2hex(random_bytes(32));
                $expires = time() + (30 * 24 * 60 * 60); // 30 days

                // Store token in database (you might want to create a remember_tokens table)
                setcookie('remember_token', $token, $expires, '/', '', true, true);
            }

            // Update last login
            $db->update('users',
                ['last_login' => date('Y-m-d H:i:s')],
                'id = ?',
                [$user['id']]
            );

            // Redirect to dashboard
            header('Location: index.php');
            exit;
        } else {
            $error = 'Invalid username or password. Please try again.';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - <?= SITE_NAME ?></title>
    <meta name="description" content="Secure admin login for <?= SITE_NAME ?> content management system">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin-login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Preload critical resources -->
    <link rel="preload" href="<?= ASSETS_URL ?>/images/logo.png" as="image">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-container">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo" onerror="this.style.display='none'">
                    <div class="logo-fallback" style="display: none;">
                        <i class="fas fa-building"></i>
                    </div>
                </div>
                <h1>
                    <span class="title-main">Admin Portal</span>
                    <span class="title-sub"><?= SITE_NAME ?></span>
                </h1>
                <p>Secure access to your construction management system</p>
            </div>

            <?php if ($success): ?>
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <?= htmlspecialchars($success) ?>
            </div>
            <?php endif; ?>

            <?php if ($error): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-triangle"></i>
                <?= htmlspecialchars($error) ?>
            </div>
            <?php endif; ?>

            <form method="POST" class="login-form" novalidate>
                <div class="form-group">
                    <label for="username">
                        <i class="fas fa-user"></i>
                        Username or Email
                    </label>
                    <div class="input-wrapper">
                        <input
                            type="text"
                            id="username"
                            name="username"
                            value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                            required
                            autofocus
                            autocomplete="username"
                            placeholder="Enter your username or email"
                            class="form-input"
                        >
                        <div class="input-icon">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <div class="input-wrapper">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            required
                            autocomplete="current-password"
                            placeholder="Enter your password"
                            class="form-input password-input"
                        >
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <button type="button" class="password-toggle" data-target="password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" name="remember" id="remember">
                        <span class="checkmark"></span>
                        <span class="remember-text">Remember me for 30 days</span>
                    </label>
                </div>

                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    <span class="btn-text">Sign In Securely</span>
                </button>

                <div class="security-notice">
                    <i class="fas fa-shield-alt"></i>
                    <span>Your connection is secured with SSL encryption</span>
                </div>
            </form>

            <div class="login-footer">
                <div class="footer-links">
                    <a href="../index.php" class="back-link">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to Website</span>
                    </a>
                </div>

                <div class="login-info">
                    <div class="info-header">
                        <i class="fas fa-info-circle"></i>
                        <h3>Development Access</h3>
                    </div>
                    <div class="credentials">
                        <div class="credential-item">
                            <span class="label">Username:</span>
                            <code>admin</code>
                        </div>
                        <div class="credential-item">
                            <span class="label">Password:</span>
                            <code>admin123</code>
                        </div>
                    </div>
                    <div class="security-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Change default credentials in production environment</span>
                    </div>
                </div>

                <div class="login-stats">
                    <div class="stat-item">
                        <i class="fas fa-shield-check"></i>
                        <span>Secure Login</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-clock"></i>
                        <span>24/7 Access</span>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span>Mobile Ready</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Background Animation -->
    <div class="background-animation">
        <div class="construction-icons">
            <i class="fas fa-hard-hat"></i>
            <i class="fas fa-hammer"></i>
            <i class="fas fa-tools"></i>
            <i class="fas fa-building"></i>
            <i class="fas fa-truck"></i>
            <i class="fas fa-drafting-compass"></i>
            <i class="fas fa-ruler-combined"></i>
            <i class="fas fa-wrench"></i>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Focus on username field if no value
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }

            // Password toggle functionality
            const passwordToggle = document.querySelector('.password-toggle');
            const passwordInput = document.getElementById('password');

            if (passwordToggle && passwordInput) {
                passwordToggle.addEventListener('click', function() {
                    const icon = this.querySelector('i');

                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                        this.setAttribute('title', 'Hide password');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                        this.setAttribute('title', 'Show password');
                    }
                });
            }

            // Enhanced form submission with loading state
            const form = document.querySelector('.login-form');
            const submitBtn = document.querySelector('.login-btn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnIcon = submitBtn.querySelector('i');

            form.addEventListener('submit', function(e) {
                // Show loading state without preventing submission
                btnIcon.className = 'fas fa-spinner fa-spin';
                btnText.textContent = 'Signing In...';
                submitBtn.disabled = true;
                submitBtn.classList.add('loading');

                // Don't disable form inputs to allow server processing
                // The server will handle validation
            });

            // Input validation and styling
            const inputs = document.querySelectorAll('.form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                    this.classList.remove('error');
                });

                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                    if (this.hasAttribute('required') && !this.value.trim()) {
                        this.classList.add('error');
                    }
                });

                input.addEventListener('input', function() {
                    if (this.classList.contains('error') && this.value.trim()) {
                        this.classList.remove('error');
                    }
                });
            });

            // Animate construction icons with staggered delays
            const icons = document.querySelectorAll('.construction-icons i');
            icons.forEach((icon, index) => {
                icon.style.animationDelay = `${index * 0.8}s`;
                icon.style.animationDuration = `${4 + (index * 0.5)}s`;
            });

            // Logo fallback handling
            const logo = document.querySelector('.logo');
            const logoFallback = document.querySelector('.logo-fallback');

            if (logo && logoFallback) {
                logo.addEventListener('error', function() {
                    this.style.display = 'none';
                    logoFallback.style.display = 'flex';
                });
            }

            // Auto-hide success/error messages
            const messages = document.querySelectorAll('.success-message, .error-message');
            messages.forEach(message => {
                setTimeout(() => {
                    message.style.opacity = '0';
                    setTimeout(() => {
                        message.style.display = 'none';
                    }, 300);
                }, 5000);
            });

            // Remember me tooltip
            const rememberCheckbox = document.getElementById('remember');
            if (rememberCheckbox) {
                rememberCheckbox.setAttribute('title', 'Keep me signed in for 30 days');
            }
        });

        // Error display function
        function showError(message) {
            // Remove existing error messages
            const existingError = document.querySelector('.error-message');
            if (existingError) {
                existingError.remove();
            }

            // Create new error message
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i>${message}`;

            // Insert before form
            const form = document.querySelector('.login-form');
            form.parentNode.insertBefore(errorDiv, form);

            // Auto-hide after 5 seconds
            setTimeout(() => {
                errorDiv.style.opacity = '0';
                setTimeout(() => {
                    errorDiv.remove();
                }, 300);
            }, 5000);
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Alt + L to focus username field
            if (e.altKey && e.key === 'l') {
                e.preventDefault();
                document.getElementById('username').focus();
            }

            // Escape to clear form
            if (e.key === 'Escape') {
                const form = document.querySelector('.login-form');
                if (form && confirm('Clear the login form?')) {
                    form.reset();
                    document.getElementById('username').focus();
                }
            }
        });

        // Security notice animation
        setTimeout(() => {
            const securityNotice = document.querySelector('.security-notice');
            if (securityNotice) {
                securityNotice.style.opacity = '1';
                securityNotice.style.transform = 'translateY(0)';
            }
        }, 1000);
    </script>
</body>
</html>
