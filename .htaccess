# Flori Construction Ltd - Apache Configuration
# Enable URL rewriting and security headers

# Enable mod_rewrite
RewriteEngine On

# Security Headers
<IfModule mod_headers.c>
    # Prevent clickjacking
    Header always append X-Frame-Options SAMEORIGIN

    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options nosniff

    # Enable XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # Content Security Policy (adjust as needed)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; media-src 'self'; connect-src 'self';"
</IfModule>

# Cache Control for Static Assets
<IfModule mod_expires.c>
    ExpiresActive On

    # Images
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS and JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"

    # Fonts
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"

    # Videos
    ExpiresByType video/mp4 "access plus 1 month"
    ExpiresByType video/webm "access plus 1 month"

    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# Gzip Compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Clean URLs - Remove .php extension
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Project pages
RewriteRule ^project/([a-zA-Z0-9-]+)/?$ project.php?slug=$1 [NC,L,QSA]

# Service pages
RewriteRule ^service/([a-zA-Z0-9-]+)/?$ service.php?slug=$1 [NC,L,QSA]

# Projects listing with pagination
RewriteRule ^projects/?$ projects.php [NC,L,QSA]
RewriteRule ^projects/page/([0-9]+)/?$ projects.php?page=$1 [NC,L,QSA]

# Services listing
RewriteRule ^services/?$ services.php [NC,L,QSA]

# About page
RewriteRule ^about/?$ about.php [NC,L,QSA]

# Contact page
RewriteRule ^contact/?$ contact.php [NC,L,QSA]

# Media gallery
RewriteRule ^gallery/?$ media.php [NC,L,QSA]

# Admin panel (preserve existing URLs)
RewriteRule ^admin/?$ admin/index.php [NC,L,QSA]

# API endpoints (preserve existing URLs)
RewriteRule ^api/(.*)$ api/$1 [NC,L,QSA]

# Mobile app (preserve existing URLs)
RewriteRule ^mobile-app/?$ mobile-app/index.html [NC,L,QSA]

# Sitemap
RewriteRule ^sitemap\.xml$ sitemap.php [NC,L]

# Robots.txt
RewriteRule ^robots\.txt$ robots.txt [NC,L]

# Prevent access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent access to config directory
RewriteRule ^config/ - [F,L]

# Prevent access to database directory
RewriteRule ^database/ - [F,L]

# Prevent access to debug files in production (DISABLED FOR DEBUGGING)
# <FilesMatch "^(debug|test|fix)-.*\.php$">
#     Order Allow,Deny
#     Deny from all
#     # Allow from localhost for development
#     Allow from 127.0.0.1
#     Allow from ::1
# </FilesMatch>

# Prevent directory browsing
Options -Indexes

# Custom Error Pages
ErrorDocument 404 /erdevwe/404.php
ErrorDocument 403 /erdevwe/404.php
ErrorDocument 500 /erdevwe/404.php

# File Upload Security
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    <IfModule mod_rewrite.c>
        RewriteEngine On
        RewriteCond %{REQUEST_URI} ^/erdevwe/uploads/
        RewriteRule .* - [F,L]
    </IfModule>
</FilesMatch>

# Prevent hotlinking of images
RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?localhost [NC]
RewriteCond %{HTTP_REFERER} !^http(s)?://(www\.)?floriconstructionltd\.com [NC]
RewriteRule \.(jpg|jpeg|png|gif|webp)$ - [NC,F,L]

# Force HTTPS (uncomment for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# WWW redirect (uncomment and adjust for production)
# RewriteCond %{HTTP_HOST} ^floriconstructionltd\.com [NC]
# RewriteRule ^(.*)$ https://www.floriconstructionltd.com/$1 [L,R=301]
