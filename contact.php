<?php
require_once 'config/config.php';
require_once 'config/email.php';

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = sanitize($_POST['name'] ?? '');
    $email = sanitize($_POST['email'] ?? '');
    $phone = sanitize($_POST['phone'] ?? '');
    $subject = sanitize($_POST['subject'] ?? '');
    $messageText = sanitize($_POST['message'] ?? '');
    $service = sanitize($_POST['service'] ?? '');
    $project = sanitize($_POST['project'] ?? '');

    // Validation
    $errors = [];

    if (empty($name)) {
        $errors[] = 'Name is required';
    }

    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Valid email is required';
    }

    if (empty($messageText)) {
        $errors[] = 'Message is required';
    }

    if (empty($errors)) {
        try {
            // Save to database
            $inquiryData = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'subject' => $subject,
                'message' => $messageText,
                'service_interest' => $service,
                'project_reference' => $project,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $db->insert('contact_inquiries', $inquiryData);

            // Send email notifications
            $emailSent = false;
            $autoReplySent = false;

            try {
                // Send notification to admin
                $emailSent = sendContactNotification($inquiryData);

                // Send auto-reply to customer
                $autoReplySent = sendContactAutoReply($inquiryData);

            } catch (Exception $emailError) {
                // Log email error but don't fail the form submission
                error_log('Email sending failed: ' . $emailError->getMessage());
            }

            if ($emailSent && $autoReplySent) {
                $message = 'Thank you for your inquiry! We have sent you a confirmation email and will get back to you within 24 hours.';
            } elseif ($emailSent) {
                $message = 'Thank you for your inquiry! We will get back to you within 24 hours.';
            } else {
                $message = 'Thank you for your inquiry! We have received your message and will get back to you within 24 hours.';
            }

            $messageType = 'success';

            // Clear form data
            $name = $email = $phone = $subject = $messageText = $service = $project = '';

        } catch (Exception $e) {
            $message = 'Sorry, there was an error sending your message. Please try again or call us directly.';
            $messageType = 'error';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'error';
    }
}

// Get pre-filled values from URL parameters
$prefilledService = isset($_GET['service']) ? sanitize($_GET['service']) : ($service ?? '');
$prefilledProject = isset($_GET['project']) ? sanitize($_GET['project']) : ($project ?? '');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - <?= SITE_NAME ?></title>
    <meta name="description" content="Get in touch with Flori Construction Ltd for your construction needs. Contact us for quotes, consultations, and project inquiries.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?= SITE_PHONE ?></span>
                    <span><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></span>
                </div>
                <div class="social-links">
                    <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                    <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>

        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="about.php">About Us</a></li>
                    <li><a href="services.php">Our Services</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed">Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="contact.php" class="active">Contact Us</a></li>
                </ul>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>Contact Us</h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <span>Contact Us</span>
            </nav>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">Ready to start your construction project? Contact us for a free consultation and quote.</p>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="contact-form-wrapper">
                        <?php if ($message): ?>
                        <div class="alert alert-<?= $messageType ?>">
                            <?= $message ?>
                        </div>
                        <?php endif; ?>

                        <form method="POST" class="contact-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Full Name *</label>
                                    <input type="text" id="name" name="name" value="<?= htmlspecialchars($name ?? '') ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address *</label>
                                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($email ?? '') ?>" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" value="<?= htmlspecialchars($phone ?? '') ?>">
                                </div>
                                <div class="form-group">
                                    <label for="subject">Subject</label>
                                    <input type="text" id="subject" name="subject" value="<?= htmlspecialchars($subject ?? '') ?>">
                                </div>
                            </div>

                            <?php if ($prefilledService): ?>
                            <div class="form-group">
                                <label for="service">Service Interest</label>
                                <input type="text" id="service" name="service" value="<?= htmlspecialchars($prefilledService) ?>" readonly>
                            </div>
                            <?php endif; ?>

                            <?php if ($prefilledProject): ?>
                            <div class="form-group">
                                <label for="project">Project Reference</label>
                                <input type="text" id="project" name="project" value="<?= htmlspecialchars($prefilledProject) ?>" readonly>
                            </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" rows="6" required placeholder="Tell us about your project requirements..."><?= htmlspecialchars($messageText ?? '') ?></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="contact-info-sidebar">
                        <!-- Contact Information -->
                        <div class="contact-widget">
                            <h3>Contact Information</h3>
                            <div class="contact-details">
                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="contact-content">
                                        <h4>Address</h4>
                                        <p><?= SITE_ADDRESS ?></p>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    <div class="contact-content">
                                        <h4>Phone</h4>
                                        <p><a href="tel:<?= SITE_PHONE ?>"><?= SITE_PHONE ?></a></p>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div class="contact-content">
                                        <h4>Mobile</h4>
                                        <p><a href="tel:<?= SITE_MOBILE ?>"><?= SITE_MOBILE ?></a></p>
                                    </div>
                                </div>

                                <div class="contact-item">
                                    <div class="contact-icon">
                                        <i class="fas fa-envelope"></i>
                                    </div>
                                    <div class="contact-content">
                                        <h4>Email</h4>
                                        <p><a href="mailto:<?= SITE_EMAIL ?>"><?= SITE_EMAIL ?></a></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Business Hours -->
                        <div class="contact-widget">
                            <h3>Business Hours</h3>
                            <div class="business-hours">
                                <div class="hours-item">
                                    <span>Monday - Friday</span>
                                    <span>8:00 AM - 6:00 PM</span>
                                </div>
                                <div class="hours-item">
                                    <span>Saturday</span>
                                    <span>9:00 AM - 4:00 PM</span>
                                </div>
                                <div class="hours-item">
                                    <span>Sunday</span>
                                    <span>Emergency Only</span>
                                </div>
                            </div>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="contact-widget emergency-contact">
                            <h3>Emergency Contact</h3>
                            <p>For urgent construction emergencies, call us 24/7:</p>
                            <a href="tel:<?= SITE_MOBILE ?>" class="emergency-number">
                                <i class="fas fa-phone"></i>
                                <?= SITE_MOBILE ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
        <div class="container">
            <h3>Find Us</h3>
            <div class="map-container">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2476.8234567890123!2d-0.1234567890123456!3d51.61234567890123!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTHCsDM2JzQ0LjQiTiAwwrAwNyc0NC40Ilc!5e0!3m2!1sen!2suk!4v1234567890123!5m2!1sen!2suk"
                    width="100%"
                    height="400"
                    style="border:0;"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <img src="<?= ASSETS_URL ?>/images/logo-white.png" alt="<?= SITE_NAME ?>" class="footer-logo">
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="social-links">
                        <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="projects.php">Our Projects</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="service.php?slug=civil-engineering">Civil Engineering</a></li>
                        <li><a href="service.php?slug=groundworks">Groundworks</a></li>
                        <li><a href="service.php?slug=rc-frames">RC Frames</a></li>
                        <li><a href="service.php?slug=basements">Basements</a></li>
                        <li><a href="service.php?slug=hard-landscaping">Hard Landscaping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> <?= SITE_ADDRESS ?></p>
                        <p><i class="fas fa-phone"></i> <?= SITE_PHONE ?></p>
                        <p><i class="fas fa-mobile-alt"></i> <?= SITE_MOBILE ?></p>
                        <p><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
</body>
</html>
