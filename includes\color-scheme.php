<?php
/**
 * Color Scheme Generator
 * Generates dynamic CSS based on branding settings
 */

function generateColorSchemeCSS() {
    global $db;

    // Get color settings from database
    $colorSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_colors'");

    // Default colors
    $defaultColors = [
        'primary_color' => '#e74c3c',
        'secondary_color' => '#2c3e50',
        'accent_color' => '#f39c12',
        'text_color' => '#333333',
        'background_color' => '#ffffff',
        'header_bg_color' => '#ffffff',
        'header_text_color' => '#1a1a1a',
        'button_color' => '#1a1a1a',
        'button_hover_color' => '#d4a574',
        'footer_bg_color' => '#0a0a0a',
        'footer_text_color' => '#ffffff',
        'link_color' => '#d4a574',
        'link_hover_color' => '#f4d03f'
    ];

    // Parse colors from database with fallback to defaults
    $colors = $defaultColors;
    if ($colorSettings && isset($colorSettings['setting_value'])) {
        $savedColors = json_decode($colorSettings['setting_value'], true);
        if ($savedColors && is_array($savedColors)) {
            $colors = array_merge($defaultColors, $savedColors);
        }
    }

    // Generate CSS
    $css = "
    <style id=\"dynamic-color-scheme\">
        :root {
            --primary-color: {$colors['primary_color']};
            --secondary-color: {$colors['secondary_color']};
            --accent-color: {$colors['accent_color']};
            --text-color: {$colors['text_color']};
            --background-color: {$colors['background_color']};
            --header-bg-color: {$colors['header_bg_color']};
            --header-text-color: {$colors['header_text_color']};
            --button-color: {$colors['button_color']};
            --button-hover-color: {$colors['button_hover_color']};
            --footer-bg-color: {$colors['footer_bg_color']};
            --footer-text-color: {$colors['footer_text_color']};
            --link-color: {$colors['link_color']};
            --link-hover-color: {$colors['link_hover_color']};
        }

        /* Apply color scheme to elements */
        body {
            background-color: var(--background-color);
            color: var(--text-color);
        }

        /* Header Styling */
        header,
        .header {
            background-color: var(--header-bg-color) !important;
            color: var(--header-text-color) !important;
        }

        .header .nav-link,
        .header .navbar-nav .nav-link,
        .header .navbar-brand,
        header .nav-link,
        header .navbar-nav .nav-link,
        header .navbar-brand {
            color: var(--header-text-color) !important;
        }

        .header .nav-link:hover,
        .header .navbar-nav .nav-link:hover,
        header .nav-link:hover,
        header .navbar-nav .nav-link:hover {
            color: var(--link-hover-color) !important;
        }

        /* Button Styling */
        .btn,
        .btn-primary,
        .cta-button,
        .contact-btn,
        button[type=\"submit\"],
        input[type=\"submit\"] {
            background-color: var(--button-color) !important;
            border-color: var(--button-color) !important;
            color: white !important;
        }

        .btn:hover,
        .btn-primary:hover,
        .cta-button:hover,
        .contact-btn:hover,
        button[type=\"submit\"]:hover,
        input[type=\"submit\"]:hover {
            background-color: var(--button-hover-color) !important;
            border-color: var(--button-hover-color) !important;
        }

        /* Link Styling */
        a {
            color: var(--link-color);
        }

        a:hover {
            color: var(--link-hover-color);
        }

        /* Footer Styling */
        footer,
        .footer {
            background-color: var(--footer-bg-color) !important;
            color: var(--footer-text-color) !important;
        }

        footer a,
        .footer a {
            color: var(--footer-text-color);
        }

        footer a:hover,
        .footer a:hover {
            color: var(--link-hover-color);
        }

        /* Primary Color Elements */
        .primary-bg,
        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .text-primary {
            color: var(--primary-color) !important;
        }

        /* Secondary Color Elements */
        .secondary-bg,
        .bg-secondary {
            background-color: var(--secondary-color) !important;
        }

        .text-secondary {
            color: var(--secondary-color) !important;
        }

        /* Accent Color Elements */
        .accent-bg,
        .bg-accent {
            background-color: var(--accent-color) !important;
        }

        .text-accent {
            color: var(--accent-color) !important;
        }

        /* Service Cards and Sections */
        .service-card:hover,
        .card:hover {
            border-color: var(--primary-color);
        }

        .service-icon,
        .feature-icon {
            color: var(--primary-color);
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        }

        /* Contact Form */
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
        }

        /* Navigation Active States */
        .navbar-nav .nav-link.active,
        .navbar-nav .nav-link:focus {
            color: var(--primary-color) !important;
        }

        /* Testimonials */
        .testimonial-card {
            border-left: 4px solid var(--accent-color);
        }

        /* Call-to-Action Sections */
        .cta-section {
            background-color: var(--secondary-color);
            color: white;
        }

        /* Progress Bars and Indicators */
        .progress-bar {
            background-color: var(--primary-color);
        }

        /* Badges and Tags */
        .badge-primary {
            background-color: var(--primary-color);
        }

        .badge-secondary {
            background-color: var(--secondary-color);
        }

        /* Mobile Menu */
        .navbar-toggler {
            border-color: var(--header-text-color);
        }

        .navbar-toggler-icon {
            background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e\");
        }
    </style>";

    return $css;
}

/**
 * Get a specific color from the color scheme
 */
function getSchemeColor($colorKey, $default = '#000000') {
    global $db;

    $colorSettings = $db->fetchOne("SELECT setting_value FROM site_settings WHERE setting_key = 'branding_colors'");

    if ($colorSettings && isset($colorSettings['setting_value'])) {
        $colors = json_decode($colorSettings['setting_value'], true);
        if ($colors && isset($colors[$colorKey])) {
            return $colors[$colorKey];
        }
    }

    return $default;
}
?>
