<?php
/**
 * Batch Update Script for Admin Layout
 * Updates all admin pages to use the new sidebar and header includes
 */

require_once '../config/config.php';

// List of admin pages to update
$adminPages = [
    'content.php',
    'inquiries.php',
    'testimonials.php',
    'users.php',
    'branding.php',
    'email-test.php',
    'settings.php',
    'profile.php'
];

function updateAdminPageLayout($filename) {
    if (!file_exists($filename)) {
        return "❌ File not found";
    }
    
    $content = file_get_contents($filename);
    $originalContent = $content;
    
    // Check if already updated
    if (strpos($content, "include 'includes/sidebar.php'") !== false) {
        return "✅ Already updated";
    }
    
    // Pattern 1: Standard admin-wrapper structure
    $pattern1 = '/(<div class="admin-wrapper">)\s*<!-- Sidebar -->\s*<nav class="admin-sidebar">.*?<\/nav>\s*<!-- Main Content -->\s*<div class="admin-main">\s*<!-- Header -->\s*<header class="admin-header">.*?<\/header>\s*<!-- Content -->\s*<main class="admin-content">/s';
    
    // Pattern 2: Alternative structure
    $pattern2 = '/(<div class="admin-wrapper">)\s*<nav class="admin-sidebar">.*?<\/nav>\s*<div class="admin-main">\s*<header class="admin-header">.*?<\/header>\s*<main class="admin-content">/s';
    
    // Pattern 3: Another variation
    $pattern3 = '/(<div class="admin-wrapper">)\s*<nav class="admin-sidebar">.*?<\/nav>\s*<main class="admin-main">\s*<header class="admin-header">.*?<\/header>\s*<div class="admin-content">/s';
    
    $replacement = '<div class="admin-container">
        <?php include \'includes/sidebar.php\'; ?>
        
        <main class="admin-main">
            <?php include \'includes/header.php\'; ?>
            
            <div class="admin-content">';
    
    $patterns = [$pattern1, $pattern2, $pattern3];
    $updated = false;
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $updated = true;
            break;
        }
    }
    
    if (!$updated) {
        // Try a more flexible approach
        $flexPattern = '/(<div class="admin-wrapper">).*?(<main class="admin-content">|<div class="admin-content">)/s';
        if (preg_match($flexPattern, $content)) {
            $content = preg_replace($flexPattern, $replacement, $content);
            $updated = true;
        }
    }
    
    if ($updated) {
        // Fix closing tags - multiple variations
        $closingPatterns = [
            '</main>\s*</div>\s*</div>',
            '</div>\s*</main>\s*</div>',
            '</main>\s*</main>\s*</div>'
        ];
        
        $correctClosing = '</div>
        </main>
    </div>';
        
        foreach ($closingPatterns as $closingPattern) {
            $content = preg_replace('/' . $closingPattern . '/', $correctClosing, $content);
        }
        
        // Backup original file
        file_put_contents($filename . '.backup', $originalContent);
        
        // Write updated content
        file_put_contents($filename, $content);
        return "✅ Updated successfully (backup created)";
    }
    
    return "⚠️ No matching pattern found";
}

echo "<!DOCTYPE html>\n<html>\n<head>\n<title>Batch Admin Layout Update</title>\n";
echo "<style>body{font-family:Arial,sans-serif;margin:40px;} .success{color:#27ae60;} .warning{color:#f39c12;} .error{color:#e74c3c;}</style>\n";
echo "</head>\n<body>\n";

echo "<h1>🔧 Batch Admin Layout Update</h1>\n";
echo "<p>Updating all admin pages to use the new sidebar and header includes...</p>\n";

$results = [];
foreach ($adminPages as $page) {
    $result = updateAdminPageLayout($page);
    $results[$page] = $result;
    
    $class = '';
    if (strpos($result, '✅') !== false) $class = 'success';
    elseif (strpos($result, '⚠️') !== false) $class = 'warning';
    elseif (strpos($result, '❌') !== false) $class = 'error';
    
    echo "<p class='$class'><strong>$page:</strong> $result</p>\n";
}

echo "<h2>📋 Summary</h2>\n";
$successful = count(array_filter($results, function($r) { return strpos($r, '✅') !== false; }));
$total = count($results);

echo "<p><strong>Successfully updated:</strong> $successful out of $total pages</p>\n";

if ($successful > 0) {
    echo "<h3>✅ Benefits of the Update:</h3>\n";
    echo "<ul>\n";
    echo "<li>🎯 <strong>Consistent Navigation:</strong> All menu items visible on every page</li>\n";
    echo "<li>🎯 <strong>Enhanced Features:</strong> Analytics, SEO, Users, Testimonials menus</li>\n";
    echo "<li>🎯 <strong>Role-based Access:</strong> Admin vs Editor permissions</li>\n";
    echo "<li>🎯 <strong>Quick Actions:</strong> View Website, Mobile App, Notifications</li>\n";
    echo "<li>🎯 <strong>Better UX:</strong> Professional sidebar with user info</li>\n";
    echo "<li>🎯 <strong>Easy Maintenance:</strong> Centralized sidebar and header</li>\n";
    echo "</ul>\n";
}

echo "<h3>🔄 What Was Updated:</h3>\n";
echo "<ul>\n";
echo "<li>Replaced hardcoded sidebar with <code>includes/sidebar.php</code></li>\n";
echo "<li>Replaced hardcoded header with <code>includes/header.php</code></li>\n";
echo "<li>Changed <code>admin-wrapper</code> to <code>admin-container</code></li>\n";
echo "<li>Fixed HTML structure and closing tags</li>\n";
echo "<li>Created backup files (.backup extension)</li>\n";
echo "</ul>\n";

echo "<p><a href='index.php'>← Back to Admin Dashboard</a></p>\n";
echo "</body>\n</html>";
?>
