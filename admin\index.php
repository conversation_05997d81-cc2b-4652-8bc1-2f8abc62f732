<?php
/**
 * Admin Panel for Flori Construction Ltd Website
 * Simple web-based admin interface
 */

require_once '../config/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$user = getCurrentUser();
if (!$user) {
    header('Location: login.php');
    exit;
}

// Get enhanced dashboard statistics with real data
try {
    // Projects statistics
    $totalProjects = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1")['count'];
    $completedProjects = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1 AND project_type = 'completed'")['count'];
    $ongoingProjects = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1 AND project_type = 'ongoing'")['count'];
    $pendingProjects = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1 AND project_type = 'pending'")['count'];

    // Media statistics
    $totalMedia = $db->fetchOne("SELECT COUNT(*) as count FROM media WHERE is_active = 1")['count'];
    $totalMediaSize = $db->fetchOne("SELECT COALESCE(SUM(file_size), 0) as size FROM media WHERE is_active = 1")['size'];

    // Inquiry statistics
    $totalInquiries = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries")['count'];
    $newInquiries = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'];
    $pendingInquiries = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status IN ('new', 'pending')")['count'];
    $todayInquiries = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE DATE(created_at) = CURDATE()")['count'];
    $weekInquiries = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE WEEK(created_at) = WEEK(NOW())")['count'];

    // Revenue calculation (estimated based on projects)
    $totalRevenue = ($completedProjects * 75000) + ($ongoingProjects * 50000);

    // Monthly statistics
    $thisMonthProjects = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1 AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())")['count'];
    $thisMonthCompleted = $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE is_active = 1 AND project_type = 'completed' AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())")['count'];

    // Success rate calculation
    $successRate = $totalProjects > 0 ? round(($completedProjects / $totalProjects) * 100) : 0;

    // Get recent projects with enhanced data
    $recentProjects = $db->fetchAll("
        SELECT
            p.*,
            CASE
                WHEN p.project_type = 'completed' THEN 'Completed'
                WHEN p.project_type = 'ongoing' THEN 'In Progress'
                WHEN p.project_type = 'pending' THEN 'Pending'
                ELSE 'Unknown'
            END as status_display,
            DATE_FORMAT(p.created_at, '%M %d, %Y') as formatted_date,
            TIMESTAMPDIFF(DAY, p.created_at, NOW()) as days_ago
        FROM projects p
        WHERE p.is_active = 1
        ORDER BY p.created_at DESC
        LIMIT 5
    ");

    // Get recent contact inquiries with enhanced data
    $recentInquiries = $db->fetchAll("
        SELECT
            i.*,
            CASE
                WHEN i.status = 'new' THEN 'New'
                WHEN i.status = 'contacted' THEN 'Contacted'
                WHEN i.status = 'quoted' THEN 'Quoted'
                WHEN i.status = 'closed' THEN 'Closed'
                ELSE 'Pending'
            END as status_display,
            DATE_FORMAT(i.created_at, '%M %d, %Y') as formatted_date,
            TIMESTAMPDIFF(DAY, i.created_at, NOW()) as days_ago
        FROM contact_inquiries i
        ORDER BY i.created_at DESC
        LIMIT 5
    ");

    // Get recent media files
    $recentMedia = $db->fetchAll("
        SELECT
            m.*,
            DATE_FORMAT(m.created_at, '%M %d, %Y') as formatted_date,
            ROUND(m.file_size / 1024 / 1024, 2) as size_mb
        FROM media m
        WHERE m.is_active = 1
        ORDER BY m.created_at DESC
        LIMIT 10
    ");

    // Performance metrics
    $avgCompletionDays = 30; // Default value, can be calculated from actual data

} catch (Exception $e) {
    error_log("Dashboard query error: " . $e->getMessage());
    // Set default values if database queries fail
    $totalProjects = 0;
    $completedProjects = 0;
    $ongoingProjects = 0;
    $pendingProjects = 0;
    $totalMedia = 0;
    $totalMediaSize = 0;
    $totalInquiries = 0;
    $newInquiries = 0;
    $pendingInquiries = 0;
    $todayInquiries = 0;
    $weekInquiries = 0;
    $totalRevenue = 0;
    $thisMonthProjects = 0;
    $thisMonthCompleted = 0;
    $successRate = 0;
    $avgCompletionDays = 30;
    $recentProjects = [];
    $recentInquiries = [];
    $recentMedia = [];
}

// Helper functions

function getStatusBadgeClass($status) {
    switch (strtolower($status)) {
        case 'completed':
            return 'completed';
        case 'ongoing':
        case 'in progress':
            return 'ongoing';
        case 'pending':
            return 'pending';
        case 'new':
            return 'new';
        case 'contacted':
            return 'contacted';
        case 'quoted':
            return 'quoted';
        case 'closed':
            return 'closed';
        default:
            return 'default';
    }
}

function timeAgo($days) {
    if ($days == 0) {
        return 'Today';
    } elseif ($days == 1) {
        return 'Yesterday';
    } elseif ($days < 7) {
        return $days . ' days ago';
    } elseif ($days < 30) {
        return ceil($days / 7) . ' weeks ago';
    } else {
        return ceil($days / 30) . ' months ago';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?= SITE_NAME ?></title>

    <!-- CSS -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-container">
        <?php include 'includes/sidebar.php'; ?>

        <main class="admin-main">
            <?php include 'includes/header.php'; ?>

            <div class="admin-content">
                <!-- Enhanced Dashboard Header -->
                <div class="dashboard-header">
                    <div class="dashboard-title-section">
                        <div class="dashboard-title">
                            <h1>Dashboard</h1>
                            <div class="dashboard-subtitle">
                                <span class="welcome-text">Welcome back, <?= htmlspecialchars($user['full_name']) ?>! 👋</span>
                                <p>Take a look at your construction business overview and manage your projects efficiently.</p>
                            </div>
                        </div>
                        <div class="dashboard-stats-summary">
                            <div class="stat-summary-item">
                                <span class="stat-number"><?= $totalProjects ?></span>
                                <span class="stat-label">Total Projects</span>
                            </div>
                            <div class="stat-summary-item">
                                <span class="stat-number"><?= $ongoingProjects ?></span>
                                <span class="stat-label">Active</span>
                            </div>
                            <div class="stat-summary-item">
                                <span class="stat-number"><?= $completedProjects ?></span>
                                <span class="stat-label">Completed</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-actions">
                       

                    </div>
                </div>

                <!-- Dashboard Quick Stats Bar -->
                <div class="quick-stats-bar">
                    <div class="quick-stat-item">
                        <div class="quick-stat-icon revenue">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="quick-stat-content">
                            <span class="quick-stat-value">$<?= number_format($totalRevenue, 0) ?></span>
                            <span class="quick-stat-label">Total Revenue</span>
                            <span class="quick-stat-change positive">
                                <i class="fas fa-arrow-up"></i> +12.5%
                            </span>
                        </div>
                    </div>

                    <div class="quick-stat-item">
                        <div class="quick-stat-icon inquiries">
                            <i class="fas fa-envelope-open"></i>
                        </div>
                        <div class="quick-stat-content">
                            <span class="quick-stat-value"><?= $newInquiries ?></span>
                            <span class="quick-stat-label">New Inquiries</span>
                            <span class="quick-stat-change <?= $newInquiries > 0 ? 'positive' : 'neutral' ?>">
                                <i class="fas fa-<?= $newInquiries > 0 ? 'arrow-up' : 'minus' ?>"></i>
                                <?= $newInquiries > 0 ? 'New today' : 'No change' ?>
                            </span>
                        </div>
                    </div>

                    <div class="quick-stat-item">
                        <div class="quick-stat-icon media">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="quick-stat-content">
                            <span class="quick-stat-value"><?= $totalMedia ?></span>
                            <span class="quick-stat-label">Media Files</span>
                            <span class="quick-stat-change neutral">
                                <i class="fas fa-camera"></i> Gallery
                            </span>
                        </div>
                    </div>

                    <div class="quick-stat-item">
                        <div class="quick-stat-icon performance">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="quick-stat-content">
                            <span class="quick-stat-value"><?= $totalProjects > 0 ? round(($completedProjects / $totalProjects) * 100) : 0 ?>%</span>
                            <span class="quick-stat-label">Success Rate</span>
                            <span class="quick-stat-change positive">
                                <i class="fas fa-trophy"></i> Excellent
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Overview Cards Section -->
                <div class="overview-section">
                    <!-- Projects Overview Card -->
                    <div class="overview-card projects-card">
                        <div class="overview-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="overview-content">
                            <div class="overview-number"><?= $totalProjects ?></div>
                            <div class="overview-label">Total Projects</div>
                            <div class="overview-action">
                                <a href="projects.php" class="view-all-link">
                                    View All Projects
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Inquiries Overview Card -->
                    <div class="overview-card inquiries-card">
                        <div class="overview-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="overview-content">
                            <div class="overview-number"><?= $totalInquiries ?></div>
                            <div class="overview-label">Total Inquiries</div>
                            <div class="overview-action">
                                <a href="inquiries.php" class="view-all-link">
                                    View All Inquiries
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Status Overview -->
                <div class="status-overview-section">
                    <div class="status-overview-card">
                        <h3>Project Status Overview</h3>
                        <div class="status-content">
                            <!-- Project Status Chart -->
                            <div class="status-chart">
                                <div class="chart-container">
                                    <div class="donut-chart">
                                        <svg width="120" height="120" viewBox="0 0 120 120">
                                            <circle cx="60" cy="60" r="50" fill="none" stroke="#f1f5f9" stroke-width="10"/>
                                            <circle cx="60" cy="60" r="50" fill="none" stroke="#4f46e5" stroke-width="10"
                                                    stroke-dasharray="<?= $totalProjects > 0 ? ($completedProjects / $totalProjects) * 314 : 0 ?> 314"
                                                    stroke-dashoffset="0" transform="rotate(-90 60 60)"/>
                                        </svg>
                                        <div class="chart-center">
                                            <span class="chart-percentage"><?= $totalProjects > 0 ? round(($completedProjects / $totalProjects) * 100) : 0 ?>%</span>
                                            <span class="chart-label">Completed</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="status-legend">
                                    <div class="legend-item">
                                        <span class="legend-color completed"></span>
                                        <span class="legend-text">Completed (<?= $completedProjects ?>)</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color ongoing"></span>
                                        <span class="legend-text">Ongoing (<?= $ongoingProjects ?>)</span>
                                    </div>
                                    <div class="legend-item">
                                        <span class="legend-color pending"></span>
                                        <span class="legend-text">Pending (<?= $pendingProjects ?>)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Upcoming Interview/Schedule -->
                    <div class="upcoming-card">
                        <div class="upcoming-header">
                            <h3>Upcoming Schedule</h3>
                            <div class="schedule-nav">
                                <button class="nav-btn prev">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <button class="nav-btn next">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="upcoming-content">
                            <div class="upcoming-date">
                                <span class="date-label">Today</span>
                                <span class="date-value"><?= date('M d, Y') ?></span>
                            </div>
                            <div class="upcoming-item">
                                <div class="upcoming-avatar">
                                    <img src="<?= ASSETS_URL ?>/images/default-avatar.svg" alt="Project Manager">
                                </div>
                                <div class="upcoming-details">
                                    <div class="upcoming-title">Project Review Meeting</div>
                                    <div class="upcoming-subtitle">Site Inspection & Progress Review</div>
                                </div>
                                <div class="upcoming-time">10:00 - 11:00</div>
                            </div>
                            <div class="upcoming-action">
                                <a href="analytics.php" class="view-schedule-link">
                                    View Schedule
                                    <i class="fas fa-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity Section -->
                <div class="activity-section">
                    <!-- Recent Projects History -->
                    <div class="activity-card">
                        <div class="activity-header">
                            <h3>Recent Projects History</h3>
                            <a href="projects.php" class="view-all-btn">
                                View All Projects History
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        <div class="activity-content">
                            <div class="activity-date-label">TODAY</div>
                            <?php if (empty($recentProjects)): ?>
                                <div class="no-activity">
                                    <i class="fas fa-inbox"></i>
                                    <p>No recent projects</p>
                                </div>
                            <?php else: ?>
                                <div class="activity-list">
                                    <?php foreach (array_slice($recentProjects, 0, 3) as $project): ?>
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <i class="fas fa-building"></i>
                                        </div>
                                        <div class="activity-details">
                                            <div class="activity-title"><?= htmlspecialchars($project['title']) ?></div>
                                            <div class="activity-meta">
                                                <span class="activity-company"><?= htmlspecialchars($project['location'] ?: 'Flori Construction') ?></span>
                                                <span class="activity-type"><?= $project['status_display'] ?? ucfirst($project['project_type']) ?></span>
                                                <span class="activity-location"><?= isset($project['days_ago']) ? timeAgo($project['days_ago']) : ($project['formatted_date'] ?? 'Recent') ?></span>
                                            </div>
                                        </div>
                                        <div class="activity-status">
                                            <span class="status-badge <?= getStatusBadgeClass($project['project_type']) ?>">
                                                <?= $project['status_display'] ?? ucfirst($project['project_type']) ?>
                                            </span>
                                            <button class="activity-menu">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Saved Jobs (Recent Inquiries) -->
                    <div class="saved-jobs-card">
                        <div class="saved-jobs-header">
                            <h3>Recent Inquiries</h3>
                            <a href="inquiries.php" class="view-all-btn">
                                View All Inquiries
                                <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                        <div class="saved-jobs-content">
                            <?php if (empty($recentInquiries)): ?>
                                <div class="no-saved-jobs">
                                    <i class="fas fa-bookmark"></i>
                                    <p>No recent inquiries</p>
                                </div>
                            <?php else: ?>
                                <div class="saved-jobs-list">
                                    <?php foreach (array_slice($recentInquiries, 0, 4) as $inquiry): ?>
                                    <div class="saved-job-item">
                                        <div class="job-icon">
                                            <i class="fas fa-envelope"></i>
                                        </div>
                                        <div class="job-details">
                                            <div class="job-title"><?= htmlspecialchars($inquiry['name'] ?? 'Unknown Client') ?></div>
                                            <div class="job-company"><?= htmlspecialchars($inquiry['subject'] ?: 'General Inquiry') ?></div>
                                            <div class="job-meta">
                                                <span class="job-type"><?= $inquiry['status_display'] ?? ucfirst($inquiry['status']) ?></span>
                                                <span class="job-location"><?= isset($inquiry['days_ago']) ? timeAgo($inquiry['days_ago']) : ($inquiry['formatted_date'] ?? 'Recent') ?></span>
                                                <span class="job-email"><?= htmlspecialchars($inquiry['email'] ?? '') ?></span>
                                            </div>
                                        </div>
                                        <div class="job-actions">
                                            <span class="days-to-apply">
                                                <i class="fas fa-clock"></i>
                                                <?= $inquiry['status_display'] ?? ucfirst($inquiry['status']) ?>
                                            </span>
                                            <button class="job-menu">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>


            </div>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/admin.js"></script>
</body>
</html>
