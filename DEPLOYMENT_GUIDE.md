# 🚀 Flori Construction Ltd - Mobile PWA Deployment Guide

This guide provides step-by-step instructions for deploying and testing the Progressive Web App (PWA) mobile solution.

**⚠️ IMPORTANT NOTE**: The native Android app has been removed from this project. The PWA provides complete mobile functionality.

**📱 For PWA deployment instructions, see the updated sections below. Android-specific sections are now outdated.**

## 📋 Prerequisites

### Development Environment

- **Web Browser**: Modern browser with PWA support
- **Text Editor**: VS Code, Sublime Text, or similar
- **Local Server**: XAMPP, WAMP, or similar for testing

### Server Requirements

- **PHP**: 7.4+ with MySQL support
- **MySQL**: 5.7+ or MariaDB 10.2+
- **Web Server**: Apache or Nginx
- **SSL Certificate**: Required for PWA features and production

## 🛠️ PWA Deployment Process

### 1. Setup Development Environment

```bash
# Navigate to your web server directory
cd /path/to/your/webserver/htdocs

# Ensure mobile-app directory exists with all PWA files
ls mobile-app/
# Should contain: index.html, manifest.json, sw.js, css/, js/, icons/
```

### 2. Configure API Endpoints

Edit `app/build.gradle` to set the correct API URLs:

```gradle
buildTypes {
    debug {
        buildConfigField "String", "API_BASE_URL", "\"http://your-domain.com/api/\""
        buildConfigField "String", "WEB_BASE_URL", "\"http://your-domain.com/\""
    }
    release {
        buildConfigField "String", "API_BASE_URL", "\"https://your-domain.com/api/\""
        buildConfigField "String", "WEB_BASE_URL", "\"https://your-domain.com/\""
    }
}
```

### 3. Build the Application

#### Option A: Using Android Studio

1. Open Android Studio
2. Open the `android-app` project
3. Wait for Gradle sync to complete
4. Build → Generate Signed Bundle/APK
5. Choose APK and follow the signing wizard

#### Option B: Using Command Line

```bash
# Make build script executable
chmod +x build.sh

# Run the build script
./build.sh
```

#### Option C: Manual Gradle Build

```bash
# Clean previous builds
./gradlew clean

# Build debug APK
./gradlew assembleDebug

# Build release APK (requires signing)
./gradlew assembleRelease
```

## 🧪 Testing

### 1. API Testing

First, verify that all API endpoints are working:

```bash
# Run the API test script
php test_api.php
```

Expected results:

- ✅ Authentication: PASS
- ✅ Dashboard Data: PASS
- ✅ Projects List: PASS
- ✅ Media List: PASS
- ✅ Create Project: PASS

### 2. Device Testing

#### Install on Physical Device

```bash
# Enable USB debugging on your Android device
# Connect device via USB

# Install the APK
adb install app/build/outputs/apk/debug/app-debug.apk

# Or for release builds
adb install app/build/outputs/apk/release/app-release.apk
```

#### Install on Emulator

```bash
# Start Android emulator
emulator -avd YourAVDName

# Install APK
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 3. Testing Checklist

#### Authentication

- [ ] Login with valid admin credentials
- [ ] Login fails with invalid credentials
- [ ] Token verification works
- [ ] Auto-logout on token expiry

#### Project Management

- [ ] View projects list
- [ ] Search and filter projects
- [ ] Create new project
- [ ] Edit existing project
- [ ] Delete project
- [ ] Toggle featured status

#### Media Management

- [ ] View media gallery
- [ ] Upload photos from camera
- [ ] Upload photos from gallery
- [ ] View upload progress
- [ ] Edit media metadata
- [ ] Delete media files

#### Offline Functionality

- [ ] App works without internet
- [ ] Data syncs when connection restored
- [ ] Offline indicator shows correctly
- [ ] Local changes preserved

#### Performance

- [ ] App launches quickly
- [ ] Smooth scrolling in lists
- [ ] Image loading is efficient
- [ ] No memory leaks during usage

## 🌐 Server Deployment

### 1. Upload API Files

Upload the following files to your web server:

```
/api/
├── auth.php
├── mobile.php
└── test_api.php

/config/
└── config.php (ensure database credentials are correct)
```

### 2. Database Setup

Ensure your database has the required tables:

```sql
-- Users table (should already exist)
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'editor') DEFAULT 'editor',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- API tokens table
CREATE TABLE IF NOT EXISTS api_tokens (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (token),
    INDEX idx_expires (expires_at)
);
```

### 3. File Permissions

Set correct permissions for upload directories:

```bash
# Make upload directories writable
chmod 755 uploads/
chmod 755 uploads/projects/
chmod 755 uploads/media/

# Ensure web server can write to these directories
chown -R www-data:www-data uploads/
```

### 4. SSL Configuration

For production, ensure HTTPS is enabled:

```apache
# Apache .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## 📱 App Distribution

### 1. Internal Distribution

#### Option A: Direct APK Distribution

1. Build signed release APK
2. Upload to secure file sharing service
3. Share download link with admin team
4. Provide installation instructions

#### Option B: Firebase App Distribution

1. Setup Firebase project
2. Add app to Firebase
3. Upload APK to Firebase App Distribution
4. Invite testers via email

### 2. Google Play Store (Internal Testing)

For larger teams, consider Google Play Console internal testing:

1. Create Google Play Console account
2. Upload signed AAB (Android App Bundle)
3. Setup internal testing track
4. Add team members as internal testers

### 3. Enterprise Distribution

For enterprise deployment:

1. Sign APK with enterprise certificate
2. Deploy via Mobile Device Management (MDM)
3. Configure app policies and restrictions

## 🔧 Configuration

### 1. App Configuration

Key configuration options in the app:

```kotlin
// Constants.kt
const val API_TIMEOUT = 30L // seconds
const val UPLOAD_TIMEOUT = 120L // seconds
const val MAX_IMAGE_SIZE = 5 * 1024 * 1024 // 5MB
const val DEFAULT_PAGE_SIZE = 20
const val SYNC_INTERVAL_15_MIN = 15
```

### 2. Server Configuration

Key server settings:

```php
// config.php
define('MAX_UPLOAD_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'webp']);
define('TOKEN_EXPIRY_DAYS', 30);
```

## 🚨 Troubleshooting

### Common Issues

#### Build Errors

```bash
# Clear Gradle cache
./gradlew clean
rm -rf ~/.gradle/caches/

# Update dependencies
./gradlew dependencies --refresh-dependencies
```

#### API Connection Issues

1. Check API URL configuration
2. Verify server is accessible
3. Check SSL certificate validity
4. Test API endpoints manually

#### Upload Failures

1. Check file size limits
2. Verify upload directory permissions
3. Check server PHP upload limits
4. Test with smaller files

#### Authentication Problems

1. Verify admin credentials
2. Check database connection
3. Ensure api_tokens table exists
4. Check token expiry settings

### Debug Mode

Enable debug logging in the app:

```kotlin
// In BuildConfig
if (BuildConfig.DEBUG) {
    // Enable detailed logging
    Log.d("FloriAdmin", "Debug message")
}
```

## 📊 Monitoring

### 1. Server Monitoring

Monitor API usage and errors:

```php
// Add to API endpoints
error_log("API Request: " . $_SERVER['REQUEST_URI']);
error_log("User: " . $user['username']);
```

### 2. App Analytics

Consider adding analytics for usage tracking:

- Firebase Analytics
- Custom event tracking
- Crash reporting

## 🔄 Updates

### App Updates

1. Increment version in `build.gradle`
2. Build new APK/AAB
3. Test thoroughly
4. Distribute to users
5. Monitor for issues

### API Updates

1. Test changes with existing app versions
2. Maintain backward compatibility
3. Update API version if needed
4. Document changes

## 📞 Support

For technical support:

- **Email**: <<EMAIL>>
- **Documentation**: Check README.md files
- **Issues**: Create GitHub issues for bugs
- **Updates**: Monitor repository for updates

---

**© 2024 Flori Construction Ltd. All rights reserved.**
