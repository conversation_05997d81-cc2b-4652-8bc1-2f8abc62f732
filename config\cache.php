<?php
/**
 * Simple File-based Caching System
 * Flori Construction Ltd
 */

class Cache {
    private $cacheDir;
    private $defaultTTL;
    
    public function __construct($cacheDir = null, $defaultTTL = 3600) {
        $this->cacheDir = $cacheDir ?: ROOT_PATH . '/cache';
        $this->defaultTTL = $defaultTTL;
        $this->ensureCacheDir();
    }
    
    private function ensureCacheDir() {
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create .htaccess to protect cache directory
        $htaccessFile = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    private function getCacheFile($key) {
        $hash = md5($key);
        $subdir = $this->cacheDir . '/' . substr($hash, 0, 2);
        
        if (!is_dir($subdir)) {
            mkdir($subdir, 0755, true);
        }
        
        return $subdir . '/' . $hash . '.cache';
    }
    
    public function get($key, $default = null) {
        $file = $this->getCacheFile($key);
        
        if (!file_exists($file)) {
            return $default;
        }
        
        $data = file_get_contents($file);
        $cache = unserialize($data);
        
        if (!$cache || !isset($cache['expires']) || $cache['expires'] < time()) {
            $this->delete($key);
            return $default;
        }
        
        return $cache['data'];
    }
    
    public function set($key, $data, $ttl = null) {
        $ttl = $ttl ?: $this->defaultTTL;
        $file = $this->getCacheFile($key);
        
        $cache = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($file, serialize($cache)) !== false;
    }
    
    public function delete($key) {
        $file = $this->getCacheFile($key);
        
        if (file_exists($file)) {
            return unlink($file);
        }
        
        return true;
    }
    
    public function clear() {
        $this->clearDirectory($this->cacheDir);
        return true;
    }
    
    private function clearDirectory($dir) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_dir($file)) {
                $this->clearDirectory($file);
                rmdir($file);
            } else {
                unlink($file);
            }
        }
    }
    
    public function remember($key, $callback, $ttl = null) {
        $data = $this->get($key);
        
        if ($data === null) {
            $data = $callback();
            $this->set($key, $data, $ttl);
        }
        
        return $data;
    }
    
    public function getStats() {
        $stats = [
            'total_files' => 0,
            'total_size' => 0,
            'expired_files' => 0
        ];
        
        $this->scanDirectory($this->cacheDir, $stats);
        
        return $stats;
    }
    
    private function scanDirectory($dir, &$stats) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_dir($file)) {
                $this->scanDirectory($file, $stats);
            } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'cache') {
                $stats['total_files']++;
                $stats['total_size'] += filesize($file);
                
                // Check if expired
                $data = file_get_contents($file);
                $cache = unserialize($data);
                if ($cache && isset($cache['expires']) && $cache['expires'] < time()) {
                    $stats['expired_files']++;
                }
            }
        }
    }
    
    public function cleanup() {
        $cleaned = 0;
        $this->cleanupDirectory($this->cacheDir, $cleaned);
        return $cleaned;
    }
    
    private function cleanupDirectory($dir, &$cleaned) {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_dir($file)) {
                $this->cleanupDirectory($file, $cleaned);
                // Remove empty directories
                if (count(glob($file . '/*')) === 0) {
                    rmdir($file);
                }
            } elseif (pathinfo($file, PATHINFO_EXTENSION) === 'cache') {
                $data = file_get_contents($file);
                $cache = unserialize($data);
                if ($cache && isset($cache['expires']) && $cache['expires'] < time()) {
                    unlink($file);
                    $cleaned++;
                }
            }
        }
    }
}

/**
 * Image Optimization Class
 */
class ImageOptimizer {
    private $quality = 85;
    private $maxWidth = 1920;
    private $maxHeight = 1080;
    
    public function __construct($quality = 85, $maxWidth = 1920, $maxHeight = 1080) {
        $this->quality = $quality;
        $this->maxWidth = $maxWidth;
        $this->maxHeight = $maxHeight;
    }
    
    public function optimize($sourcePath, $destinationPath = null) {
        $destinationPath = $destinationPath ?: $sourcePath;
        
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            throw new Exception('Invalid image file');
        }
        
        $mimeType = $imageInfo['mime'];
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        // Create image resource
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($sourcePath);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($sourcePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($sourcePath);
                break;
            default:
                throw new Exception('Unsupported image type: ' . $mimeType);
        }
        
        if (!$image) {
            throw new Exception('Failed to create image resource');
        }
        
        // Calculate new dimensions
        $newDimensions = $this->calculateDimensions($width, $height);
        $newWidth = $newDimensions['width'];
        $newHeight = $newDimensions['height'];
        
        // Create optimized image
        $optimized = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($optimized, false);
            imagesavealpha($optimized, true);
            $transparent = imagecolorallocatealpha($optimized, 255, 255, 255, 127);
            imagefill($optimized, 0, 0, $transparent);
        }
        
        // Resize image
        imagecopyresampled($optimized, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        // Save optimized image
        switch ($mimeType) {
            case 'image/jpeg':
                imagejpeg($optimized, $destinationPath, $this->quality);
                break;
            case 'image/png':
                imagepng($optimized, $destinationPath, 9);
                break;
            case 'image/gif':
                imagegif($optimized, $destinationPath);
                break;
            case 'image/webp':
                imagewebp($optimized, $destinationPath, $this->quality);
                break;
        }
        
        // Clean up
        imagedestroy($image);
        imagedestroy($optimized);
        
        return [
            'original_size' => filesize($sourcePath),
            'optimized_size' => filesize($destinationPath),
            'original_dimensions' => ['width' => $width, 'height' => $height],
            'optimized_dimensions' => ['width' => $newWidth, 'height' => $newHeight],
            'compression_ratio' => round((1 - filesize($destinationPath) / filesize($sourcePath)) * 100, 2)
        ];
    }
    
    private function calculateDimensions($width, $height) {
        $ratio = min($this->maxWidth / $width, $this->maxHeight / $height);
        
        if ($ratio >= 1) {
            return ['width' => $width, 'height' => $height];
        }
        
        return [
            'width' => round($width * $ratio),
            'height' => round($height * $ratio)
        ];
    }
    
    public function createThumbnail($sourcePath, $destinationPath, $thumbWidth = 300, $thumbHeight = 200) {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) {
            throw new Exception('Invalid image file');
        }
        
        $mimeType = $imageInfo['mime'];
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        // Create image resource
        switch ($mimeType) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($sourcePath);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($sourcePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($sourcePath);
                break;
            default:
                throw new Exception('Unsupported image type: ' . $mimeType);
        }
        
        // Calculate crop dimensions
        $sourceRatio = $width / $height;
        $thumbRatio = $thumbWidth / $thumbHeight;
        
        if ($sourceRatio > $thumbRatio) {
            // Source is wider
            $cropHeight = $height;
            $cropWidth = $height * $thumbRatio;
            $cropX = ($width - $cropWidth) / 2;
            $cropY = 0;
        } else {
            // Source is taller
            $cropWidth = $width;
            $cropHeight = $width / $thumbRatio;
            $cropX = 0;
            $cropY = ($height - $cropHeight) / 2;
        }
        
        // Create thumbnail
        $thumbnail = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // Preserve transparency
        if ($mimeType === 'image/png' || $mimeType === 'image/gif') {
            imagealphablending($thumbnail, false);
            imagesavealpha($thumbnail, true);
            $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
            imagefill($thumbnail, 0, 0, $transparent);
        }
        
        // Crop and resize
        imagecopyresampled($thumbnail, $image, 0, 0, $cropX, $cropY, $thumbWidth, $thumbHeight, $cropWidth, $cropHeight);
        
        // Save thumbnail
        imagejpeg($thumbnail, $destinationPath, $this->quality);
        
        // Clean up
        imagedestroy($image);
        imagedestroy($thumbnail);
        
        return true;
    }
}

// Global cache instance
function getCache() {
    static $cache = null;
    
    if ($cache === null) {
        $cache = new Cache();
    }
    
    return $cache;
}

// Global image optimizer instance
function getImageOptimizer() {
    static $optimizer = null;
    
    if ($optimizer === null) {
        $optimizer = new ImageOptimizer();
    }
    
    return $optimizer;
}
?>
