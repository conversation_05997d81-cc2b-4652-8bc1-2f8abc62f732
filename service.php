<?php
require_once 'config/config.php';

// Get service slug from URL
$slug = isset($_GET['slug']) ? sanitize($_GET['slug']) : '';

if (empty($slug)) {
    header('Location: services.php');
    exit;
}

// Get service details
$service = $db->fetchOne("SELECT * FROM services WHERE slug = ? AND is_active = 1", [$slug]);

if (!$service) {
    header('HTTP/1.0 404 Not Found');
    include '404.php';
    exit;
}

// Get related services (same category or random)
$relatedServices = $db->fetchAll(
    "SELECT * FROM services WHERE slug != ? AND is_active = 1 ORDER BY RAND() LIMIT 3",
    [$slug]
);

// Process gallery if exists
$gallery = $service['gallery'] ? json_decode($service['gallery'], true) : [];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($service['meta_title'] ?: $service['title']) ?> - <?= SITE_NAME ?></title>
    <meta name="description" content="<?= htmlspecialchars($service['meta_description'] ?: $service['short_description']) ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?= ASSETS_URL ?>/images/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/style.css">
    <link rel="stylesheet" href="<?= ASSETS_URL ?>/css/responsive.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Oswald:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="top-bar">
            <div class="container">
                <div class="contact-info">
                    <span><i class="fas fa-phone"></i> <?= SITE_PHONE ?></span>
                    <span><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></span>
                </div>
                <div class="social-links">
                    <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                    <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                    <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                </div>
            </div>
        </div>

        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <img src="<?= ASSETS_URL ?>/images/logo.png" alt="<?= SITE_NAME ?>" class="logo">
                </div>

                <ul class="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="about.php">About Us</a></li>
                    <li><a href="services.php" class="active">Our Services</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropdown-toggle">Our Projects <i class="fas fa-chevron-down"></i></a>
                        <ul class="dropdown-menu">
                            <li><a href="projects.php?type=completed">Completed Projects</a></li>
                            <li><a href="projects.php?type=ongoing">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="media.php">Media</a></li>
                    <li><a href="contact.php">Contact Us</a></li>
                </ul>

                <div class="mobile-menu-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1><?= htmlspecialchars($service['title']) ?></h1>
            <nav class="breadcrumb">
                <a href="index.php">Home</a>
                <span>/</span>
                <a href="services.php">Services</a>
                <span>/</span>
                <span><?= htmlspecialchars($service['title']) ?></span>
            </nav>
        </div>
    </section>

    <!-- Service Detail -->
    <section class="service-detail">
        <div class="container">
            <div class="row">
                <div class="col-md-8">
                    <div class="service-content">
                        <?php if ($service['featured_image']): ?>
                        <div class="service-featured-image">
                            <img src="<?= UPLOAD_URL . '/' . $service['featured_image'] ?>"
                                 alt="<?= htmlspecialchars($service['title']) ?>">
                        </div>
                        <?php endif; ?>

                        <div class="service-description">
                            <h2>Service Overview</h2>
                            <p class="lead"><?= htmlspecialchars($service['short_description']) ?></p>

                            <?php if ($service['description']): ?>
                            <div class="service-full-description">
                                <?= nl2br(htmlspecialchars($service['description'])) ?>
                            </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($gallery)): ?>
                        <div class="service-gallery">
                            <h3>Gallery</h3>
                            <div class="gallery-grid">
                                <?php foreach ($gallery as $image): ?>
                                <div class="gallery-item">
                                    <img src="<?= UPLOAD_URL . '/' . $image['path'] ?>"
                                         alt="<?= htmlspecialchars($image['alt'] ?? $service['title']) ?>"
                                         onclick="openLightbox('<?= UPLOAD_URL . '/' . $image['path'] ?>')">
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="service-sidebar">
                        <!-- Service Info -->
                        <div class="sidebar-widget">
                            <h3>Service Information</h3>
                            <div class="service-info">
                                <div class="info-item">
                                    <i class="fas fa-clock"></i>
                                    <span>Available 24/7</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <span>London & Surrounding Areas</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-certificate"></i>
                                    <span>Fully Licensed & Insured</span>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Widget -->
                        <div class="sidebar-widget">
                            <h3>Get a Quote</h3>
                            <p>Interested in this service? Contact us for a free consultation and quote.</p>
                            <div class="contact-buttons">
                                <a href="contact.php?service=<?= urlencode($service['title']) ?>" class="btn btn-primary btn-block">
                                    <i class="fas fa-envelope"></i> Get Quote
                                </a>
                                <a href="tel:<?= SITE_PHONE ?>" class="btn btn-outline btn-block">
                                    <i class="fas fa-phone"></i> Call Now
                                </a>
                            </div>
                        </div>

                        <!-- Related Services -->
                        <?php if (!empty($relatedServices)): ?>
                        <div class="sidebar-widget">
                            <h3>Related Services</h3>
                            <div class="related-services">
                                <?php foreach ($relatedServices as $relatedService): ?>
                                <div class="related-service-item">
                                    <div class="related-service-image">
                                        <img src="<?= $relatedService['featured_image'] ? UPLOAD_URL . '/' . $relatedService['featured_image'] : ASSETS_URL . '/images/service-default.jpg' ?>"
                                             alt="<?= htmlspecialchars($relatedService['title']) ?>">
                                    </div>
                                    <div class="related-service-content">
                                        <h4><a href="service.php?slug=<?= $relatedService['slug'] ?>"><?= htmlspecialchars($relatedService['title']) ?></a></h4>
                                        <p><?= htmlspecialchars(substr($relatedService['short_description'], 0, 80)) ?>...</p>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Get Started?</h2>
                <p>Contact us today to discuss your <?= htmlspecialchars($service['title']) ?> needs and get a personalized quote.</p>
                <div class="cta-buttons">
                    <a href="contact.php?service=<?= urlencode($service['title']) ?>" class="btn btn-primary">Get Quote</a>
                    <a href="projects.php" class="btn btn-outline">View Our Work</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Lightbox Modal -->
    <div id="lightbox" class="lightbox" onclick="closeLightbox()">
        <div class="lightbox-content">
            <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
            <img id="lightbox-image" src="" alt="">
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <img src="<?= ASSETS_URL ?>/images/logo-white.png" alt="<?= SITE_NAME ?>" class="footer-logo">
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="social-links">
                        <a href="<?= FACEBOOK_URL ?>" target="_blank"><i class="fab fa-facebook-f"></i></a>
                        <a href="<?= INSTAGRAM_URL ?>" target="_blank"><i class="fab fa-instagram"></i></a>
                        <a href="<?= YOUTUBE_URL ?>" target="_blank"><i class="fab fa-youtube"></i></a>
                        <a href="<?= LINKEDIN_URL ?>" target="_blank"><i class="fab fa-linkedin-in"></i></a>
                    </div>
                </div>

                <div class="footer-section">
                    <h3>Company</h3>
                    <ul>
                        <li><a href="index.php">Home</a></li>
                        <li><a href="about.php">About Us</a></li>
                        <li><a href="services.php">Our Services</a></li>
                        <li><a href="projects.php">Our Projects</a></li>
                        <li><a href="contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Services</h3>
                    <ul>
                        <li><a href="service.php?slug=civil-engineering">Civil Engineering</a></li>
                        <li><a href="service.php?slug=groundworks">Groundworks</a></li>
                        <li><a href="service.php?slug=rc-frames">RC Frames</a></li>
                        <li><a href="service.php?slug=basements">Basements</a></li>
                        <li><a href="service.php?slug=hard-landscaping">Hard Landscaping</a></li>
                    </ul>
                </div>

                <div class="footer-section">
                    <h3>Contact Us</h3>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> <?= SITE_ADDRESS ?></p>
                        <p><i class="fas fa-phone"></i> <?= SITE_PHONE ?></p>
                        <p><i class="fas fa-mobile-alt"></i> <?= SITE_MOBILE ?></p>
                        <p><i class="fas fa-envelope"></i> <?= SITE_EMAIL ?></p>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?= date('Y') ?> <?= SITE_NAME ?>. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="<?= ASSETS_URL ?>/js/main.js"></script>
    <script>
        // Lightbox functionality
        function openLightbox(imageSrc) {
            document.getElementById('lightbox').style.display = 'flex';
            document.getElementById('lightbox-image').src = imageSrc;
            document.body.style.overflow = 'hidden';
        }

        function closeLightbox() {
            document.getElementById('lightbox').style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // Close lightbox with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLightbox();
            }
        });
    </script>
</body>
</html>
